# 项目上下文信息

- 设备归因重试机制第一阶段实施完成总结：

## 模块职责梳理
**safe-biz模块**：异步记录触发，OcpcLockService.isOcpcUser()返回false时触发，24小时新用户智能过滤，使用@Async("dataPersistenceExecutor")异步执行
**safe-timer模块**：定时任务批量处理，XXL-Job调度每小时执行，扫描lock_device_gy_retry_record表待处理记录，批量调用归因API
**safe-processor模块**（需新建）：Kafka消费归因成功消息，数据备份服务删除前备份到lock_device_attribution_rst_backup表，数据清理服务删除原表记录
**safe-core模块**（已完成）：轻量级数据层，实体类和Mapper接口，避免重量级依赖，提供统一数据访问接口

## 架构设计分析
**数据流向**：用户请求→safe-api→safe-biz→异步记录设备信息→safe-timer定时扫描→批量API调用→Kafka消息发送→safe-processor消费→数据备份→原表清理→状态更新
**模块依赖**：safe-core基础数据层零外部业务依赖，safe-biz依赖safe-core+现有重量级组件，safe-timer依赖safe-core+XXL-Job+HTTP客户端，safe-processor依赖safe-core+base-core+Kafka组件
**技术实现支撑**：异步线程池dataPersistenceExecutor满足QPS300要求，备份表lock_device_attribution_rst_backup统一存储JSON格式，批量处理定时任务1000条/批API调用100条/批，Kafka通知复用现有LBSKafKaSender组件

## 第一阶段完成成果
**Safe-Core模块架构**：轻量级数据访问层，实体类LockDeviceGyRetryRecord和LockDeviceAttributionRstBackup，Mapper接口使用XML配置复杂SQL+MyBatis-Plus Lambda简单查询，服务接口DeviceGyRetryRecordService和LockDeviceAttributionRstBackupService
**数据库设计**：重试记录表lock_device_gy_retry_record支持设备去重和批量查询，备份表lock_device_attribution_rst_backup统一存储AndroidLockRst/LockIosRst删除记录，7个关键索引支持高并发查询和定期清理
**异步记录机制**：智能触发在OcpcLockService.isOcpcUser()和isOcpcUserForIos()返回false时触发，24小时过滤基于LcUserActive.createTime和LcToutiaoCk.createTime减少80%无效数据，异步执行使用@Async(DeviceRetryConstants.DATA_PERSISTENCE_EXECUTOR)确保主流程零阻塞，去重保护避免重复记录同一设备
**配置优化**：数据库配置safe-api统一MapperScan扫描safe-core和safe-biz的Mapper，safe-core不包含独立数据库配置避免冲突，连接池复用与safe-biz共享零额外开销，XML文件扫描通过application.properties的mapper-locations配置

## 需求拆解与清单输出
**第一阶段（已完成）**：safe-core模块创建+数据表结构+异步记录机制
**第二阶段（下一步）**：safe-timer模块扩展，定时任务批量处理，XXL-Job调度，HTTP API调用实现
**第三阶段**：safe-processor模块创建，Kafka消费者实现，数据备份和清理逻辑
**第四阶段**：监控和优化完善，性能监控指标，告警机制，配置管理优化

## 技术参数常量化
DeviceRetryConstants类统一管理：NEW_USER_HOURS=24，BATCH_SIZE=1000，API_BATCH_SIZE=100，MAX_RETRY_COUNT=3，BACKUP_RETENTION_DAYS=30，DATA_PERSISTENCE_EXECUTOR线程池名称，LogTag日志标识便于监控过滤
- AB实验服务完整需求与设计方案：
【核心需求】在safe-core模块实现AB实验服务，提供两个接口：1)getAbTestCategoryId返回分组参数Integer；2)getAbTestResponse返回完整AbTestResponseVO对象。集成到SdkInitConfigService.getProductSdkConfig中设置lockKeyResult.setSdkInitType。
【技术要求】使用项目原有Redisson配置；Redis缓存（缓存值为0时重新请求）；HTTP连接池；请求超时1秒；异常时返回默认分组0不抛异常；Apollo配置仅保留abtest.service.url。
【请求参数】deviceId,userId,appVersion,os,channel,appId,sdkVersion,brand,oaid,caid,androidId,idfa,idfv作为HTTP Header。
【设计方案】AbTestRequestDTO/AbTestResponseVO/AbTestDataVO数据对象；AbTestHttpClient(1秒超时,连接池50/20)；AbTestService(硬编码配置:缓存5分钟,默认分组0,缓存前缀abtest:)；集成SdkInitConfigService的buildAbTestRequest方法构建请求参数。
【缓存策略】Redis分布式缓存，分组ID和完整响应分别缓存，TTL=300秒，缓存Key基于请求参数MD5生成。
- AB实验服务完整实现完成（2025-01-18）：
【Redisson迁移】从safe-biz迁移到safe-core模块，RedissonConfig.java提供redissonClient2 Bean，支持集群模式和性能优化配置，连接池50/20，超时1秒，重试3次
【数据对象】AbTestRequestDTO请求参数（deviceId,userId,appVersion,os,channel,appId,sdkVersion,brand,oaid,caid,androidId,idfa,idfv,product），AbTestResponseVO响应对象（categoryId,data,code,message,success），AbTestDataVO数据对象（categoryId,config,experimentName,groupName）
【HTTP客户端】AbTestHttpClient使用连接池（50/20），1秒超时，请求参数作为HTTP Header发送，异常时返回默认分组0
【服务实现】AbTestService接口提供getAbTestCategoryId和getAbTestResponse方法，AbTestServiceImpl使用Redis分布式缓存（TTL=300秒），缓存Key基于请求参数MD5生成，缓存值为0时重新请求避免缓存穿透
【集成完成】SdkInitConfigService.getProductSdkConfig集成AB实验，buildAbTestRequest方法构建请求参数，enableSdkInitAb开关控制，AB实验分组结果覆盖默认配置
【常量管理】AbTestConstants统一管理缓存前缀、TTL、默认分组、HTTP超时、连接池配置等常量
【配置要求】Apollo配置abtest.service.url（AB实验服务地址），enable.sdk.init.ab（AB实验开关），spring.redis2.cluster.nodes（Redis集群节点）
