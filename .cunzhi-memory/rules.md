# 开发规范和规则

- 设备归因重试机制技术规范：

## 数据库设计规范
- 表名：lock_device_ocpc_retry_record
- 索引：(create_time,is_ocpc,retry_count)、(device_id,product,os)唯一索引
- 备份表：lock_device_attribution_backup，存储删除前的原始数据JSON

## 代码集成规范
- OcpcLockService.isOcpcUser()和isOcpcUserForIos()返回false时触发异步记录
- 使用@Async("dataPersistenceExecutor")确保异步执行
- 智能过滤：检查LcUserActive.createTime或LcToutiaoCk.createTime，只记录24小时内新用户

## 配置管理规范
- Apollo配置前缀：device.ocpc.retry.*
- 关键开关：device.ocpc.retry.enabled、device.ocpc.retry.new.user.hours
- Kafka配置：复用LBSKafKaSender的bootstrap.servers配置

## 监控规范
- 关键指标：记录成功率>99.9%、API成功率>90%、转化率统计
- 告警条件：错误数>100、成功率<95%
- 日志格式：结构化日志，包含deviceId、product、os等关键字段

## 性能要求
- 主流程无阻塞：异步记录不影响OCPC判断响应时间
- 数据量控制：通过24小时过滤减少80%无效数据
- 批处理限制：定时任务每批最多1000条，API调用每批100条
- 设备归因重试机制技术规范（2025-01-17更新）：

## 数据库设计规范
**重试记录表：lock_device_gy_retry_record**
- 主键：id (bigint auto_increment)
- 业务字段：product, device_id, os, is_ocpc, retry_count
- 时间字段：create_time, update_time
- 唯一索引：(device_id, product, os)
- 查询索引：(create_time desc, is_ocpc asc)

**备份表：lock_device_attribution_backup**
- 来源标识：source_table (AndroidLockRst/LockIosRst), source_id
- 设备信息：device_id, product, os, user_id
- 数据存储：original_data (JSON格式)
- 关联信息：delete_reason, retry_record_id
- 索引：(device_id, product, os), (source_table, source_id), (create_time desc)

## 代码集成规范
- 触发点：OcpcLockService.isOcpcUser()和isOcpcUserForIos()返回false时
- 异步执行：@Async("dataPersistenceExecutor")确保主流程零阻塞
- 新用户过滤：检查LcUserActive.createTime或LcToutiaoCk.createTime，24小时窗口
- 数据备份：删除前必须备份到lock_device_attribution_backup表

## 技术参数规范（代码写死）
- NEW_USER_HOURS = 24（新用户时间窗口）
- BATCH_SIZE = 1000（定时任务批量大小）
- API_BATCH_SIZE = 100（API调用批量大小）
- MAX_RETRY_COUNT = 3（最大重试次数）
- BACKUP_RETENTION_DAYS = 30（备份数据保留天数）

## 监控规范
- 异步记录成功率 >99.9%
- API调用成功率 >90%
- 数据处理延迟 <1小时
- 线程池使用率监控
- 告警条件：错误数>100/小时，成功率<95%
- safe-processor模块Maven构建规范（2025-01-17更新）：

## Maven依赖配置
**基础Spring Boot依赖**：
- spring-boot-starter, spring-boot-starter-web, spring-boot-starter-actuator

**监控依赖（与safe-api/safe-biz完全一致）**：
- micrometer-registry-prometheus
- micrometer-jvm-extras (version: 0.2.0)
- pepper-metrics-servlet (version: 1.0.24)
- pepper-metrics-ds-prometheus (version: 1.0.24)

**基础核心依赖**：
- base-core (version: 0.1.5) - 提供Apollo配置、Redis、数据库连接等基础功能

**项目内部依赖**：
- safe-core (轻量级数据层，避免safe-biz重量级依赖)

**Kafka消费依赖**：
- kafka-clients, spring-kafka

**数据库和工具依赖**：
- mybatis-plus-boot-starter, mysql-connector-java, hutool-all, fastjson

## Apollo配置规范
**配置命名空间（复用现有）**：
- prometheus.management（监控配置）
- base.motan（基础RPC配置）
- safe.datasource（数据源配置）

**应用配置**：
- server.port=8300
- spring.application.name=safe-processor
- mybatis-plus.typeAliasesPackage=com.shinet.core.safe.entity
- app.logging.path=logs/safe-processor

## 构建配置规范
**Maven构建**：与safe-api保持相同构建方式
**主类**：com.shinet.core.safe.SafeProcessorApplication
**最终名称**：safe-processor
**根pom.xml**：需要在modules中添加safe-processor模块

## 架构设计原则
- 轻量级独立：仅依赖safe-core数据层，避免重量级依赖
- 监控完备：包含完整的Prometheus监控体系
- 配置复用：使用相同的Apollo配置管理
- 功能专一：专注于Kafka消费和数据处理
- 设备拉黑系统Redis Key规范：统一前缀lock:black:，Android设备key格式为lock:black:{target_id}（不包含os），iOS设备key格式为lock:black:ios:{target_id}（包含ios标识）；数据库表device_black_list核心字段os、target_type、target_id；批量新增接口支持最大1000条记录；统一检测服务根据os构建不同的Redis key进行Redisson批量查询，任意命中返回true；集成点为androidLock2Service.isAdLock和IosLockService.iosLockCheckSd方法
- 设备拉黑系统数据层实现规范：实体类和Mapper放在safe-core模块，包路径com.shinet.core.safe.core.entity和com.shinet.core.safe.core.mapper，SQL必须写在XML文件中不可在代码中字符串拼接，XML文件放在safe-core/src/main/resources/mapper/目录下
