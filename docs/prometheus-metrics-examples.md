# Prometheus指标类型详解与实际应用

## 1. Counter（计数器）- 只增不减
```java
// 示例：HTTP请求总数
@Component
public class HttpMetrics {
    private final Counter httpRequestsTotal = Counter.builder("http_requests_total")
            .description("Total HTTP requests")
            .labelNames("method", "endpoint", "status")
            .register(Metrics.globalRegistry);
    
    public void incrementRequest(String method, String endpoint, String status) {
        httpRequestsTotal.labels(method, endpoint, status).increment();
    }
}

// PromQL查询示例
// 查询每秒请求率：rate(http_requests_total[5m])
// 查询错误率：rate(http_requests_total{status=~"4..|5.."}[5m]) / rate(http_requests_total[5m])
```

## 2. Gauge（仪表盘）- 可增可减
```java
// 示例：线程池状态监控
@Component
public class ThreadPoolMetrics {
    private final Gauge activeThreads = Gauge.builder("thread_pool_active_threads")
            .description("Active threads in thread pool")
            .labelNames("pool_name")
            .register(Metrics.globalRegistry);
    
    private final Gauge queueSize = Gauge.builder("thread_pool_queue_size")
            .description("Queue size of thread pool")
            .labelNames("pool_name")
            .register(Metrics.globalRegistry);
    
    public void updateThreadPoolMetrics(String poolName, int active, int queueSize) {
        this.activeThreads.labels(poolName).set(active);
        this.queueSize.labels(poolName).set(queueSize);
    }
}

// PromQL查询示例
// 查询线程池使用率：thread_pool_active_threads / thread_pool_max_threads * 100
// 查询队列积压：thread_pool_queue_size
```

## 3. Histogram（直方图）- 统计分布
```java
// 示例：API响应时间分布
@Component
public class ApiMetrics {
    private final Timer responseTime = Timer.builder("api_response_time")
            .description("API response time distribution")
            .labelNames("endpoint", "method")
            .register(Metrics.globalRegistry);
    
    public Timer.Sample startTimer() {
        return Timer.start(Metrics.globalRegistry);
    }
    
    public void recordResponseTime(Timer.Sample sample, String endpoint, String method) {
        sample.stop(responseTime.labels(endpoint, method));
    }
}

// PromQL查询示例
// P95响应时间：histogram_quantile(0.95, rate(api_response_time_bucket[5m]))
// P99响应时间：histogram_quantile(0.99, rate(api_response_time_bucket[5m]))
// 平均响应时间：rate(api_response_time_sum[5m]) / rate(api_response_time_count[5m])
```

## 4. Summary（摘要）- 预计算分位数
```java
// 示例：数据库查询时间摘要
@Component
public class DatabaseMetrics {
    private final DistributionSummary queryTime = DistributionSummary.builder("db_query_time")
            .description("Database query time summary")
            .labelNames("table", "operation")
            .publishPercentiles(0.5, 0.95, 0.99) // 预计算分位数
            .register(Metrics.globalRegistry);
    
    public void recordQueryTime(String table, String operation, double timeMs) {
        queryTime.labels(table, operation).record(timeMs);
    }
}

// PromQL查询示例
// 直接查询分位数：db_query_time{quantile="0.95"}
// 查询总数：db_query_time_count
// 查询总和：db_query_time_sum
```

## 5. 业务指标设计原则

### 5.1 标签设计最佳实践
```java
// ✅ 好的标签设计
Counter.builder("api_requests_total")
    .labelNames("service", "endpoint", "method", "status_code")
    .register(registry);

// ❌ 避免高基数标签
Counter.builder("api_requests_total")
    .labelNames("user_id", "request_id") // 会产生大量时间序列
    .register(registry);
```

### 5.2 指标命名规范
```
# 基本格式：<namespace>_<subsystem>_<name>_<unit>
safe_api_requests_total          # 请求总数
safe_api_response_time_seconds   # 响应时间（秒）
safe_thread_pool_active_threads  # 活跃线程数
safe_cache_hit_ratio            # 缓存命中率
safe_db_connections_active      # 活跃数据库连接数
```

## 6. 针对core-safe项目的指标设计

### 6.1 IP查询服务指标
```java
// 缓存命中率
Gauge cacheHitRatio = Gauge.builder("safe_ip_cache_hit_ratio")
    .labelNames("cache_type") // caffeine, redis, hbase
    .register(registry);

// API调用次数
Counter apiCalls = Counter.builder("safe_ip_api_calls_total")
    .labelNames("api_type", "status") // ipService, cityByIpCc, cityByLocation
    .register(registry);

// 查询响应时间
Timer queryTime = Timer.builder("safe_ip_query_time")
    .labelNames("query_type", "cache_level")
    .register(registry);
```

### 6.2 设备归因重试指标
```java
// 异步记录成功率
Counter asyncRecords = Counter.builder("safe_device_async_records_total")
    .labelNames("status", "device_type") // success/failure, android/ios
    .register(registry);

// 重试次数分布
Counter retryAttempts = Counter.builder("safe_device_retry_attempts_total")
    .labelNames("retry_count", "result") // 1,2,3 / success/failure
    .register(registry);

// 批处理性能
Timer batchProcessTime = Timer.builder("safe_device_batch_process_time")
    .labelNames("batch_type") // api_call, kafka_consume
    .register(registry);
```
