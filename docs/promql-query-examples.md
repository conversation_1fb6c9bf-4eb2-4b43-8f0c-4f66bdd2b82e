# PromQL查询语言实战指南

## 1. 基础查询语法

### 1.1 选择器和过滤器
```promql
# 基础选择器
http_requests_total

# 标签匹配
http_requests_total{method="GET"}
http_requests_total{method="GET", status="200"}

# 标签匹配操作符
http_requests_total{status=~"2.."}     # 正则匹配
http_requests_total{status!="200"}     # 不等于
http_requests_total{status!~"4..|5.."} # 正则不匹配
```

### 1.2 时间范围查询
```promql
# 瞬时查询（当前值）
http_requests_total

# 范围查询（时间窗口）
http_requests_total[5m]    # 最近5分钟
http_requests_total[1h]    # 最近1小时
http_requests_total[1d]    # 最近1天

# 偏移查询
http_requests_total offset 1h          # 1小时前的值
http_requests_total[5m] offset 1h      # 1小时前的5分钟范围
```

## 2. 聚合函数

### 2.1 基础聚合
```promql
# 求和
sum(http_requests_total)
sum by (method) (http_requests_total)           # 按method分组求和
sum without (instance) (http_requests_total)    # 除instance外分组求和

# 平均值
avg(cpu_usage_percent)
avg by (service) (cpu_usage_percent)

# 最大值/最小值
max(memory_usage_bytes)
min(disk_free_bytes)

# 计数
count(up == 1)                                  # 统计在线实例数
count by (service) (up == 1)                   # 按服务统计在线实例
```

### 2.2 分位数计算
```promql
# Histogram分位数
histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))
histogram_quantile(0.99, sum by (le) (rate(http_request_duration_seconds_bucket[5m])))

# Summary分位数（直接查询）
http_request_duration_seconds{quantile="0.95"}
```

## 3. 速率和变化率函数

### 3.1 rate() - 每秒平均增长率
```promql
# HTTP请求QPS
rate(http_requests_total[5m])

# 错误率
rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])

# 按服务分组的QPS
sum by (service) (rate(http_requests_total[5m]))
```

### 3.2 irate() - 瞬时增长率
```promql
# 瞬时QPS（基于最后两个数据点）
irate(http_requests_total[5m])

# 瞬时CPU使用率变化
irate(cpu_seconds_total[5m])
```

### 3.3 increase() - 时间窗口内增长量
```promql
# 5分钟内请求增长量
increase(http_requests_total[5m])

# 1小时内错误增长量
increase(http_requests_total{status=~"5.."}[1h])
```

## 4. 数学运算和函数

### 4.1 算术运算
```promql
# 内存使用率
(memory_used_bytes / memory_total_bytes) * 100

# 磁盘使用率
100 - (disk_free_bytes / disk_total_bytes) * 100

# CPU使用率
100 - (avg by (instance) (rate(cpu_seconds_total{mode="idle"}[5m])) * 100)
```

### 4.2 比较运算
```promql
# 内存使用率超过80%的实例
(memory_used_bytes / memory_total_bytes) * 100 > 80

# QPS超过100的服务
sum by (service) (rate(http_requests_total[5m])) > 100

# 响应时间P95超过1秒的接口
histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
```

### 4.3 逻辑运算
```promql
# AND运算：高CPU且高内存的实例
(cpu_usage_percent > 80) and (memory_usage_percent > 80)

# OR运算：CPU或内存使用率高的实例
(cpu_usage_percent > 80) or (memory_usage_percent > 80)

# UNLESS运算：排除特定实例
up unless on(instance) up{instance=~"test.*"}
```

## 5. 针对core-safe项目的实用查询

### 5.1 API性能监控
```promql
# API QPS
sum by (endpoint) (rate(http_requests_total[5m]))

# API错误率
sum by (endpoint) (rate(http_requests_total{status=~"4..|5.."}[5m])) / 
sum by (endpoint) (rate(http_requests_total[5m])) * 100

# API P95响应时间
histogram_quantile(0.95, 
  sum by (endpoint, le) (rate(http_request_duration_seconds_bucket[5m])))

# 慢接口识别（P95 > 1秒）
histogram_quantile(0.95, 
  sum by (endpoint, le) (rate(http_request_duration_seconds_bucket[5m]))) > 1
```

### 5.2 线程池监控
```promql
# 线程池使用率
(thread_pool_active_threads / thread_pool_max_threads) * 100

# 队列积压严重的线程池
thread_pool_queue_size > 100

# 线程池拒绝率
rate(thread_pool_rejected_total[5m])

# 线程池效率（完成任务数/活跃线程数）
rate(thread_pool_completed_total[5m]) / thread_pool_active_threads
```

### 5.3 缓存性能监控
```promql
# 缓存命中率
cache_hits_total / (cache_hits_total + cache_misses_total) * 100

# 缓存命中率趋势（1小时窗口）
rate(cache_hits_total[1h]) / 
(rate(cache_hits_total[1h]) + rate(cache_misses_total[1h])) * 100

# 缓存穿透检测
rate(cache_misses_total[5m]) > 10

# 多级缓存效果对比
sum by (cache_level) (rate(cache_hits_total[5m]))
```

### 5.4 业务指标监控
```promql
# 设备归因成功率
rate(device_attribution_success_total[5m]) / 
rate(device_attribution_attempts_total[5m]) * 100

# IP查询服务可用性
up{job="safe-api"} * 100

# 数据库连接池状态
db_connections_active / db_connections_max * 100

# JVM GC影响
rate(jvm_gc_collection_seconds_sum[5m]) / rate(jvm_gc_collection_seconds_count[5m])
```

## 6. 告警规则设计

### 6.1 基础告警
```yaml
# 服务可用性告警
- alert: ServiceDown
  expr: up == 0
  for: 1m
  labels:
    severity: critical
  annotations:
    summary: "Service {{ $labels.instance }} is down"

# 高错误率告警
- alert: HighErrorRate
  expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
  for: 2m
  labels:
    severity: warning
  annotations:
    summary: "High error rate on {{ $labels.endpoint }}"
```

### 6.2 性能告警
```yaml
# 响应时间告警
- alert: HighResponseTime
  expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
  for: 3m
  labels:
    severity: warning
  annotations:
    summary: "High response time on {{ $labels.endpoint }}"

# 线程池告警
- alert: ThreadPoolExhaustion
  expr: thread_pool_active_threads / thread_pool_max_threads > 0.9
  for: 2m
  labels:
    severity: critical
  annotations:
    summary: "Thread pool {{ $labels.pool_name }} near exhaustion"
```
