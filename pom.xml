<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.shinet.core.base</groupId>
		<artifactId>base</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<groupId>com.shinet.core</groupId>
	<artifactId>core-safe</artifactId>
	<version>${revision}</version>
	<name>core-safe</name>
	<description>safe service</description>
	<packaging>pom</packaging>
	<properties>
		<java.version>1.8</java.version>
		<revision>0.0.1-SNAPSHOT</revision>
	</properties>
	<dependencies>
		<dependency>
			<groupId>com.xuxueli</groupId>
			<artifactId>xxl-job-core</artifactId>
			<version>2.2.0</version>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<version>2.10.10</version>
		</dependency>
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>4.9.1</version>
		</dependency>

		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-core</artifactId>
			<version>4.5.6</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun.openservices</groupId>
			<artifactId>aliyun-log-logback-appender</artifactId>
			<version>0.1.18</version>
		</dependency>


		<dependency>
			<groupId>com.volcengine</groupId>
			<artifactId>volc-sdk-java</artifactId>
			<version>1.0.21</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
		</dependency>
		<!-- Redisson for advanced Redis operations -->
<!--		<dependency>-->
<!--			<groupId>org.redisson</groupId>-->
<!--			<artifactId>redisson-spring-boot-starter</artifactId>-->
<!--			<version>3.24.3</version>-->
<!--		</dependency>-->
		<!-- https://mvnrepository.com/artifact/org.apache.commons/commons-pool2 -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
			<version>2.6.2</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.jsoup/jsoup -->
		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.14.3</version>
		</dependency>


	</dependencies>
	<modules>
		<module>safe-core</module>
		<module>safe-biz</module>
		<module>safe-api</module>
		<module>safe-timer</module>
		<module>safe-processor</module>
	</modules>
	<profiles>
		<profile>
			<id>deploy-all</id>
			<modules>
				<module>safe-core</module>
				<module>safe-biz</module>
				<module>safe-api</module>
				<module>safe-timer</module>
				<module>safe-processor</module>
			</modules>
		</profile>
	</profiles>

	<repositories>
		<repository>
			<id>coohua_snapshots</id>
			<url>http://maven.coohua.com:8002/nexus/content/repositories/snapshots</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>true</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>coohua_releases</id>
			<url>http://maven.coohua.com:8002/nexus/content/repositories/releases</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
		<repository>
			<id>coohua_3rdparty</id>
			<url>http://maven.coohua.com:8002/nexus/content/repositories/thirdparty/</url>
			<releases>
				<enabled>true</enabled>
			</releases>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
	</repositories>

</project>
