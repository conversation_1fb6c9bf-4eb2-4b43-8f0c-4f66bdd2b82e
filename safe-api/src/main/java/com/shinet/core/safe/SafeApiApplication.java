package com.shinet.core.safe;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;

@SpringBootApplication
@EnableAspectJAutoProxy
@MapperScan(basePackages = {"com.shinet.core.safe.msql.mapper", "com.shinet.core.safe.core.mapper"})
@EnableApolloConfig(value = {
		"prometheus.management","base.motan","safe.datasource","bp.retrieve.rpc.refer","ad.user.ad.api.referer","bp.user.rpc.referer","bp.account.rpc.referer"
		,"related.biz.config"
})
@Slf4j
@EnableScheduling
public class SafeApiApplication {

	public static void main(String[] args) {
		ConfigurableApplicationContext configurableApplicationContext = SpringApplication.run(SafeApiApplication.class, args);
	}

}
