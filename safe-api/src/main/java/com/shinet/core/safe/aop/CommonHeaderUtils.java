package com.shinet.core.safe.aop;

import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Base64;

@Slf4j
public class CommonHeaderUtils {
    public static CommonHeaderDTO getHeaderVo(HttpServletRequest request){
        String deviceId = request.getHeader("deviceId");
        String brand = request.getHeader("brand");
        String gps = request.getHeader("gps");
        String bs = request.getHeader("bs");
        String appVersion = request.getHeader("appVersion");
        String os = request.getHeader("os");
        String channel = request.getHeader("channel");
        String romVersion = request.getHeader("romVersion");
        String osVersion = request.getHeader("osVersion");
        String osLevel = request.getHeader("osLevel");
        String accessKey = request.getHeader("accessKey");
        String wechatId = request.getHeader("wechatId");
        String pkgId = request.getHeader("pkgId");
        String appId = request.getHeader("appId");
        String imei = request.getHeader("imei");
        String oaid = request.getHeader("oaid");
        String androidId = request.getHeader("androidId");
        String mac = request.getHeader("mac");
        String model = request.getHeader("model");
        String caid = request.getHeader("caid");
        String product = request.getHeader("product");
        String ua = request.getHeader("ua");
        String openId = request.getHeader("openId");
        String timestamp = request.getHeader("timestamp");
        String sdkVersion = request.getHeader("sdkVersion");
        String idfa = request.getHeader("idfa");
        if (StringUtils.isEmpty(ua)){
            ua = request.getHeader("useragent");
        }


        CommonHeaderDTO commonHeaderDTO = new CommonHeaderDTO();
        commonHeaderDTO.setDeviceId(deviceId);
        commonHeaderDTO.setBrand(brand);
        commonHeaderDTO.setGps(gps);
        commonHeaderDTO.setBs(bs);
        commonHeaderDTO.setAppVersion(appVersion);
        commonHeaderDTO.setOs(os);
        commonHeaderDTO.setProduct(product);
        commonHeaderDTO.setChannel(channel);
        commonHeaderDTO.setRomVersion(romVersion);
        commonHeaderDTO.setOsVersion(osVersion);
        commonHeaderDTO.setOsLevel(osLevel);
        commonHeaderDTO.setAccessKey(accessKey);
        commonHeaderDTO.setWechatId(wechatId);
        commonHeaderDTO.setPkgId(pkgId);
        commonHeaderDTO.setAppId(appId);
        commonHeaderDTO.setImei(imei);
        commonHeaderDTO.setOaid(oaid);
        commonHeaderDTO.setAndroidId(androidId);
        commonHeaderDTO.setMac(mac);
        commonHeaderDTO.setIp(getReomteIp(request));
        commonHeaderDTO.setModel(model);
        commonHeaderDTO.setCaid(caid);
        commonHeaderDTO.setUa(ua);
        commonHeaderDTO.setOpenId(openId);
        commonHeaderDTO.setTimestamp(timestamp);
        commonHeaderDTO.setSdkVersion(sdkVersion);
        commonHeaderDTO.setIdfa(idfa);
        return commonHeaderDTO;
    }

    public static String getReomteIp(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        try{
            if (ip.contains(",")){
                String newIp = ip.split(",")[0];
                log.info("替换ip {} -> {}",ip,newIp);
                ip = newIp;
            }
        }catch (Exception e){
            log.error("处理Ip异常：",e);
        }
        return ip;
    }

    public static String getDeviceInfoFromHeader(HttpServletRequest request) {
        try {
            String deviceInfo = request.getHeader("deviceInfo");
            if (deviceInfo == null) {
                return null;
            }

            // Base64 解码
            byte[] decodedBytes = Base64.getDecoder().decode(deviceInfo);
            return new String(decodedBytes);
        } catch (IllegalArgumentException e) {
            log.warn("Invalid Base64 format for deviceInfo", e);
        } catch (Exception e) {
            log.warn("Decode Base64 format for deviceInfo", e);
        }
        return null;
    }
}