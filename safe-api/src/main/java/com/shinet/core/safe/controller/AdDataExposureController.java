package com.shinet.core.safe.controller;

import com.shinet.core.safe.msql.entity.AdExposure;
import com.shinet.core.safe.msql.service.AdExposureService;
import com.shinet.core.safe.util.DateUtils;
import com.shinet.core.safe.util.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;

@RestController
@RequestMapping("/sf/expo")
@Slf4j
public class AdDataExposureController {
    @Autowired
    AdExposureService adExposureService;
    @RequestMapping("/up")
    @ResponseBody
    public ReturnResult click(AdExposure adExposure, HttpServletRequest request) {
        ReturnResult returnResult = new ReturnResult();

        adExposure.setAppVersion(request.getHeader("appVersion"));
        adExposure.setDeviceId(request.getHeader("deviceId"));
        adExposure.setOaid(request.getHeader("oaid"));
        adExposure.setProduct(request.getHeader("product"));
        adExposure.setSdkVersion(request.getHeader("sdkVersion"));
        String userId = request.getHeader("userId")==null?"0":request.getHeader("userId");
        adExposure.setUserId(Integer.parseInt(userId));
        Date date = new Date();
        adExposure.setSendTime(request.getHeader("sendTime"));
        adExposure.setCreateTime(date);
        adExposure.setLogday(DateUtils.formatDate(date,DateUtils.PATTERN_YMDSTR));


        try {
            adExposureService.save(adExposure);
        }catch (Exception e){
            log.error("",e);
        }
        return returnResult;
    }
}
