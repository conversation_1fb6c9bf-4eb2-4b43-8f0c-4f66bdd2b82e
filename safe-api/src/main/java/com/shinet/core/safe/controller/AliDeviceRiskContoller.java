package com.shinet.core.safe.controller;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.shinet.core.safe.aop.CommonHeaderUtils;
import com.shinet.core.safe.hsq.req.CsRiskDeviceReq;
import com.shinet.core.safe.hsq.req.RiskDeviceReq;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.enums.ByteEventEnums;
import com.shinet.core.safe.msql.enums.SubEventEnums;
import com.shinet.core.safe.msql.service.AliUserDeviceService;
import com.shinet.core.safe.msql.service.ByteSafeService;
import com.shinet.core.safe.msql.service.ByteUserDeviceService;
import com.shinet.core.safe.util.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/sf/rk")
@Slf4j
public class AliDeviceRiskContoller {
    @Autowired
    AliUserDeviceService aliUserDeviceService;
    @Autowired
    ByteUserDeviceService byteUserDeviceService;

    @ApolloJsonValue("${join.hs.app.list:[]}")
    private List<Integer> joinHuoShanCheckApp;

    @RequestMapping("/iy")
    @ResponseBody
    public ReturnResult click(RiskDeviceReq riskDeviceReq, HttpServletRequest request) {
        ReturnResult returnResult = new ReturnResult();

        CommonHeaderDTO commonHeaderDTO = CommonHeaderUtils.getHeaderVo(request);
        try {
            boolean isRisk = aliUserDeviceService.queryRiskDeivce(riskDeviceReq,commonHeaderDTO);
            returnResult.setData(isRisk);
        }catch (Exception e){
            log.error("",e);
        }
        return returnResult;
    }

    @Autowired
    ByteSafeService byteSafeService;
    @RequestMapping("/cs")
    @ResponseBody
    public ReturnResult cs(CsRiskDeviceReq csRiskDeviceReq, HttpServletRequest request) {
        ReturnResult returnResult = new ReturnResult();

        CommonHeaderDTO commonHeaderDTO = CommonHeaderUtils.getHeaderVo(request);
        try {
            ByteEventEnums byteEventEnums = ByteEventEnums.getByCode(csRiskDeviceReq.getDtype());
            if (byteEventEnums == null){
                returnResult.setData(true);
                return returnResult;
            }

            if (ByteEventEnums.activity.code.equals(csRiskDeviceReq.getDtype()) || ByteEventEnums.send_invite.code.equals(csRiskDeviceReq.getDtype()) ){
                returnResult.setData(true);
                return returnResult;
            }

            SubEventEnums subEventEnums = SubEventEnums.getByCode(csRiskDeviceReq.getSubDType());
            boolean isRisk = true;
            if (StringUtils.isNotEmpty(commonHeaderDTO.getAppId()) && !"null".equals(commonHeaderDTO.getAppId())) {
                if (joinHuoShanCheckApp.contains(Integer.valueOf(commonHeaderDTO.getAppId()))) {
                    isRisk = byteSafeService.csRisk(commonHeaderDTO, csRiskDeviceReq, byteEventEnums,subEventEnums);
                }
            }
            returnResult.setData(isRisk);
        }catch (Exception e){
            log.error("",e);
        }
        return returnResult;
    }
    @RequestMapping("/checkExists")
    @ResponseBody
    public ReturnResult checkExists(String product, Integer dayCount, Long userId) {
        ReturnResult returnResult = new ReturnResult();
        try {
            returnResult.setData(
                byteUserDeviceService.existsByteUser(product
                        , userId
                        , dayCount)
            );
        }catch (Exception e){
            log.error("ReturnResult checkExists(String product, Integer dayCount, Long userId) 异常",e);
            returnResult.setData(false);
        }
        return returnResult;
    }
}
