package com.shinet.core.safe.controller;

import cn.hutool.core.thread.NamedThreadFactory;
import com.shinet.core.safe.msql.entity.ClientTest;
import com.shinet.core.safe.msql.mapper.UserExpInfoMapper;
import com.shinet.core.safe.util.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@RequestMapping("/client/test")
@RestController
public class ClientTestController {

    @Autowired
    private UserExpInfoMapper userExpInfoMapper;

    public static final ThreadPoolExecutor clientRecordPool =  new ThreadPoolExecutor(
            Runtime.getRuntime().availableProcessors(),
            Runtime.getRuntime().availableProcessors()*2,
            60,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(500),
            new NamedThreadFactory("clientRecordPool",false),
            new ThreadPoolExecutor.CallerRunsPolicy());

    @RequestMapping("/record")
    @ResponseBody
    public ReturnResult clientTestRecord(@RequestBody ClientTest clientTest, HttpServletRequest request) {
        ReturnResult returnResult = new ReturnResult();

//        try {
//            CompletableFuture.runAsync(() -> {
//                userExpInfoMapper.insertClientTestRecord(clientTest.getType(), clientTest.getUserId(), clientTest.getPosId(), clientTest.getAdType(), new Date());
//            }, clientRecordPool);
//            returnResult.setStatus(200);
//        } catch (Exception e) {
//            returnResult.setStatus(500);
//        }
        returnResult.setStatus(500);
        returnResult.setMessage("接口已关闭");


        return returnResult;
    }
}
