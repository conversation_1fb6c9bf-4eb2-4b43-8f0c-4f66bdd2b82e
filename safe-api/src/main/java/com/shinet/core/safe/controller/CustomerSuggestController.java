package com.shinet.core.safe.controller;

import com.shinet.core.safe.aop.CommonHeaderUtils;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.entity.CustomerSuggest;
import com.shinet.core.safe.msql.service.CustomerSuggestService;
import com.shinet.core.safe.util.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @since 2024/3/13
 * @desc 记录建议
 */
@Slf4j
@RequestMapping("sf/customer")
@RestController
public class CustomerSuggestController {

    @Autowired
    private CustomerSuggestService customerSuggestService;


    /**
     * customerSuggest 必传参数  Integer appId, String phone, String suggest
     * @param request
     * @param customerSuggest
     * @return
     */
    @PostMapping("suggest")
    public ReturnResult suggest(HttpServletRequest request, @RequestBody CustomerSuggest customerSuggest){
        if (customerSuggest.getAppId() == null){
            return new ReturnResult();
        }

        CommonHeaderDTO commonHeaderDTO = CommonHeaderUtils.getHeaderVo(request);

        if (StringUtils.isNotEmpty(customerSuggest.getLlxip())){
            log.info("记录建议 使用传入值 {} ==> {}",commonHeaderDTO.getIp(),customerSuggest.getLlxip());
            commonHeaderDTO.setIp(customerSuggest.getLlxip());
        }


        customerSuggestService.preSave(commonHeaderDTO, customerSuggest);

        ReturnResult returnResult = new ReturnResult();

        return returnResult;
    }
}
