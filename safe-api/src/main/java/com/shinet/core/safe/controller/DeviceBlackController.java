package com.shinet.core.safe.controller;

import com.shinet.core.safe.core.entity.LockDeviceBlack;
import com.shinet.core.safe.msql.service.DeviceBlackService;
import com.shinet.core.safe.util.ReturnResult;
import com.shinet.core.safe.vo.DeviceBlackBatchAddVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 设备拉黑管理接口
 *
 * <AUTHOR>
 * @since 2025-01-24
 */
@Slf4j
@RestController
@RequestMapping("/device/black")
public class DeviceBlackController {

    @Autowired
    private DeviceBlackService deviceBlackService;

    /**
     * 批量新增拉黑设备
     *
     * @param request 批量新增请求参数
     * @return 新增结果
     */
    @PostMapping("/batch/add")
    public ReturnResult batchAddBlackDevices(@RequestBody DeviceBlackBatchAddVo request) {
        ReturnResult result = new ReturnResult();

        try {
            // 参数校验
            if (request == null) {
                result.setStatus(400);
                result.setMessage("请求参数不能为空");
                return result;
            }

            if (StringUtils.isBlank(request.getOs()) || request.getTargetType() == null ||
                    StringUtils.isBlank(request.getTargetListStr()) || StringUtils.isBlank(request.getRemark())) {
                result.setStatus(400);
                result.setMessage("参数不完整：os、targetType、targetListStr、remark为必填项");
                return result;
            }

            // 执行批量新增
            int addCount = deviceBlackService.batchAddBlackDevices(
                    request.getOs(),
                    request.getTargetType(),
                    request.getTargetListStr(),
                    request.getRemark());

            result.setStatus(200);
            result.setMessage("批量新增成功");
            result.setData("addCount", addCount);
            result.setData("os", request.getOs());
            result.setData("targetType", request.getTargetType());
            result.setData("targetTypeDesc", request.getTargetTypeDesc());

            log.info("设备拉黑批量新增成功：os={}, targetType={}, 新增数量={}, remark={}",
                    request.getOs(), request.getTargetType(), addCount, request.getRemark());

        } catch (Exception e) {
            log.error("设备拉黑批量新增异常：os={}, targetType={}, error={}",
                    request != null ? request.getOs() : "null",
                    request != null ? request.getTargetType() : "null",
                    e.getMessage(), e);
            result.setStatus(500);
            result.setMessage("批量新增失败：" + e.getMessage());
        }

        return result;
    }

    @PostMapping("batch/del")
    public ReturnResult batchDelBlackDevices(@RequestBody DeviceBlackBatchAddVo request) {
        ReturnResult result = new ReturnResult();

        try {
            // 参数校验
            if (request == null) {
                result.setStatus(400);
                result.setMessage("请求参数不能为空");
                return result;
            }

            if (StringUtils.isBlank(request.getOs()) || request.getTargetType() == null ||
                    StringUtils.isBlank(request.getTargetListStr())) {
                result.setStatus(400);
                result.setMessage("参数不完整：os、targetType、targetListStr为必填项");
                return result;
            }

            // 执行批量删除
            int delCount = deviceBlackService.batchDelBlackDevices(
                    request.getOs(),
                    request.getTargetType(),
                    request.getTargetListStr(),
                    request.getRemark());

            result.setStatus(200);
            result.setMessage("批量删除成功");
            result.setData("delCount", delCount);
            result.setData("os", request.getOs());

            log.info("设备拉黑批量删除成功：os={}, targetType={}, 删除数量={}, remark={}",
                    request.getOs(), request.getTargetType(), delCount, request.getRemark());

        } catch (Exception e) {
            log.error("设备拉黑批量新增异常：os={}, targetType={}, error={}",
                    request != null ? request.getOs() : "null",
                    request != null ? request.getTargetType() : "null",
                    e.getMessage(), e);
            result.setStatus(500);
            result.setMessage("批量删除失败：" + e.getMessage());
        }

        return result;
    }

    @PostMapping("/query/info")
    public ReturnResult queryBlackDevices(@RequestBody DeviceBlackBatchAddVo request) {
        ReturnResult result = new ReturnResult();

        try {
            // 参数校验
            if (request == null) {
                result.setStatus(400);
                result.setMessage("请求参数不能为空");
                return result;
            }

            if (StringUtils.isBlank(request.getOs()) || request.getTargetType() == null ||
                    StringUtils.isBlank(request.getTargetListStr())) {
                result.setStatus(400);
                result.setMessage("参数不完整：os、targetType、targetListStr为必填项");
                return result;
            }

            // 执行批量新增
            LockDeviceBlack lockDeviceBlack = deviceBlackService.queryBlackDevice(
                    request.getOs(),
                    request.getTargetType(),
                    request.getTargetListStr(),
                    request.getRemark());

            result.setStatus(200);
            result.setMessage("成功");
            result.setData("deviceBlackInfo", lockDeviceBlack);

        } catch (Exception e) {
            log.error("设备拉黑查询异常：os={}, targetType={}, error={}",
                    request != null ? request.getOs() : "null",
                    request != null ? request.getTargetType() : "null",
                    e.getMessage(), e);
            result.setStatus(500);
            result.setMessage("查询失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 获取支持的设备类型列表
     */
    @GetMapping("/types")
    public ReturnResult getSupportedTypes() {
        ReturnResult result = new ReturnResult();

        try {
            result.setStatus(200);
            result.setMessage("获取成功");
            result.setData("android", new String[]{"1-IP", "2-OAID"});
            result.setData("ios", new String[]{"1-IP", "3-CAID", "4-IDFA"});
            result.setData("description", "设备类型说明：1-IP地址, 2-OAID(Android), 3-CAID(iOS), 4-IDFA(iOS)");

        } catch (Exception e) {
            log.error("获取设备类型列表异常：error={}", e.getMessage(), e);
            result.setStatus(500);
            result.setMessage("获取失败：" + e.getMessage());
        }

        return result;
    }
}
