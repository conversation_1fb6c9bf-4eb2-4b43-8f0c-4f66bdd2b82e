package com.shinet.core.safe.controller;

import com.alibaba.fastjson.JSON;
import com.shinet.core.safe.aop.CommonHeaderUtils;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.service.IosBipService;
import com.shinet.core.safe.msql.service.IosProjectSwitchService;
import com.shinet.core.safe.util.DateUtils;
import com.shinet.core.safe.util.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Controller
@RequestMapping("/sf/evt")
@Slf4j
public class IosEventController {
    @Autowired
    IosBipService iosBipService;
    @RequestMapping("/i1")
    @ResponseBody
    public ReturnResult textToImg(HttpServletRequest request) {
        ReturnResult returnResult = new ReturnResult();
        String dateStr = DateUtils.formatDate(new Date());
        try {
            String product = request.getParameter("project");
            String ipStr = IpUtils.getIpAddress(request);

            log.info("访问ios "+request.getParameter("project") +" "+dateStr+" "+request.getParameter("et")+"@@@"+ IpUtils.getIpAddress(request));

            iosBipService.addBip(product,ipStr);
        }catch (Exception e){
            log.error("",e);
        }
        return returnResult;
    }

    @Autowired
    IosProjectSwitchService iosProjectSwitchService;

    @RequestMapping("/i2")
    @ResponseBody
    public ReturnResult i2(HttpServletRequest request) {
        ReturnResult returnResult = new ReturnResult();
        CommonHeaderDTO commonHeaderDTO = CommonHeaderUtils.getHeaderVo(request);
        String dateStr = DateUtils.formatDate(new Date());
        try {
            String product = commonHeaderDTO.getProduct();
            String ipStr = IpUtils.getIpAddress(request);
            boolean isLocked = iosProjectSwitchService.isLock(commonHeaderDTO,commonHeaderDTO.getIp());
            log.info("IOS锁区访问接口 "+request.getParameter("project") +" "+isLocked+" "+ JSON.toJSONString(commonHeaderDTO));

            Map<String,String> dmap = new HashMap<>();
            dmap.put("lm",product);
            dmap.put("lh",commonHeaderDTO.getProduct());
            dmap.put("lt",isLocked+"");
            returnResult.setData(dmap);
        }catch (Exception e){
            log.error("",e);
            Map<String,String> dmap = new HashMap<>();
            dmap.put("lh",commonHeaderDTO.getProduct());
            dmap.put("lt",true+"");
            returnResult.setData(dmap);
        }
        return returnResult;
    }
}
