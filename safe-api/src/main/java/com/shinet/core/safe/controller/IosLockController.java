package com.shinet.core.safe.controller;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.shinet.core.safe.dto.LockKeyResult;
import com.shinet.core.safe.msql.config.SafeSwitcher;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.service.LockAreaConfigService;
import com.shinet.core.safe.msql.service.LockIosRstService;
import com.shinet.core.safe.msql.service.ioslock.IosLockManagerService;
import com.shinet.core.safe.msql.service.ioslock.IosLockService;
import com.shinet.core.safe.msql.service.ioslock.LockIosConfigService;
import com.shinet.core.safe.util.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Random;

@Slf4j
@RestController
public class IosLockController {
    @Autowired
    LockAreaConfigService lockAreaConfigService;
    @Autowired
    IosLockManagerService iosLockMag;
    @Autowired
    SafeSwitcher safeSwitcher;
    @Autowired
    LockIosConfigService lockIosConfigService;

    /**
     *
     * @param product
     * @param version
     * @param uuid
     * @param ist  istxx  ist开头代表安装  其他代表没安装
     * @param request
     * @return
     */
    @RequestMapping("cost/{product}/{version}/{uuid}")
    @ResponseBody
    public HashMap queryLockInfo(@PathVariable String product,
                                 @PathVariable String version,
                                 @PathVariable String uuid,
                                 String ist,
                                 HttpServletRequest request) {


        HashMap returnResult = new HashMap();
        String ip = IpUtils.getIpAddress(request);

        boolean isInstallWx = true;
        if(StringUtils.isNotBlank(ist)){
            isInstallWx = StringUtils.startsWith(ist,"ist");
        }
        Pair<Boolean, LockKeyResult> resultK = iosLockMag.iosLockMag(product, version, ip,isInstallWx,true,null);
//        if(resultK.getValue().getLocked()){
//            //锁区自动同步一份到
//            log.info("开始存储第一层锁区 "+product+" "+version+" "+ip);
//            lockIosRstService.addIosRst("ipcheck",product,null,null,true,null,ip,resultK.getValue().getCity(),"iplock",version);
//        }
        long ctime = System.currentTimeMillis();
        returnResult.put(uuid + new Random().nextInt(1000), System.currentTimeMillis());
        String key = "jmt" + ctime + "";
        returnResult.put(key, resultK.getValue().getLocked() + "");

        if (resultK.getKey() ||
                (resultK.getValue() != null && resultK.getValue().getIosIpRst() != null && resultK.getValue().getIosIpRst().getLoadingFlg() != null && resultK.getValue().getIosIpRst().getLoadingFlg() == 1)) {
            returnResult.put("loading" + ctime + "", resultK.getKey() + "");
            log.info("产品loading "+product+" "+ JSON.toJSONString(resultK)+" ip:"+ip);
        }

        if (StringUtils.equalsIgnoreCase("qzcyksy", product) &&
                (resultK.getValue().getLocked() || (resultK.getValue().getIosIpRst() != null && resultK.getValue().getIosIpRst().getLoadingFlg() != null && resultK.getValue().getIosIpRst().getLoadingFlg() == 1)
                )) {
            log.info("移除key直接锁死 " + product + " " + version + " " + ip);
            returnResult.remove(key);
        }

        log.info("iosfirst "+product+" "+isInstallWx + " " + version + " " + ip+" "+JSON.toJSONString(returnResult));
        return returnResult;
    }


    /**
     *
     * @param product
     * @param version
     * @param uuid
     * @param ist
     * @param request
     * @return
     */
    @RequestMapping("oiumk/{product}/{version}/{uuid}/{zfbist}/{dyist}")
    @ResponseBody
    public HashMap oiumkqueryLockInfo(@PathVariable String product,
                                 @PathVariable String version,
                                 @PathVariable String uuid,
                                      @PathVariable String zfbist,
                                      @PathVariable String dyist,
                                 String ist,
                                 HttpServletRequest request) {


        HashMap returnResult = new HashMap();
        String ip = IpUtils.getIpAddress(request);

        /**
         * wx是否安装
         */
        boolean isInstallWx = true;
        if(StringUtils.isNotBlank(ist)){
            isInstallWx = StringUtils.startsWith(ist,"ist");
        }


        boolean isInstallZfb = true;
        if(StringUtils.isNotBlank(zfbist)){
            isInstallZfb = StringUtils.startsWith(zfbist,"ist");
        }

        if(!isInstallZfb){
            log.info("zfb未安装 "+product+" "+version);
        }

        boolean isInstallDy = true;
        if(StringUtils.isNotBlank(dyist)){
            isInstallDy = StringUtils.startsWith(ist,"ist");
        }
        if(!isInstallDy){
            log.info("dy未安装 "+product+" "+version);
        }

        Pair<Boolean, LockKeyResult> resultK = iosLockMag.iosLockMag(product, version, ip,isInstallWx,isInstallDy,null);
//        if(resultK.getValue().getLocked()){
//            //锁区自动同步一份到
//            log.info("开始存储第一层锁区 "+product+" "+version+" "+ip);
//            lockIosRstService.addIosRst("ipcheck",product,null,null,true,null,ip,resultK.getValue().getCity(),"iplock",version);
//        }
        long ctime = System.currentTimeMillis();
        returnResult.put(uuid + new Random().nextInt(1000), System.currentTimeMillis());
        String key = "jmt" + ctime + "";
        returnResult.put(key, resultK.getValue().getLocked() + "");

        if (resultK.getKey() ||
                (resultK.getValue() != null && resultK.getValue().getIosIpRst() != null && resultK.getValue().getIosIpRst().getLoadingFlg() != null && resultK.getValue().getIosIpRst().getLoadingFlg() == 1)) {
            returnResult.put("loading" + ctime + "", resultK.getKey() + "");
            log.info("产品loading "+product+" "+ JSON.toJSONString(resultK)+" ip:"+ip);
        }

        if (StringUtils.equalsIgnoreCase("qzcyksy", product) &&
                (resultK.getValue().getLocked() || (resultK.getValue().getIosIpRst() != null && resultK.getValue().getIosIpRst().getLoadingFlg() != null && resultK.getValue().getIosIpRst().getLoadingFlg() == 1)
                )) {
            log.info("移除key直接锁死 " + product + " " + version + " " + ip);
            returnResult.remove(key);
        }

        log.info("iosfirst "+product+" "+isInstallWx + " " + version + " " + ip+" "+JSON.toJSONString(returnResult));
        return returnResult;
    }

    @Autowired
    LockIosRstService lockIosRstService;
    @Autowired
    IosLockService iosLockService;
    @RequestMapping("upd/{product}/{version}/{uuid}")
    @ResponseBody
    public ReturnResult uploadLockInfo(@PathVariable String product,
                                 @PathVariable String version,
                                 @PathVariable String uuid,
                                 HttpServletRequest request) {

        ReturnResult returnResult = new ReturnResult();
        String ip = IpUtils.getIpAddress(request);
        log.info("wx未安装 "+product+" "+ip);

        CommonHeaderDTO commonHeaderDTO = new CommonHeaderDTO();
        commonHeaderDTO.setIp(ip);
        commonHeaderDTO.setProduct(product);

        String city = iosLockService.getCityAli(commonHeaderDTO,"dws");

        lockIosRstService.addIosRst("dsp",product,uuid,ip,true,
                ip,
                ip,city,"wx未安装",version);
        return returnResult;
    }



    @RequestMapping("mst/{product}/{version}/{uuid}")
    @ResponseBody
    public ReturnResult mstuploadLockInfo(@PathVariable String product,
                                       @PathVariable String version,
                                       @PathVariable String uuid,
                                       HttpServletRequest request) {

        ReturnResult returnResult = new ReturnResult();
        String ip = IpUtils.getIpAddress(request);
        log.info("wx未安装 "+product+" "+ip);

        CommonHeaderDTO commonHeaderDTO = new CommonHeaderDTO();
        commonHeaderDTO.setIp(ip);
        commonHeaderDTO.setProduct(product);

        String city = iosLockService.getCityAli(commonHeaderDTO,"dws");

        lockIosRstService.addIosRst("dsp",product,uuid,ip,true,
                ip,
                ip,city,"wx未安装",version);
        return returnResult;
    }
}
