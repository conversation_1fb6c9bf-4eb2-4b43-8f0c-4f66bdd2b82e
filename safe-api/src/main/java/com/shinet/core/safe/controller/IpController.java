package com.shinet.core.safe.controller;


import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.shinet.core.safe.aop.CommonHeaderUtils;
import com.shinet.core.safe.dto.AreaResult;
import com.shinet.core.safe.dto.LockKeyResult;
import com.shinet.core.safe.hsq.rsp.GdIpRsp;
import com.shinet.core.safe.hsq.rsp.GdLocationRsp;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.service.GetCityRecordService;
import com.shinet.core.safe.msql.service.IpService;
import com.shinet.core.safe.msql.service.LockAreaConfigService;
import com.shinet.core.safe.msql.service.ioslock.IosLockService;
import com.shinet.core.safe.send.LBSKafKaSender;
import com.shinet.core.safe.util.ReturnResult;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;


@Controller
@RequestMapping("/sf")
@Slf4j
public class IpController {
	@Autowired
    IpService ipService;
	@Autowired
    private LockAreaConfigService lockAreaConfigService;
	@Autowired
    private GetCityRecordService getCityRecordService;

	@ApolloJsonValue("${area.skip.location.list:[\"海南\",\"北京\"]}")
    private List<String> cityList;

	@ApolloJsonValue("${area.skip.location.map:{\"690\":[\"北京\"]}}")
	private Map<String,List<String>> cityMap;

	@ApolloJsonValue("${not.limit.device.list:[\"8DD372BA-7FB3-445F-8550-030CAEC8F723\",\"C8618BD4-4FB8-46BF-9A7D-D99A5DAF731D\"]}")
	private List<String> notLimitedDevice;

    @ApolloJsonValue("${not.limit.ip.list:[\"************\"]}")
    private List<String> notLimitedIp;


	@Autowired
    private LBSKafKaSender lbsKafKaSender;

    @RequestMapping("/ip/location")
    @ResponseBody
    public ReturnResult activate(HttpServletRequest request) {
        ReturnResult returnResult = new ReturnResult();
        String ipStr = request.getParameter("ipStr");
        if(StringUtils.isNotBlank(ipStr)){
            GdIpRsp gdIpRsp = ipService.getIpLocation(ipStr);
            returnResult.setData(gdIpRsp);
        }
        return returnResult;
    }

    @RequestMapping("/ip/getCity")
    @ResponseBody
    public ReturnResult getCity(HttpServletRequest request, Integer appId,String product,String ip){
        ReturnResult returnResult = new ReturnResult();
        if (appId == null || StringUtils.isEmpty(product)){
            return returnResult;
        }
        String city = "";
        String province = "";
        CommonHeaderDTO commonHeaderDTO = CommonHeaderUtils.getHeaderVo(request);
        if (StringUtils.isNotBlank(ip)){
            log.info("City Query Set Ip {} appId {} ak {}",ip,appId,commonHeaderDTO.getDeviceId());
            commonHeaderDTO.setIp(ip);
        }

        String getSaveKey = ipService.getSaveKey(commonHeaderDTO);
        GdIpRsp gdIpRsp = ipService.getFormHBase(getSaveKey);
        if(StringUtils.isNotBlank(commonHeaderDTO.getIp())){
            if (gdIpRsp == null) {
                gdIpRsp = ipService.getIpLocation(commonHeaderDTO.getIp());
                city = gdIpRsp.getCity();
                province = gdIpRsp.getProvince();
                if (StringUtils.isNotEmpty(city) && !"[]".equals(city) && !"null".equals(city)) {
                    ipService.saveHBase(gdIpRsp, getSaveKey);
                }
            }
            city = gdIpRsp.getCity();
            province = gdIpRsp.getProvince();
            if (StringUtils.isEmpty(city) || "[]".equals(city) || "null".equals(city)) {
                city = ipService.getCityByIpCc(commonHeaderDTO.getIp());
                if (StringUtils.isNotEmpty(city) && !"中国".equals(city) && "null".equals(city)) {
                    gdIpRsp.setCity(city);
                }else {
//                    if (StringUtils.isNotEmpty(commonHeaderDTO.getGps()) && !"0,0".equals(commonHeaderDTO.getGps())) {
//                        // 经纬度补充ip
//                        GdLocationRsp.AddressComponent addressComponent = IpService.getCityByLocation(commonHeaderDTO.getGps());
//                        if(addressComponent!=null){
//                            city = addressComponent.getCity();
//                            province = addressComponent.getProvince();
//                        }
//                        if (StringUtils.isNotEmpty(city)){
//                            gdIpRsp.setCity(city);
//                            ipService.saveHBase(gdIpRsp, getSaveKey);
//                        }
//                    }
                }
            }
        }

        log.info("{} Current City:{}",commonHeaderDTO.getDeviceId(),city);

        AreaResult areaResult = new AreaResult();
        areaResult.setCity(city);
        areaResult.setProvince(province);

        if (StringUtils.isNotBlank(commonHeaderDTO.getDeviceId()) ||
                StringUtils.isNotBlank(commonHeaderDTO.getOaid()) ||
                StringUtils.isNotBlank(commonHeaderDTO.getImei())) {
            String dsp = LockAreaConfigService.userIsOCPC(commonHeaderDTO, appId, product);
            areaResult.setOcpc(!"nodsp".equals(dsp));
        }

        boolean flag = false;
        if (StringUtils.isEmpty(city)){
            areaResult.setLimitArea(false);
            returnResult.setData(areaResult);
            return returnResult;
        }

        List<String> targetCityList = cityMap.getOrDefault(appId.toString(),cityList);
        for (String cityTarget :targetCityList){
            if (cityTarget.contains(city)|| city.contains(cityTarget)){
                flag = true;
                break;
            }
        }
        areaResult.setLimitArea(flag);
        returnResult.setData(areaResult);
        if (notLimitedDevice.contains(commonHeaderDTO.getDeviceId()) || notLimitedIp.contains(commonHeaderDTO.getIp())){
            log.info("{} {} Target WhiteList",appId,commonHeaderDTO.getDeviceId());
            areaResult.setLimitArea(false);
            flag = false;
        }
        log.info("City Result {} {} {} {} RESULT OCPC {} Limit {}",appId,product,
                commonHeaderDTO.getDeviceId(),commonHeaderDTO.getIp(),
                areaResult.getOcpc(),areaResult.getLimitArea());
        if (appId.equals(716) && "ios".equalsIgnoreCase(commonHeaderDTO.getOs())){
            if (flag && !areaResult.getOcpc()){
                lockAreaConfigService.refreshLockKeyResult(appId,commonHeaderDTO,new LockKeyResult(){{
                    setLocked(true);
                    setGdIpRsp(new GdIpRsp(){{
                        setCity(areaResult.getCity());
                    }});
                }});
            }
        }
        // LBS INFO SEND TO CK
        lbsKafKaSender.sendLBSInfo(commonHeaderDTO,gdIpRsp, product,areaResult.getLimitArea());
        getCityRecordService.saveIfNotExist(commonHeaderDTO,areaResult.getCity(),areaResult.getOcpc(),areaResult.getLimitArea(),product,appId);
        return returnResult;
    }
    @Autowired
    IosLockService iosLockService;
    @RequestMapping("/ip/getCityName")
    @ResponseBody
    public ReturnResult getCityName(HttpServletRequest request, String product,String ip){
        ReturnResult returnResult = new ReturnResult();
        CommonHeaderDTO commonHeaderDTO = new CommonHeaderDTO();
        commonHeaderDTO.setIp(ip);
        commonHeaderDTO.setProduct(product);
        String city = iosLockService.getCity(commonHeaderDTO,"getCityName");
        returnResult.setData("city",city);
        return returnResult;
    }


}
