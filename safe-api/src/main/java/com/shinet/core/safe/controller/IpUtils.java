package com.shinet.core.safe.controller;

import cn.hutool.core.lang.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.ByteBuffer;

@Slf4j
public class IpUtils {

    public static String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (StringUtils.isNotBlank(ip)) {
            ip =StringUtils.substringBefore(ip, ",");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
//        String ip = request.getHeader("X-Real-IP");
//        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
//            ip = request.getHeader("x-forwarded-for");
//        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if (!StringUtils.isEmpty(ip) && ip.indexOf(",") > 0) {
            ip = ip.substring(0, ip.indexOf(",")).trim();
        }
        try {
            String ip2 = getIpDdAddress(request);
            if(!StringUtils.equalsIgnoreCase(ip,ip2)){
                log.info("获取到ip数据不一致 "+ip+" @@@ "+ip2);
            }
        }catch (Exception e){
            log.warn("",e);
        }
        return ip;
    }


    public static String getIpDdAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Real-IP");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("x-forwarded-for");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if (!StringUtils.isEmpty(ip) && ip.indexOf(",") > 0) {
            ip = ip.substring(0, ip.indexOf(",")).trim();
        }
        return ip;
    }

    /**
     * X-Forwarded-For: clientIP, proxyIP1, proxyIP2
     * @param request
     * @return
     */
    public static String getRealIpAddress(HttpServletRequest request) {
        // 这个 X-Real-IP 大部分时候是对的，但老毕说可能会获取到slb的 IP，而 x-forwarded-for 第一段ip肯定没错 。
        String ip = request.getHeader("x-forwarded-for");
        if (StringUtils.isNotBlank(ip)) {
            ip =StringUtils.substringBefore(ip, ",");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if (!StringUtils.isEmpty(ip) && ip.indexOf(",") > 0) {
            ip = ip.substring(0, ip.indexOf(",")).trim();
        }
        try {
            String ip2 = getIpDdAddress(request);
            if(!StringUtils.equalsIgnoreCase(ip,ip2)){
                log.info("获取到ip数据不一致 "+ip+" @@@ "+ip2);
            }
        }catch (Exception e){
            log.warn("",e);
        }
        return ip;
    }
    public static Pair<String,String> getLocalHostIp(){
        try {
            InetAddress ip = InetAddress.getLocalHost();
            String hostName = ip.getHostName();
            String ipStr = ip.getHostAddress();
            Pair<String,String> pair = new Pair<>(hostName,ipStr);
            return pair;
        }catch (Exception e){
            log.error("",e);
        }
        return null;
    }

    /**
     * 32位地址
     * @param cidrNotation
     */
    public static void printAllIPsInNetwork(String cidrNotation) {
        String[] parts = cidrNotation.split("/");
        String networkAddress = parts[0];
        int prefix = Integer.parseInt(parts[1]);
        //精确到ip
        if(prefix > 24) {
            int hostBits = 32 - prefix; // 计算主机位数

            int maxHostNumber = (int) Math.pow(2, hostBits) - 2; // 减去网络地址和广播地址

            String[] ipParts = networkAddress.split("\\.");
            for (int host = 1; host <= maxHostNumber; host++) {
                String ip = String.format("%d.%d.%d.%d",
                        Integer.parseInt(ipParts[0]),
                        Integer.parseInt(ipParts[1]),
                        Integer.parseInt(ipParts[2]),
                        host);
                System.out.println(ip);
            }
        }else {
            calculateIPRange(networkAddress, prefix);
        }
    }

    public static void calculateIPRange(String ip, int prefixLength) {
        System.out.println(StringUtils.substringBeforeLast(ip, ".")+".*");
        if(prefixLength < 24) {
            String[] ips = ip.split("\\.");
            StringBuilder allBinary = new StringBuilder();
            for (int i = 0; i < 3; i++) {
                String curB = Integer.toBinaryString(Integer.parseInt(ips[i]));
                for (int j = curB.length(); j < 8; j++) {
                    allBinary.append("0");
                }
                allBinary.append(curB);
            }
            for (int i = 23; i >= prefixLength; i--) {
                allBinary.setCharAt(i, '1');
                Integer ip1 = Integer.parseInt(allBinary.substring(0, 8), 2),
                        ip2 = Integer.parseInt(allBinary.substring(8, 16), 2),
                        ip3 = Integer.parseInt(allBinary.substring(16, 24), 2);
                System.out.println(ip1 + "." + ip2 + "." + ip3 + ".*");
            }
        }
    }

    public static void printAllIPsInNetwork2(String cidrNotation) {
        String[] parts = cidrNotation.split("/");
        String networkAddress = parts[0];
        int prefix = Integer.parseInt(parts[1]);
        //精确到ip
        if(prefix > 24) {
            int hostBits = 32 - prefix; // 计算主机位数

            int maxHostNumber = (int) Math.pow(2, hostBits) - 2; // 减去网络地址和广播地址

            String[] ipParts = networkAddress.split("\\.");
            for (int host = 1; host <= maxHostNumber; host++) {
                String ip = String.format("%d.%d.%d.%d",
                        Integer.parseInt(ipParts[0]),
                        Integer.parseInt(ipParts[1]),
                        Integer.parseInt(ipParts[2]),
                        host);
                System.out.println(ip);
            }
        }else {
            calculateIPRange2(networkAddress, prefix);
        }
    }

    public static void calculateIPRange2(String ip, int prefixLength) {
        String[] ips = ip.split("\\.");
        StringBuilder allBinary = new StringBuilder();
        for (int i = 0; i < 4; i++) {
            String curB = Integer.toBinaryString(Integer.parseInt(ips[i]));
            for (int j = curB.length(); j < 8; j++) {
                allBinary.append("0");
            }
            allBinary.append(curB);
        }
        int start = Integer.parseInt(allBinary.toString(), 2);
        for (int i = prefixLength; i < 32; i++) {
            allBinary.setCharAt(i, '1');
        }
        int end = Integer.parseInt(allBinary.toString(), 2);
        for (int i = start; i <= end; i++) {
            StringBuilder thisIp = new StringBuilder(Integer.toBinaryString(i));
            for (int j = thisIp.length(); j < 32; j++) {
                thisIp.insert(0, "0");
            }
            Integer ip1 = Integer.parseInt(thisIp.substring(0, 8), 2),
                    ip2 = Integer.parseInt(thisIp.substring(8, 16), 2),
                    ip3 = Integer.parseInt(thisIp.substring(16, 24), 2),
                    ip4 = Integer.parseInt(thisIp.substring(24, 32), 2);
            System.out.println(ip1 + "." + ip2 + "." + ip3 + "." + ip4);
        }
    }

    public static void main(String[] args) {
        printAllIPsInNetwork("***********/23");
        printAllIPsInNetwork("*********/20");
        printAllIPsInNetwork2("***********/23");
        printAllIPsInNetwork2("*********/20");
    }
}
