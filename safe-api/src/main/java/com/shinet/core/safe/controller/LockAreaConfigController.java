package com.shinet.core.safe.controller;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.shinet.core.safe.aop.CommonHeaderUtils;
import com.shinet.core.safe.dto.LockKeyResult;
import com.shinet.core.safe.msql.config.SafeSwitcher;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.service.LockAreaConfigService;
import com.shinet.core.safe.util.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2022/1/8
 */
@Slf4j
@RequestMapping("sf/config")
@RestController
public class LockAreaConfigController {

    @Autowired
    private LockAreaConfigService lockAreaConfigService;
    @Value("${lock.area.log:false}")
    public boolean logPrint;

    @ApolloJsonValue("${target.lock.ocpc.false.list:[841,902]}")
    private List<Integer> changeOcpcAppList;

    @Autowired
    private SafeSwitcher safeSwitcher;

    /**
     *  老锁区
     */
    @PostMapping("check")
    public ReturnResult queryLockInfo(HttpServletRequest request,String llxip, Integer appId, String pkgNames,String wfName,String ivpn){

        if (appId == null){
            return new ReturnResult();
        }

        CommonHeaderDTO commonHeaderDTO = CommonHeaderUtils.getHeaderVo(request);
        String trans = UUID.randomUUID().toString();
        if (StringUtils.isNotEmpty(llxip)){
            log.info("锁区接口传入IP 使用传入值 {} ==> {}",commonHeaderDTO.getIp(),llxip);
            commonHeaderDTO.setIp(llxip);
        }
        commonHeaderDTO.setRealIp(IpUtils.getIpAddress(request));

        boolean printLog = true;
        if (!lockAreaConfigService.getJoinChannelStartList(appId,commonHeaderDTO).contains(commonHeaderDTO.getChannel())
                || commonHeaderDTO.getChannel().startsWith("ks")
                || commonHeaderDTO.getChannel().startsWith("gdt")){
            printLog = logPrint;
        }
        if (printLog) {
            log.info("[{}]REQ-HEADER {} {} " +
                            " device:{} oaid:{} " +
                            " androidid:{} mac:{} ip:{}, av:{},ca:{} REQ-PARAM {}", trans,
                    commonHeaderDTO.getChannel(), commonHeaderDTO.getAccessKey(),
                    commonHeaderDTO.getDeviceId(),commonHeaderDTO.getOaid(),
                    commonHeaderDTO.getAndroidId(),commonHeaderDTO.getMac(),
                    commonHeaderDTO.getIp(),commonHeaderDTO.getAppVersion(),commonHeaderDTO.getCaid(), appId);
        }

        ReturnResult returnResult = new ReturnResult();
        LockKeyResult result = lockAreaConfigService.pushLockKey(commonHeaderDTO,appId,pkgNames,trans,wfName,ivpn);
        lockAreaConfigService.refreshLockKeyResult(appId,commonHeaderDTO,result);
        result.setGdIpRsp(null);
        if (changeOcpcAppList.contains(appId)){
            if (result.getLocked()){
                result.setOcpc(false);
            }
        }
        returnResult.setData(result);

        if (printLog) {
            log.info("[{}]REQ-RESULT {} {} {} " +
                            " device:{} oaid:{} " +
                            " androidid:{} mac:{} ip:{}, av:{},ca:{} RESULT {}", trans,
                    appId,commonHeaderDTO.getChannel(), commonHeaderDTO.getAccessKey(),
                    commonHeaderDTO.getDeviceId(),commonHeaderDTO.getOaid(),
                    commonHeaderDTO.getAndroidId(),commonHeaderDTO.getMac(),
                    commonHeaderDTO.getIp(),commonHeaderDTO.getAppVersion(),commonHeaderDTO.getCaid(),
                    JSON.toJSONString(result));
        }
        return returnResult;
    }
}
