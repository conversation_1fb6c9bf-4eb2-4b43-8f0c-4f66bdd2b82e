package com.shinet.core.safe.controller;

import com.alibaba.fastjson.JSON;
import com.shinet.core.safe.aop.CommonHeaderUtils;
import com.shinet.core.safe.dto.LockKeyResult;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.service.LockAreaConfigMinService;
import com.shinet.core.safe.util.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2022/1/8
 * @desc 小游戏
 */
@Slf4j
@RequestMapping("sf/minConfig")
@RestController
public class LockAreaConfigMinController {

    @Autowired
    private LockAreaConfigMinService lockAreaConfigMinService;
    @Value("${lock.area.log:false}")
    public boolean logPrint;


    @PostMapping("check")
    public ReturnResult queryLockInfo(HttpServletRequest request,String llxip, Integer appId, String pkgNames){

        if (appId == null){
            return new ReturnResult();
        }

        CommonHeaderDTO commonHeaderDTO = CommonHeaderUtils.getHeaderVo(request);
        String trans = UUID.randomUUID().toString();
        if (StringUtils.isNotEmpty(llxip)){
            log.info("小游戏锁区接口传入IP 使用传入值 {} ==> {}",commonHeaderDTO.getIp(),llxip);
            commonHeaderDTO.setIp(llxip);
        }

        boolean printLog = true;

        if (printLog) {
            log.info("[{}] REQ-HEADER-MIN {} {} " +
                        " ip:{}, av:{},ca:{} REQ-PARAM {}", trans,
                    commonHeaderDTO.getOpenId(), commonHeaderDTO.getAccessKey(),
                    commonHeaderDTO.getIp(),commonHeaderDTO.getAppVersion(),commonHeaderDTO.getCaid(), appId);
        }

        ReturnResult returnResult = new ReturnResult();
        LockKeyResult result = lockAreaConfigMinService.pushLockKey(commonHeaderDTO,appId,pkgNames,trans);
        lockAreaConfigMinService.refreshLockKeyResult(appId,commonHeaderDTO,result);
        result.setGdIpRsp(null);

        returnResult.setData(result);

        if (printLog) {
            log.info("[{}]REQ-RESULT {} {} {} " +
                            " device:{} oaid:{} " +
                            " androidid:{} mac:{} ip:{}, av:{},ca:{} RESULT {}", trans,
                    appId,commonHeaderDTO.getChannel(), commonHeaderDTO.getAccessKey(),
                    commonHeaderDTO.getDeviceId(),commonHeaderDTO.getOaid(),
                    commonHeaderDTO.getAndroidId(),commonHeaderDTO.getMac(),
                    commonHeaderDTO.getIp(),commonHeaderDTO.getAppVersion(),commonHeaderDTO.getCaid(),
                    JSON.toJSONString(result));
        }
        return returnResult;
    }
}
