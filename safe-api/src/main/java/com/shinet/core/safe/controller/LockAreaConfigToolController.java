package com.shinet.core.safe.controller;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.shinet.core.safe.aop.CommonHeaderUtils;
import com.shinet.core.safe.dto.LockKeyResult;
import com.shinet.core.safe.msql.entity.AndroidLockRst;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.service.LockAreaConfigToolService;
import com.shinet.core.safe.msql.service.androidlock.AndroidLockRstService;
import com.shinet.core.safe.msql.service.androidlock.AndroidLockService;
import com.shinet.core.safe.msql.service.androidlock.AndroidLokReq;
import com.shinet.core.safe.util.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @since 2024/4/23
 * @desc 工具类app
 */
@Slf4j
@RestController
public class LockAreaConfigToolController {

    @Autowired
    private LockAreaConfigToolService lockAreaConfigToolService;
    @Autowired
    private AndroidLockRstService androidLockRstService;
    @Autowired
    private AndroidLockService androidLockService;


    @RequestMapping("/safe/tool/lc")
    public ReturnResult queryLockInfo(AndroidLokReq androidLokReq, HttpServletRequest request){

        ReturnResult returnResult = new ReturnResult();
        try {
            CommonHeaderDTO commonHeaderDTO = CommonHeaderUtils.getHeaderVo(request);

            log.info("toollc "+ JSON.toJSONString(androidLokReq)  + " header " + JSON.toJSONString(commonHeaderDTO));


            if(StringUtils.isBlank(androidLokReq.getMac()) || StringUtils.equalsIgnoreCase("null",androidLokReq.getMac())){
                androidLokReq.setMac(null);
            }
            if(StringUtils.isBlank(androidLokReq.getAndroidId()) || StringUtils.equalsIgnoreCase("null",androidLokReq.getAndroidId())){
                androidLokReq.setAndroidId(null);
            }
            if(StringUtils.isBlank(androidLokReq.getOaid()) || StringUtils.equalsIgnoreCase("null",androidLokReq.getOaid())){
                androidLokReq.setOaid(commonHeaderDTO.getOaid());
            }
            String ip  = IpUtils.getIpAddress(request);

            Pair<LockKeyResult, AndroidLockRst> lockKeyResultPair = lockAreaConfigToolService.isToolLock(commonHeaderDTO,androidLokReq,ip);
            returnResult.setData(lockKeyResultPair.getKey());

            AndroidLockRst androidLockRst = lockKeyResultPair.getValue();
            androidLockRst.setLockFlag(lockKeyResultPair.getKey().getLocked()+"");
            androidLockRst.setIp(ip);

            androidLockRstService.saveRst(androidLokReq,androidLockRst,false);
            //刷新hbase锁区结果
            androidLockService.refreshHbase(commonHeaderDTO, androidLockRst, androidLokReq);
            log.info("访问tool "+androidLokReq.getProduct() +" "+ IpUtils.getIpAddress(request)+" "+androidLokReq.getOaid() + " "+ JSON.toJSONString(androidLockRst));
        }catch (Exception e){
            log.error("",e);
        }
        return returnResult;
    }
}
