package com.shinet.core.safe.controller;

import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.shinet.core.safe.aop.CommonHeaderUtils;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.entity.ShumeiRiskReq;
import com.shinet.core.safe.msql.service.ShumeiRiskInfoService;
import com.shinet.core.safe.util.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 数美风险识别
 */
@Slf4j
@RestController
@RequestMapping("/shumei")
public class ShuMeiRiskController {


    @Autowired
    private ShumeiRiskInfoService shumeiRiskInfoService;


    @ApolloJsonValue("${join.sm.risk.app.list:[]}")
    private List<Integer> joinShuMeiRiskInfoApp;

    @RequestMapping("/riskInfo")
    @ResponseBody
    public ReturnResult riskInfo(@RequestBody ShumeiRiskReq shumeiRiskReq, HttpServletRequest req){
        log.info("requestParam : {}", JSON.toJSONString(shumeiRiskReq));
        ReturnResult res = new ReturnResult();
        CommonHeaderDTO headerVo = CommonHeaderUtils.getHeaderVo(req);
        log.info("headVO: {}" , JSON.toJSONString(headerVo));
        try {
            boolean isRisk = true;
            if (StringUtils.isNotBlank(headerVo.getAppId())) {
                if (joinShuMeiRiskInfoApp.contains(Integer.valueOf(headerVo.getAppId()))) {
                    isRisk = shumeiRiskInfoService.riskInfo(shumeiRiskReq,headerVo);
                }
            }
            res.setData(isRisk);
            return res;
        }catch (Exception e){
            log.error("ShuMeiRiskController-riskInfo",e);
        }
        return res;
    }
}
