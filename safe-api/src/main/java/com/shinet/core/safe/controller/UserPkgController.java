package com.shinet.core.safe.controller;

import com.shinet.core.safe.msql.entity.UserExpInfo;
import com.shinet.core.safe.msql.entity.UserPkgs;
import com.shinet.core.safe.msql.service.UserExpInfoService;
import com.shinet.core.safe.msql.service.usrpkgs.UserPkgsService;
import com.shinet.core.safe.util.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@RestController
@Slf4j
public class UserPkgController {
    @Autowired
    UserPkgsService userPkgsService;
    @RequestMapping("/sf/user/pkgs")
    @ResponseBody
    public ReturnResult riskInfo(UserPkgs userPkgs, HttpServletRequest req){
//        log.info("开始接收安装包列表 : {}", JSON.toJSONString(userPkgs));
        ReturnResult res = new ReturnResult();
        userPkgsService.savePkgs(userPkgs);
        return res;
    }

    @Autowired
    UserExpInfoService userExpInfoService;
    @RequestMapping("/sf/user/uexps")
    @ResponseBody
    public ReturnResult uexps(UserExpInfo userExpInfo, HttpServletRequest req){
        ReturnResult res = new ReturnResult();
        userExpInfoService.saveUseExps(userExpInfo);
        return res;
    }
}
