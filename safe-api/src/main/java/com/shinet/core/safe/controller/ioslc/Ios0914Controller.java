package com.shinet.core.safe.controller.ioslc;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.shinet.core.safe.aop.CommonHeaderUtils;
import com.shinet.core.safe.controller.IpUtils;
import com.shinet.core.safe.dto.LockKeyResult;
import com.shinet.core.safe.msql.service.ioslock.IosLockManagerService;
import com.shinet.core.safe.util.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Random;

@Slf4j
@RestController
public class Ios0914Controller {
    @Autowired
    IosLockManagerService iosLockMag;

    @Value("${enable.ios.alternate.domain:false}")
    private boolean enableAlternateDomain;

    @Value("${alternate.domain.name}")
    private String alternateDomainName;

    @RequestMapping("topdgs/{product}/{version}/{uuid}/{zfbist}/{dyist}")
    @ResponseBody
    public HashMap topdgsockInfo(@PathVariable String product,
                                      @PathVariable String version,
                                      @PathVariable String uuid,
                                      @PathVariable String zfbist,
                                      @PathVariable String dyist,
                                      String ist,
                                      HttpServletRequest request) {


        HashMap returnResult = new HashMap();
        String ip = IpUtils.getIpAddress(request);

        /**
         * wx是否安装
         */
        boolean isInstallWx = true;
        if(StringUtils.isNotBlank(ist)){
            isInstallWx = StringUtils.startsWith(ist,"ist");
        }


        boolean isInstallZfb = true;
        if(StringUtils.isNotBlank(zfbist)){
            isInstallZfb = StringUtils.startsWith(zfbist,"ist");
        }

        if(!isInstallZfb){
            log.info("zfb未安装 "+product+" "+version);
        }

        boolean isInstallDy = true;
        if(StringUtils.isNotBlank(dyist)){
            isInstallDy = StringUtils.startsWith(ist,"ist");
        }
        if(!isInstallDy){
            log.info("dy未安装 "+product+" "+version);
        }

        String timestamp = request.getHeader("timestamp");

        Pair<Boolean, LockKeyResult> resultK = iosLockMag.iosLockMag(product, version, ip, isInstallWx, isInstallDy, timestamp);
//        if(resultK.getValue().getLocked()){
//            //锁区自动同步一份到
//            log.info("开始存储第一层锁区 "+product+" "+version+" "+ip);
//            lockIosRstService.addIosRst("ipcheck",product,null,null,true,null,ip,resultK.getValue().getCity(),"iplock",version);
//        }
        long ctime = System.currentTimeMillis();
        returnResult.put(uuid + new Random().nextInt(1000), System.currentTimeMillis());
        String key = "jmt" + ctime + "";
        returnResult.put(key, resultK.getValue().getLocked() + "");

        if (resultK.getKey() ||
                (resultK.getValue() != null && resultK.getValue().getIosIpRst() != null && resultK.getValue().getIosIpRst().getLoadingFlg() != null && resultK.getValue().getIosIpRst().getLoadingFlg() == 1)) {
            returnResult.put("loading" + ctime + "", resultK.getKey() + "");
            log.info("产品loading "+product+" "+ JSON.toJSONString(resultK)+" ip:"+ip);
        }

        if (StringUtils.equalsIgnoreCase("qzcyksy", product) &&
                (resultK.getValue().getLocked() || (resultK.getValue().getIosIpRst() != null && resultK.getValue().getIosIpRst().getLoadingFlg() != null && resultK.getValue().getIosIpRst().getLoadingFlg() == 1)
                )) {
            log.info("移除key直接锁死 " + product + " " + version + " " + ip);
            returnResult.remove(key);
        }

        // 下发域名 混淆
        if (enableAlternateDomain && StringUtils.isNotBlank(alternateDomainName)) {
            String s = Base64.getEncoder().encodeToString(alternateDomainName.getBytes(StandardCharsets.UTF_8));
            returnResult.put("ack" + uuid + new Random().nextInt(1000), s);
        }

        log.info("iosfirst "+product+" "+isInstallWx + " " + version + " " + ip+" "+JSON.toJSONString(returnResult));
        return returnResult;
    }

    @RequestMapping("games/{product}/{version}")
    @ResponseBody
    public ReturnResult iosLockV2(@PathVariable String product,
                                 @PathVariable String version,
                                 HttpServletRequest request) {
        ReturnResult returnResult = new ReturnResult();

        String ip = IpUtils.getIpAddress(request);

        String deviceInfo = CommonHeaderUtils.getDeviceInfoFromHeader(request);
        if (StringUtils.isNotBlank(deviceInfo)) {
            log.info("iosFirst product:{} version:{} ip:{} device:{}", product, version, ip, deviceInfo);
        }

        /**
         * wx是否安装
         */
        boolean isInstallWx = true;

        boolean isInstallZfb = true;

        boolean isInstallDy = true;

        String timestamp = request.getHeader("timestamp");

        Pair<Boolean, LockKeyResult> resultK = iosLockMag.iosLockMag(product, version, ip, isInstallWx, isInstallDy, timestamp);

        String key = new Random().nextInt(10000) + product;
        returnResult.setData(key, resultK.getValue().getLocked() + "");

        if (StringUtils.equalsIgnoreCase("qzcyksy", product) &&
                (resultK.getValue().getLocked() || (resultK.getValue().getIosIpRst() != null && resultK.getValue().getIosIpRst().getLoadingFlg() != null && resultK.getValue().getIosIpRst().getLoadingFlg() == 1)
                )) {
            log.info("移除key直接锁死 " + product + " " + version + " " + ip);
            returnResult.removeData(key);
        }

        // 下发域名 混淆
        if (enableAlternateDomain && StringUtils.isNotBlank(alternateDomainName)) {
            String s = Base64.getEncoder().encodeToString(alternateDomainName.getBytes(StandardCharsets.UTF_8));
            returnResult.setData("ack" + new Random().nextInt(1000), s);
        }

        returnResult.setData(UUID.fastUUID().toString() + new Random().nextInt(1000), System.currentTimeMillis());

        log.info("iosfirst "+product+" "+isInstallWx + " " + version + " " + ip+" "+JSON.toJSONString(returnResult));
        return returnResult;
    }

    @RequestMapping("apples/{product}/{version}")
    @ResponseBody
    public ReturnResult iosLockV3(@PathVariable String product,
                                  @PathVariable String version,
                                  HttpServletRequest request) {
        ReturnResult returnResult = new ReturnResult();

        String ip = IpUtils.getIpAddress(request);

        String deviceInfo = CommonHeaderUtils.getDeviceInfoFromHeader(request);
        if (StringUtils.isNotBlank(deviceInfo)) {
            log.info("iosFirst product:{} version:{} ip:{} device:{}", product, version, ip, deviceInfo);
        }

        /**
         * wx是否安装
         */
        boolean isInstallWx = true;

        boolean isInstallZfb = true;

        boolean isInstallDy = true;

        String timestamp = request.getHeader("timestamp");

        Pair<Boolean, LockKeyResult> resultK = iosLockMag.iosLockMag(product, version, ip, isInstallWx, isInstallDy, timestamp);

        String key = new Random().nextInt(10000) + product;
        returnResult.setData(key, resultK.getValue().getLocked() + "");

        if (StringUtils.equalsIgnoreCase("qzcyksy", product) &&
                (resultK.getValue().getLocked() || (resultK.getValue().getIosIpRst() != null && resultK.getValue().getIosIpRst().getLoadingFlg() != null && resultK.getValue().getIosIpRst().getLoadingFlg() == 1)
                )) {
            log.info("移除key直接锁死 " + product + " " + version + " " + ip);
            returnResult.removeData(key);
        }

        // 下发域名 混淆
        if (enableAlternateDomain && StringUtils.isNotBlank(alternateDomainName)) {
            String s = Base64.getEncoder().encodeToString(alternateDomainName.getBytes(StandardCharsets.UTF_8));
            returnResult.setData("ack" + new Random().nextInt(1000), s);
        }

        returnResult.setData(UUID.fastUUID().toString() + new Random().nextInt(1000), System.currentTimeMillis());

        log.info("iosfirst "+product+" "+isInstallWx + " " + version + " " + ip+" "+JSON.toJSONString(returnResult));
        return returnResult;
    }
}
