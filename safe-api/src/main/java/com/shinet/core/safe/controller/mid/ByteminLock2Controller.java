package com.shinet.core.safe.controller.mid;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.shinet.core.safe.aop.CommonHeaderUtils;
import com.shinet.core.safe.controller.IpUtils;
import com.shinet.core.safe.dto.LockKeyResult;
import com.shinet.core.safe.msql.config.SafeSwitcher;
import com.shinet.core.safe.msql.entity.AndroidLockRst;
import com.shinet.core.safe.msql.entity.ByteminLockRst;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.service.androidlock.AndroidLockRstService;
import com.shinet.core.safe.msql.service.androidlock.AndroidLockService;
import com.shinet.core.safe.msql.service.androidlock.AndroidLokReq;
import com.shinet.core.safe.msql.service.bytemin.ByteminLockService;
import com.shinet.core.safe.msql.service.bytemin.ByteminLokReq;
import com.shinet.core.safe.util.ReturnResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestController
public class ByteminLock2Controller {
    @Autowired
    ByteminLockService byteminLockService;

    @Autowired
    SafeSwitcher safeSwitcher;
    @RequestMapping("/sf/byrd/milc")
    @ResponseBody
    public ReturnResult milc(ByteminLokReq byteminLokReq,HttpServletRequest request) {

        ReturnResult returnResult = new ReturnResult();
        try {
            CommonHeaderDTO commonHeaderDTO = CommonHeaderUtils.getHeaderVo(request);
            if(safeSwitcher.logFlag){
                log.info("byteminlockreq "+ JSON.toJSONString(byteminLokReq)  + " header " + JSON.toJSONString(commonHeaderDTO));
            }
            String ip  = IpUtils.getIpAddress(request);

            Pair<LockKeyResult, ByteminLockRst>  lockRstPair = byteminLockService.isByteminLock(commonHeaderDTO,byteminLokReq,ip);
            returnResult.setData(lockRstPair.getKey());
        }catch (Exception e){
            log.error("",e);
        }
        return returnResult;
    }

}
