package com.shinet.core.safe.filter;

import com.pepper.metrics.core.Profiler;
import com.pepper.metrics.core.Stats;
import com.pepper.metrics.integration.servlet.utils.HttpUtil;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

public class SafePerfFilter implements Filter {

    private static final Stats PROFILER_STAT = Profiler.Builder.builder().type("http").subType("in").namespace("default").build();
    private static final Stats PROFILER_STAT_HTTPSTATUS = Profiler.Builder.builder().type("http-status").subType("in").namespace("default").build();

    public SafePerfFilter() {
    }

    public void init(FilterConfig filterConfig) throws ServletException {
    }

    private String getSafeUrl(String uri) {
        if (uri == null || uri.trim().isEmpty()) {
            return HttpUtil.getPatternUrl(uri);
        }
        //IosLockController Ios0914Controller
        if(uri.contains("/")) {
            String[] parts = StringUtils.split(uri, '/');
            if (parts != null && parts.length > 0 && !parts[0].isEmpty()) {
                String first = StringUtils.split(uri, '/')[0];
                if (Arrays.asList("cost", "oiumk", "upd", "mst", "topdgs").contains(first)) {
                    return "/" + first + "/xxx/";
                }
            }
        }
        return HttpUtil.getPatternUrl(uri);
    }

    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest sRequest = (HttpServletRequest)servletRequest;
        HttpServletResponse sResponse = (HttpServletResponse)servletResponse;
        String url = getSafeUrl(sRequest.getRequestURI());
        long begin = System.currentTimeMillis();
        String[] tags = new String[]{"method", sRequest.getMethod(), "url", url, "type", "exception"};
        PROFILER_STAT.incConc(tags);
        boolean var17 = false;

        try {
            var17 = true;
            filterChain.doFilter(servletRequest, servletResponse);
            var17 = false;
        } catch (ServletException | IOException var18) {
            PROFILER_STAT.error(tags);
            throw var18;
        } finally {
            if (var17) {
                PROFILER_STAT.decConc(tags);
                String httpStatus = String.valueOf(sResponse.getStatus());
                String[] httpStatusTags = new String[]{"method", sRequest.getMethod(), "url", url, "type", "status", "status", httpStatus};
                PROFILER_STAT.observe(System.currentTimeMillis() - begin, TimeUnit.MILLISECONDS, tags);
                PROFILER_STAT_HTTPSTATUS.observe(System.currentTimeMillis() - begin, TimeUnit.MILLISECONDS, httpStatusTags);
            }
        }

        PROFILER_STAT.decConc(tags);
        String httpStatus = String.valueOf(sResponse.getStatus());
        String[] httpStatusTags = new String[]{"method", sRequest.getMethod(), "url", url, "type", "status", "status", httpStatus};
        PROFILER_STAT.observe(System.currentTimeMillis() - begin, TimeUnit.MILLISECONDS, tags);
        PROFILER_STAT_HTTPSTATUS.observe(System.currentTimeMillis() - begin, TimeUnit.MILLISECONDS, httpStatusTags);
    }

    public void destroy() {
    }
}
