package com.shinet.core.safe.send;

import com.mysql.cj.util.StringUtils;
import com.shinet.core.safe.hsq.rsp.GdIpRsp;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.vo.LBSBean;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.Properties;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @since 2021/6/3
 */
@Slf4j
@Component
public class LBSKafKaSender implements InitializingBean {

    private KafkaProducer<String, String> producer;
    private AtomicLong counter = new AtomicLong(0);


    @Override
    public void afterPropertiesSet() throws Exception {
        Properties props = new Properties();
        //设置接入点，请通过控制台获取对应Topic的接入点。
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, "alikafka-pre-cn-tl32lya4k00m-1-vpc.alikafka.aliyuncs.com:9092," +
                "alikafka-pre-cn-tl32lya4k00m-2-vpc.alikafka.aliyuncs.com:9092," +
                "alikafka-pre-cn-tl32lya4k00m-3-vpc.alikafka.aliyuncs.com:9092");
        //消息队列Kafka版消息的序列化方式。
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, "org.apache.kafka.common.serialization.StringSerializer");
        //请求的最长等待时间。
        props.put(ProducerConfig.MAX_BLOCK_MS_CONFIG, 30 * 1000);
        props.put(ProducerConfig.RETRIES_CONFIG, 5);
        props.put(ProducerConfig.ACKS_CONFIG, "1");
        //构造Producer对象，注意，该对象是线程安全的。
        //一般来说，一个进程内一个Producer对象即可。如果想提高性能，可构造多个对象，但最好不要超过5个。
        this.producer = new KafkaProducer<>(props);
        CompletableFuture.runAsync(() ->{
            while (true) {
                try {
                    TimeUnit.SECONDS.sleep(1);
                    long sec = counter.getAndSet(0);
                    log.info("LBS SENDER MSG.SEC={}", sec);
                } catch (InterruptedException e) {
                    log.error("", e);
                }
            }
        });
    }

    private void send(String msg){
        try {
            ProducerRecord<String, String> kafkaMessage = new ProducerRecord<>("lbs_detail", msg);
            producer.send(kafkaMessage, (recordMetadata, e) -> counter.incrementAndGet());
        }catch (Exception e){
            log.error("Error:",e);
        }

    }

    public void sendLBSInfo(CommonHeaderDTO commonHeaderDTO, GdIpRsp gdIpRsp, String product,Boolean isLimit){
        if (StringUtils.isNullOrEmpty(gdIpRsp.getCity())){
            return;
        }
        if (StringUtils.isNullOrEmpty(commonHeaderDTO.getAccessKey())){
            return;
        }
        if (commonHeaderDTO.getUserId() == 0L){
            return;
        }

        LBSBean lbsBean = LBSBean.convert(commonHeaderDTO,gdIpRsp,product,isLimit);
        send(lbsBean.toString());
    }
}
