package com.shinet.core.safe.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * 设备拉黑批量新增请求参数
 * 
 * <AUTHOR>
 * @since 2025-01-24
 */
@Data
@ApiModel(value = "DeviceBlackBatchAddVo", description = "设备拉黑批量新增请求参数")
public class DeviceBlackBatchAddVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "操作系统：android/ios", required = true, example = "android")
    @NotBlank(message = "操作系统不能为空")
    @Pattern(regexp = "^(android|ios)$", message = "操作系统只能是android或ios")
    private String os;

    @ApiModelProperty(value = "设备类型：1-IP, 2-OAID, 3-CAID, 4-IDFA", required = true, example = "1")
    @NotNull(message = "设备类型不能为空")
    private Integer targetType;

    @ApiModelProperty(value = "逗号分隔的设备列表（最大1000条）", required = true, 
                     example = "***********,***********,***********")
    @NotBlank(message = "设备列表不能为空")
    private String targetListStr;

    @ApiModelProperty(value = "拉黑原因备注", example = "恶意刷量设备")
    private String remark;


    /**
     * 获取设备类型描述
     */
    public String getTargetTypeDesc() {
        switch (targetType) {
            case 1:
                return "IP地址";
            case 2:
                return "OAID(Android专用)";
            case 3:
                return "CAID(iOS专用)";
            case 4:
                return "IDFA(iOS专用)";
            default:
                return "未知类型";
        }
    }
}
