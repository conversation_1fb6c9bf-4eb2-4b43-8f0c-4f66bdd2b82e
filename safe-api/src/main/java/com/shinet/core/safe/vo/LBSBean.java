package com.shinet.core.safe.vo;

import com.alibaba.fastjson.JSON;
import com.shinet.core.safe.hsq.rsp.GdIpRsp;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import lombok.Data;

@Data
public class LBSBean {
    private String product;
    private String deviceId;
    private Long userId;
    private String imei;
    private String oaid;
    private String mac;
    private String ip;
    private String city;
    private String androidId;
    private String model;
    private String brand;
    private String appVersion;
    private String channel;
    private String osVersion;
    private String appId;
    private Boolean isLimited;

    public static LBSBean convert(CommonHeaderDTO commonHeaderDTO, GdIpRsp gdIpRsp, String product,Boolean isLimited){
        LBSBean lbsBean = new LBSBean();
        lbsBean.setProduct(product);
        lbsBean.setDeviceId(commonHeaderDTO.getDeviceId());
        lbsBean.setImei(commonHeaderDTO.getImei());
        lbsBean.setOaid(commonHeaderDTO.getOaid());
        lbsBean.setAndroidId(commonHeaderDTO.getAndroidId());
        lbsBean.setMac(commonHeaderDTO.getMac());
        lbsBean.setChannel(commonHeaderDTO.getChannel());
        lbsBean.setAppId(commonHeaderDTO.getAppId());
        lbsBean.setUserId(commonHeaderDTO.getUserId());
        lbsBean.setAppVersion(commonHeaderDTO.getAppVersion());
        lbsBean.setBrand(commonHeaderDTO.getBrand());
        lbsBean.setCity(gdIpRsp.getCity());
        lbsBean.setIp(commonHeaderDTO.getIp());
        lbsBean.setModel(commonHeaderDTO.getModel());
        lbsBean.setOsVersion(commonHeaderDTO.getOsVersion());
        lbsBean.setIsLimited(isLimited);

        return lbsBean;
    }

    public String toString(){
        return JSON.toJSONString(this);
    }

    public LBSBean parseFromJs(String js){
        return JSON.parseObject(js,LBSBean.class);
    }
}
