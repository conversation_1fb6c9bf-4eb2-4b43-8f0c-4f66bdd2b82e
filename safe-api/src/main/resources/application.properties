mybatis-plus.typeAliasesPackage = com.shinet.core.safe.mapper
mybatis-plus.mapper-locations = classpath*:mapper/*.xml
#mybatis-plus.configuration.log-impl = org.apache.ibatis.logging.stdout.StdOutImpl
management.endpoints.web.exposure.include=*
server.tomcat.max-http-form-post-size=6291456
spring.http.multipart.max-request-size=10Mb
server.max-http-header-size=102400
server.port = 8080
app.logging.path = logs/safe-api

spring.application.name = safe-api

apollo.bootstrap.enabled=true
apollo.bootstrap.eagerLoad.enabled=true
apollo.meta=http://*************:8288,http://*************:8288
app.id=core-safe

spring.redis.cluster.nodes=user-even-redis001.shinet-inc.com:9720,user-even-redis002.shinet-inc.com:9720,user-even-redis003.shinet-inc.com:9720
spring.redis.lettuce.pool.max-active = 100
spring.redis.lettuce.pool.max-wait = -1
spring.redis.lettuce.pool.max-idle = 8
spring.redis.lettuce.pool.min-idle = 0
spring.redis.timeout = 2000

spring.redis2.cluster.nodes=safe-data-redis001.shinet-inc.com:9720,safe-data-redis002.shinet-inc.com:9720,safe-data-redis003.shinet-inc.com:9720
#spring.redis2.cluster.nodes=*************:9720,*************:9720,*************:9720
spring.redis2.lettuce.pool.max-active = 100
spring.redis2.lettuce.pool.max-wait = -1
spring.redis2.lettuce.pool.max-idle = 8
spring.redis2.lettuce.pool.min-idle = 0
spring.redis2.timeout = 2000