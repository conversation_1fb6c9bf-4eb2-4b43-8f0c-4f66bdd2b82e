<?xml version="1.0" encoding="UTF-8"?>
<!-- 日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，则低于WARN的信息都不会输出 -->
<!-- scan:当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true -->
<!-- scanPeriod:设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。 -->
<!-- debug:当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。 -->
<configuration  scan="true" scanPeriod="10 seconds">
    <include resource="com/shinet/core/base/logback-spring.xml" />
    <root level="info">
        <appender-ref ref="INFO_FILE" />
        <appender-ref ref="stdout" />
        <appender-ref ref="aliyun" />
        <appender-ref ref="ERROR_FILE" />
    </root>
    <logger name="com.coohua" level="INFO">
    </logger>
    <logger name="org.springframework" level="INFO">
    </logger>

</configuration>