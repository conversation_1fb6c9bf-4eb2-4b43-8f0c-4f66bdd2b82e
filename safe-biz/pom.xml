<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.shinet.core</groupId>
		<artifactId>core-safe</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<groupId>com.shinet.core</groupId>
	<artifactId>safe-biz</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>safe-biz</name>
	<description>safe service</description>
	<properties>
		<java.version>1.8</java.version>
	</properties>
	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
		</dependency>
		<!-- JAVA Maven依赖 -->
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-saf</artifactId>
			<version>1.0.2</version>
		</dependency>
		<dependency>
			<groupId>com.coohua</groupId>
			<artifactId>bp-data-retrieve-api</artifactId>
			<version>1.0.5-RELEASE</version>
		</dependency>
		<dependency>
			<groupId>com.coohua.core</groupId>
			<artifactId>user-ad-api</artifactId>
			<version>0.2.0-RELEASE</version>
		</dependency>
		<dependency>
			<groupId>com.coohua</groupId>
			<artifactId>bp-user-api</artifactId>
			<version>1.4.2-RELEASE</version>
		</dependency>
		<dependency>
			<groupId>com.coohua</groupId>
			<artifactId>bp-account-api</artifactId>
			<version>1.0.7</version>
		</dependency>

		<dependency>
			<groupId>com.shinet.core.base</groupId>
			<artifactId>base-core</artifactId>
			<version>0.1.5</version>
		</dependency>
		<dependency>
			<groupId>com.shinet.core</groupId>
			<artifactId>safe-core</artifactId>
			<version>${revision}</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
		</dependency>

		<dependency>
			<groupId>com.aliyun.hbase</groupId>
			<artifactId>alihbase-client</artifactId>
			<version>2.8.2</version>
			<exclusions>
				<exclusion>
					<artifactId>slf4j-log4j12</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.github.ben-manes.caffeine</groupId>
			<artifactId>caffeine</artifactId>
			<version>2.9.2</version>
		</dependency>
	</dependencies>
</project>
