package com.shinet.core.safe.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 统一异步线程池配置
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@Configuration
@EnableAsync
public class AsyncThreadPoolConfig {

    /**
     * 核心业务线程池
     */
//    @Bean("coreBusinessExecutor")
//    public ThreadPoolTaskExecutor coreBusinessExecutor() {
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//
//        executor.setCorePoolSize(4);           // 核心线程数：应对常规流量
//        executor.setMaxPoolSize(10);           // 最大线程数：应对5倍峰值流量
//        executor.setQueueCapacity(800);        // 队列容量：32秒缓冲时间
//        executor.setKeepAliveSeconds(60);      // 线程存活时间
//        executor.setThreadNamePrefix("CoreBusiness-");
//        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//
//        executor.initialize();
//        log.info("核心业务线程池初始化完成: core={}, max={}, queue={}", 4, 10, 800);
//        return executor;
//    }

    /**
     * 数据持久化线程池
     * 用途：日志记录、数据库写入等IO密集型任务
     */
    @Bean("dataPersistenceExecutor")
    public ThreadPoolTaskExecutor dataPersistenceExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        executor.setCorePoolSize(4);
        executor.setMaxPoolSize(8);
        executor.setQueueCapacity(2000);
        executor.setKeepAliveSeconds(120);
        executor.setThreadNamePrefix("DataPersistence-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        executor.initialize();
        log.info("数据持久化线程池初始化完成: core={}, max={}, queue={}", 2, 6, 2000);
        return executor;
    }

    /**
     * 消息发送线程池
     * 用途：Kafka发送、通知推送等轻量级任务
     */
//    @Bean("messageSendingExecutor")
//    public ThreadPoolTaskExecutor messageSendingExecutor() {
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//
//        executor.setCorePoolSize(1);           // 核心线程数：轻量级任务
//        executor.setMaxPoolSize(3);            // 最大线程数：应对突发消息
//        executor.setQueueCapacity(500);        // 队列容量：消息缓冲
//        executor.setKeepAliveSeconds(60);      // 线程存活时间
//        executor.setThreadNamePrefix("MessageSending-");
//        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
//
//        executor.initialize();
//        log.info("消息发送线程池初始化完成: core={}, max={}, queue={}", 1, 3, 500);
//        return executor;
//    }

    /**
     * IP查询专用线程池
     * 用途：统一IP查询服务的并行API调用和缓存操作
     */
    @Bean("ipQueryExecutor")
    public ThreadPoolTaskExecutor ipQueryExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        executor.setCorePoolSize(6);           // 核心线程数：支持并行API调用
        executor.setMaxPoolSize(15);           // 最大线程数：应对突发查询请求
        executor.setQueueCapacity(1000);       // 队列容量：缓冲IP查询请求
        executor.setKeepAliveSeconds(120);     // 线程存活时间：IP查询间隔较长
        executor.setThreadNamePrefix("IpQuery-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        executor.initialize();
        log.info("IP查询线程池初始化完成: core={}, max={}, queue={}", 6, 15, 1000);
        return executor;
    }

}
