package com.shinet.core.safe.constant;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;

public class DeviceConstants {

    private static List<String> IOS_INVALID_DEVICE_ID = Arrays.asList("null","0","0000-0000-0000-0000");

    private static boolean allZero(String deviceId) {
        return deviceId.matches("^[0-]+$");
    }

    public static boolean validIosDeviceId(String deviceId) {
        return StringUtils.isNotBlank(deviceId) && !DeviceConstants.IOS_INVALID_DEVICE_ID.contains(deviceId);
    }

//    public static boolean validAndroidDeviceId(String deviceId) {
//        return StringUtils.isNotBlank(deviceId) && StringUtils.equalsAny("null") && notAllZero(deviceId);
//    }

    public static boolean validDeviceId(String deviceId) {
        boolean res = StringUtils.isBlank(deviceId) || StringUtils.equalsAny("null", deviceId) || allZero(deviceId);
        return !res;
    }

    public static void main(String[] args) {
        System.out.println(allZero("0"));
        System.out.println(allZero("0000000000000000000000000000000000000000000000000000000000000000"));
        System.out.println(allZero("00000000-0000-0000-0000-000000000000"));
        System.out.println(allZero("0000a000-0000-0000-0000-000000000000"));
        System.out.println(allZero("000001--------------0"));
    }
}
