package com.shinet.core.safe.container;

import com.shinet.core.safe.dto.BasicApp;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


@Slf4j
public class AppBuilder {
    public static Map<Integer, BasicApp> basicMap = new ConcurrentHashMap<>();
    public static Map<String, BasicApp> productMap = new ConcurrentHashMap<>();

    private final static BasicApp DEFAULT_APP = new BasicApp(){{
        setAppId(0);
        setProduct("NONE");
        setProductName("未知产品");
    }};

    public static void refreshBasicApp(List<BasicApp> appList){
        log.info("===> Start Refresh App RoadMap..");
        if (appList.size() >0){
            if (appList.size() >= basicMap.values().size()){
                basicMap = appList.stream().collect(Collectors.toMap(BasicApp::getAppId,r->r,(r1,r2)->r2));
            }
            if (appList.size() >= productMap.values().size()){
                productMap = appList.stream().collect(Collectors.toMap(BasicApp::getProduct,r->r,(r1,r2)->r2));
            }
        }
        log.info("===> Complete Refresh App RoadMap..");
    }

    public static BasicApp getById(Integer id){
        return basicMap.get(id);
    }

    public static BasicApp getByProduct(String product){
        return productMap.getOrDefault(product,DEFAULT_APP);
    }

    public static BasicApp like(String appName) {
        for (BasicApp app : basicMap.values()) {
            if (appName.contains(app.desc())) {
                return app;
            }
        }
        return null;
    }


    public static List<BasicApp> values() {
        return new ArrayList<>(basicMap.values());
    }
}
