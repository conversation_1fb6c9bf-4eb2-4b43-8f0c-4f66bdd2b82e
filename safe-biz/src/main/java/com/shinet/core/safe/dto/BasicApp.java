package com.shinet.core.safe.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@AllArgsConstructor
@NoArgsConstructor
@Data
public class BasicApp {

    public final static BasicApp MASTER = new BasicApp(){{
        setAppId(-1);
        setProductName("广告中台");
    }};


    private String product;
    private Integer appId;
    private String productName;
    private String pkg;
    private String productGroup;

    public Integer appId(){
        return this.appId;
    }

    public String desc() {
        return this.productName;
    }
}
