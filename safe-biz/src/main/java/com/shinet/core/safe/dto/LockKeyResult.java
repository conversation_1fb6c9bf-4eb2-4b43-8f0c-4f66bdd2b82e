package com.shinet.core.safe.dto;

import com.shinet.core.safe.hsq.rsp.GdIpRsp;
import com.shinet.core.safe.msql.entity.IosIpRst;
import com.shinet.core.safe.msql.service.androidlock.OcpcRsp;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2022/1/8
 */
@Data
public class LockKeyResult {
    private String lockKey;
    private Boolean locked;
    private Boolean ocpc;
    private Boolean isShowNofy = false;
    private Set<String> installedPkgList;
    private Set<String> skipEventActionList;
    private Boolean enableCheckQuickApp;
    private Boolean enableInterceptQuickApp;
    private GdIpRsp gdIpRsp;
    private Integer sdkInitType;
    private transient String city;
    private transient IosIpRst iosIpRst;
    private transient OcpcRsp ocpcRsp;

    public LockKeyResult build(){
        LockKeyResult result = new LockKeyResult();
        result.setLocked(this.getLocked());
        result.setLockKey(this.getLockKey());
        result.setOcpc(this.getOcpc());
        result.setGdIpRsp(this.getGdIpRsp());
        result.setCity(this.getCity());
        return result;
    }

    public void clear() {
        this.ocpcRsp = null;
    }
}
