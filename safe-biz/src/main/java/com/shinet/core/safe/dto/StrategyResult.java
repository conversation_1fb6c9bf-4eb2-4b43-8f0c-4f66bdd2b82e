package com.shinet.core.safe.dto;

import com.shinet.core.safe.msql.enums.WhiteStrategyEnums;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class StrategyResult {

    private boolean whiteFlag;
    //使用的设备id
    private String resDeviceId;
    //非锁区原因
    private WhiteStrategyEnums reason;
    //非锁区原因类型
    private String reasonType;
    private String reasonValue;

    public String getRemark() {
        if(whiteFlag) {
            return reasonValue + " " + reasonType;
        }
        return null;
    }
}
