package com.shinet.core.safe.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2021/11/22
 */
public enum ActiveChannel {
    NONE(0,"无法识别的Channel"),
    TT(1,"toutiao"),
    KS(2,"kuaishou"),
    GDT(3,"guangdiantong"),
    VIVO(4,"vivo"),
    HW(5,"huawei"),
    XM(6,"xiaomi"),
    OPPO(7,"oppo"),
    YYB(8,"yingyongbao"),
    NLX(9,"neilaxin"),
    INNER_OLD(10,"INNER_OLD_PULL"),
    INNER_DRAINAGE(11,"INNER_DRAINAGE"),
    AL_ZR(12,"自然ALIYUN量"),
    ZR(13,"自然量"),
    INNER_VIDEO(14,"INNER_VIDEO"),
    BAIDU(15,"baidufeed"),
    ALI(15,"ali"),
    ;

    private Integer type;
    private String channel;

    ActiveChannel(Integer type, String channel) {
        this.type = type;
        this.channel = channel;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    /**
     * 所有投放渠道：指我方付费买量渠道，包含多家平台
     * @return 投放渠道List
     */
    private static List<ActiveChannel> ocpcChannel(){
        return Arrays.asList(
                ActiveChannel.TT,
                ActiveChannel.KS,
                ActiveChannel.GDT,
                ActiveChannel.VIVO,
                ActiveChannel.HW,
                ActiveChannel.XM,
                ActiveChannel.OPPO,
                ActiveChannel.YYB,
                ActiveChannel.BAIDU,
                ActiveChannel.ALI
        );
    }

    private static final Map<String, ActiveChannel> basicDescMap = Arrays.stream(ActiveChannel.values())
            .collect(Collectors.toMap(ActiveChannel::getChannel, a->a,(a1, a2) ->a1));

    private static final Map<Integer, ActiveChannel> basicTypeMap = Arrays.stream(ActiveChannel.values())
            .collect(Collectors.toMap(ActiveChannel::getType, a->a,(a1, a2) ->a1));

    public static ActiveChannel getByType(Integer type){
        return basicTypeMap.getOrDefault(type, ActiveChannel.NONE);
    }

    public static ActiveChannel getByDesc(String source){
        return basicDescMap.getOrDefault(source, ActiveChannel.NONE);
    }

    public static boolean isOCPCChanel(ActiveChannel channel){
        return ocpcChannel().contains(channel);
    }
}
