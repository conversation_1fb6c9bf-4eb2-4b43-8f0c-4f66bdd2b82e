package com.shinet.core.safe.enums;

import java.io.Serializable;


public enum IosProductStatus implements Serializable {
    SHENHE("LOCK", "全锁"),
    LOCKOPEN("LOCKOPEN", "ocpc半锁"),
    ;

    public String value;
    public String name;

    IosProductStatus(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static IosProductStatus getType(String value) {
        IosProductStatus[] payOrderStatuses = IosProductStatus.values();
        for (IosProductStatus payOrderStatus : payOrderStatuses) {
            if (payOrderStatus.value.equals(value)) {
                return payOrderStatus;
            }
        }
        return SHENHE;
    }

}
