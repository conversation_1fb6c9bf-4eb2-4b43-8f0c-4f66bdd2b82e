package com.shinet.core.safe.enums;

/**
 * <AUTHOR>
 * @since 2021/11/22
 */
public enum StoreNameEnums {
    vivo("vivo","vivo"),
    oppo("oppo","oppo"),
    xiaomi("mi","mi"),
    huawei("huawei","huawei"),
    honor("honor","honor"),

    baidu("bd","bd"),
    gdt("gdt","gdt"),
    ks("ks","ks"),
    tt("tt","tt"),
    csj("csj","csj"),
    dy("dy","dy"),
    neilaxin("neilaxin","neilaxin"),
    update1("update1","update1"),
    yingyongbao("yingyongbao","yingyongbao"),

    ziran("ziran","ziran"),

    // 默认渠道，获取拉黑配置时，非mi、华为等渠道，走这个黑名单拉黑
    DEFAULT_STORE("default_store", "default")
    ;

    public String val;
    public String channel;

    StoreNameEnums(String val, String channel) {
        this.val = val;
        this.channel = channel;
    }

}
