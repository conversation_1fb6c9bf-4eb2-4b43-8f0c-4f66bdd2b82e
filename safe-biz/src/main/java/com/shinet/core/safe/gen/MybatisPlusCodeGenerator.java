package com.shinet.core.safe.gen;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.core.exceptions.MybatisPlusException;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.Scanner;

/**
 * 代码生成器
 */
@Slf4j
public class MybatisPlusCodeGenerator {

    // 代码生成后文件所在位置
	private static final String PARENT_PACKAGE = "com.shinet.core.safe.msql";
    private static final String TARGET_PACKAGE = System.getProperty("user.dir") + "/safe-biz/src/main/java";

    /**
     * 主方法
     */
    public static void main(String[] args) {
        String[] invoke = scanner("表名称：").split(",");
        if (invoke.length > 0) {
            for (String include : invoke) {
                product(include);
            }
        }
    }

    private static void product(String include) {
        // 代码生成器
        AutoGenerator autoGenerator = new AutoGenerator();

        // 全局配置
        autoGenerator.setGlobalConfig(new GlobalConfig()
                .setOutputDir(TARGET_PACKAGE)
                .setFileOverride(true)
                .setOpen(false) // 生成后是否打开outputDir
                .setAuthor("administrator")
                .setSwagger2(true)
                .setServiceImplName("%sService")
                // AUTO : AUTO(0, “数据库ID自增”),
                // INPUT : INPUT(1, “用户输入ID”),
                // ID_WORKER : ID_WORKER(2, “全局唯一ID”),
                // UUID : UUID(3, “全局唯一ID”),
                // NONE : NONE(4, “该类型为未设置主键类型”),
                // ID_WORKER_STR : ID_WORKER_STR(5, “字符串全局唯一ID”);
                .setIdType(IdType.INPUT)
                // 不使用Java8
                .setDateType(DateType.ONLY_DATE)
        );

        // 数据源配置
        autoGenerator.setDataSource(new DataSourceConfig()
                .setDbType(DbType.MYSQL)
                .setDriverName("com.mysql.jdbc.Driver")
                // data ods
//				.setUrl("*************************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=UTC")
//				.setUsername("dispenseus")
//				.setPassword("yI2gbNL1PneDLscj")


//				.setUrl("****************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=UTC")
//				.setUsername("alertus")
//				.setPassword("alertus!@#$1234")

                .setUsername("safeacc")
                .setPassword("safeacc!@#$1234")
                .setUrl("**********************************************************************? useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=UTC")

		);

        // 包名配置
        autoGenerator.setPackageInfo(new PackageConfig()
                .setParent(PARENT_PACKAGE)// 自定义包路径
                .setServiceImpl("service")
        );

        // 策略配置
        autoGenerator.setStrategy(new StrategyConfig()
                .setNaming(NamingStrategy.underline_to_camel)
                .setColumnNaming(NamingStrategy.underline_to_camel)
                .setEntityLombokModel(true)
                .setRestControllerStyle(true)
                .setInclude(include)
                .setControllerMappingHyphenStyle(true)
        );

		/*
		// 自定义配置
		InjectionConfig injectionConfig = new InjectionConfig() {
			@Override
			public void initMap() {
				// 注入自定义 Map 对象
			}
		};
		// 如果模板引擎是 freemarker
		String templatePath = "/templates/mapper.xml.ftl";
		// 自定义输出配置
		List<FileOutConfig> fileOutConfigList = new ArrayList<>();
		// 自定义配置会被优先输出
		String dir = System.getProperty("user.dir") + "/demo-biz/src/main/resources/mapper/";
		File file = new File(dir);
		String[] list = file.list();
		if (!containsFile(list, autoGenerator.getStrategy().getInclude())) {
			fileOutConfigList.add(new FileOutConfig(templatePath) {
				@Override
				public String outputFile(TableInfo tableInfo) {
					// 自定义输出文件名 ， 如果你 Entity 设置了前后缀、此处注意 xml 的名称会跟着发生变化！！
					return dir + tableInfo.getEntityName() + "Mapper" + StringPool.DOT_XML;
				}
			});
		}
		injectionConfig.setFileOutConfigList(fileOutConfigList);
		autoGenerator.setCfg(injectionConfig);
		*/

        // 模板配置
        autoGenerator.setTemplate(initTemplateConfig(TARGET_PACKAGE, autoGenerator.getStrategy().getInclude()));
        // 模板引擎
        autoGenerator.setTemplateEngine(new FreemarkerTemplateEngine());
        // 执行生成
        autoGenerator.execute();
    }

    /**
     * <p>
     * 读取控制台内容
     * </p>
     */
    @SuppressWarnings("SameParameterValue")
    private static String scanner(String tip) {
        Scanner scanner = new Scanner(System.in);
        System.out.println(("请输入" + tip + "："));
        if (scanner.hasNext()) {
            String ipt = scanner.next();
            if (StringUtils.isNotEmpty(ipt)) {
                return ipt;
            }
        }
        throw new MybatisPlusException("请输入正确的" + tip + "！");
    }

    @SuppressWarnings("SameParameterValue")
    private static TemplateConfig initTemplateConfig(String outDir, String[] include) {
        TemplateConfig templateConfig = new TemplateConfig();
        templateConfig.setController(null);
        templateConfig.setXml(null);
        templateConfig.setService(null);
        templateConfig.setServiceImpl("/config/service-template.java");
        String[] listMapper = fileList(outDir, "mapper");
        if (listMapper != null && listMapper.length > 0) {
            if (containsFile(listMapper, include)) {
                templateConfig.setMapper(null);
            }
        }

        String[] listImpl = fileList(outDir, "service");
        if (listImpl != null && listImpl.length > 0) {
            if (containsFile(listImpl, include)) {
                templateConfig.setServiceImpl(null);
            }
        }

        return templateConfig;
    }

    private static String[] fileList(String outDir, String tmp) {
        File file = new File(Paths.get(outDir, String.join("/", PARENT_PACKAGE.split("\\.")), tmp).toString());
        return file.list();
    }

    private static boolean containsFile(String[] list, String[] include) {
        if (null == list || 0 == list.length) {
            return false;
        }
        List<String> arrayList = Arrays.asList(list);
        if (arrayList.size() > 0) {
            for (String file : arrayList) {
                file = file.replace("Service.java", "");
                file = file.replace("Mapper.java", "");
                if (include != null && include.length > 0) {
                    for (String fileName : include) {
                        if (file.toLowerCase().equals(fileName.replace("_", "").toLowerCase())) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }
}
