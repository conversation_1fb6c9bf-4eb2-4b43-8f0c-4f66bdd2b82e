package com.shinet.core.safe.hbase.config;

import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.client.ConnectionFactory;
import org.springframework.context.annotation.Bean;

import java.io.IOException;

@org.springframework.context.annotation.Configuration
public class HbaseConfig {

    @Bean(name = "hbaseConnection")
    public Connection init() throws IOException {
        // 新建一个Configuration
        Configuration conf = HBaseConfiguration.create();
        conf.set("hbase.zookeeper.quorum", "ld-2ze1vdu9684b75dkc-proxy-lindorm.lindorm.rds.aliyuncs.com:30020");
        conf.set("hbase.client.username", "root");
        conf.set("hbase.client.password", "root");
        Connection connection = ConnectionFactory.createConnection(conf);
        return connection;
    }


    /**
     * ocpc-hbase连接
     *
     * @return ocpc-hbase连接
     * @throws IOException 可能的异常
     */
    @Bean
    public Connection ocpcHadoopConnection() throws IOException {
        // 新建一个Configuration
        Configuration conf = HBaseConfiguration.create();
        conf.set("hbase.zookeeper.quorum", "ld-2ze609kgaqf74l34r-proxy-lindorm.lindorm.rds.aliyuncs.com:30020");
        conf.set("hbase.client.username", "root");
        conf.set("hbase.client.password", "root");
        return ConnectionFactory.createConnection(conf);
    }

}
