package com.shinet.core.safe.hsq.rsp;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/2/18
 */
@Data
public class BdIpRsp {
    private String address;
    private Content content;
    private Integer status;

    @Data
    public static class Content{
        private String address;
        private Address address_detail;
        private Point point;
    }

    @Data
    public static class Address{
        private String city;
        private Integer city_code;
        private String province;
    }

    @Data
    public static class Point{
        private String x;
        private String y;

        public String getDesc(){
            return String.format("%s,%s",x,y);
        }
    }

    public GdIpRsp convert(){
        GdIpRsp gdIpRsp = new GdIpRsp();
        gdIpRsp.setStatus(1);
        gdIpRsp.setInfocode("10000");
        gdIpRsp.setCity(this.content.address_detail.city);
        gdIpRsp.setProvince(this.content.address_detail.province);
        gdIpRsp.setAdcode(String.valueOf(this.content.address_detail.city_code));
        gdIpRsp.setRectangle(String.valueOf(this.content.point.getDesc()));
        return gdIpRsp;
    }
}
