package com.shinet.core.safe.hsq.rsp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class LcToutiaoCk {

    private String dsp;

    private String product;

    @ApiModelProperty(value = "ios android")
    private String os;

    @ApiModelProperty(value = "账户ID")
    private String accountId;


    @ApiModelProperty(value = "时间戳")
    private String ts;

    private Date createTime;

    private Date updateTime;

    @ApiModelProperty(value = "渠道包")
    private String pkgChannel;

    private String ip;
}
