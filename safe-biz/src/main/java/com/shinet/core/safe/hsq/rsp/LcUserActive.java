package com.shinet.core.safe.hsq.rsp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class LcUserActive {
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    @ApiModelProperty(value = "openId")
    private String openId;

    @ApiModelProperty(value = "unionId")
    private String unionId;

    @ApiModelProperty(value = "product")
    private String product;

    private String os;

    private String channel;

    private String source;

    private Date createTime;

    private Date updateTime;

    private String acDesc;

    private Date clickTime;

    private String gyType;

}
