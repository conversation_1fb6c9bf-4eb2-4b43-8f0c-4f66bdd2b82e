package com.shinet.core.safe.lock.bean;

import com.shinet.core.safe.lock.enums.LockReason;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/8/22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CheckResult {
    private boolean locked;
    private boolean ocpc;
    private String city;
    private boolean check;
    private LockReason lockReason;
}
