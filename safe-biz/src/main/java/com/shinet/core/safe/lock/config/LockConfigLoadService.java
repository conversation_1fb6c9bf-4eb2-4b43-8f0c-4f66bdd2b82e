package com.shinet.core.safe.lock.config;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.entity.LockIosConfig;
import com.shinet.core.safe.msql.service.ioslock.LockIosConfigService;
import com.shinet.core.safe.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 锁区配置刷新Load
 * <AUTHOR>
 * @since 2023/8/22
 */
@Slf4j
@Service
public class LockConfigLoadService {

    @Autowired
    private RedisUtils redisUtils;
    @ApolloJsonValue("${lock.area.black.city:[\"北京\",\"上海\",\"广州\",\"深圳\",\"佛山\",\"杭州\",\"南京\",\"成都\"]}")
    private Set<String> blackCitySet;

    @ApolloJsonValue("${lock.area.ios.black.city:[\"北京\"]}")
    public Set<String> iosBlackCitySet;

    @Autowired
    private LockIosConfigService lockIosConfigService;

    private Map<Integer, Integer> hwIosConfigMap;
    private Map<Integer, Integer> bjIosConfigMap;

    @PostConstruct
    public void refreshIosConfigMap(){
        log.info("===> Refresh IOS Config Start....");
        hwIosConfigMap = lockIosConfigService.list().stream()
                .collect(Collectors.toMap(LockIosConfig::getAppId, LockIosConfig::getLockHw,(r1, r2)->r1));
        bjIosConfigMap = lockIosConfigService.list().stream()
                .collect(Collectors.toMap(LockIosConfig::getAppId, LockIosConfig::getLockBj,(r1, r2)->r1));
        log.info("===> Refresh IOS Config End....");
    }



    public boolean defaultIosStrategy(Integer appId){
        return hwIosConfigMap.getOrDefault(appId,0) == 1;
    }

    public boolean bjLocalIosStrategy(Integer appId){
        return bjIosConfigMap.getOrDefault(appId,0) == 1;
    }

    public String getProductByAppId(Integer appId){

        return "";
    }

    public boolean cityInLockCity(String city,Integer appId){
        return true;
    }

    public boolean channelNeedCheck(CommonHeaderDTO commonHeaderDTO,Integer appId){

        return false;
    }

    public boolean lockVersion(CommonHeaderDTO commonHeaderDTO,Integer appId){

        return false;
    }

    public boolean lockAll(CommonHeaderDTO commonHeaderDTO,Integer appId){

        return false;
    }
}
