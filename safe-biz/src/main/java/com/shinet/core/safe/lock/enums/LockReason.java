package com.shinet.core.safe.lock.enums;

/**
 * <AUTHOR>
 * @since 2023/9/19
 */
public enum LockReason {
    DEFAULT(0,"默认"),
    BLACK(1,"黑名单命中"),
    SWITCH(2,"开关为锁区"),
    PKG(3,"包名命中"),
    IP(4,"地理位置命中")
    ;

    private Integer code;
    private String desc;

    LockReason(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
