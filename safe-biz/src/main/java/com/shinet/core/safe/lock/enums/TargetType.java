package com.shinet.core.safe.lock.enums;

/**
 * <AUTHOR>
 * @since 2023/9/19
 * 类型1-ip,2-oaid,3-caid,4-包名
 */
public enum TargetType {
    IP(1,"ip"),
    // 由于获取逻辑 判断 device/oa_id
    OA_ID(2,"oa_id"),
    CA_ID(3,"ca_id"),
    PKG(4,"包名"),


    ;
    private Integer type;
    private String desc;

    TargetType(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
