package com.shinet.core.safe.lock.filter;

import com.shinet.core.safe.lock.bean.CheckResult;
import com.shinet.core.safe.lock.enums.LockReason;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.service.LockBlackListService;
import com.shinet.core.safe.msql.service.LockWhiteListService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/7/13
 */
@Slf4j
@Service
public class BlackWhitelistFilter extends LockCheck{

    @Autowired
    private LockBlackListService lockBlackListService;
    @Autowired
    private LockWhiteListService lockWhiteListService;

    @Override
    protected CheckResult doInvoke(CommonHeaderDTO commonHeaderDTO, Integer appId, String pkgNames, String trans,CheckResult checkResult) {
        // 加载白名单
        if (lockWhiteListService.isInWhiteList(commonHeaderDTO)){
            log.info("[{}] 在白名单当中 直接非锁区..",trans);
            checkResult.setLocked(false);
            checkResult.setCheck(false);
            return checkResult;
        }

        if (lockBlackListService.isInBlackList(commonHeaderDTO)){
            log.info("[{}] 在黑名单当中 直接锁区..",trans);
            checkResult.setLocked(true);
            checkResult.setLockReason(LockReason.BLACK);
            checkResult.setCheck(false);
        }
        return checkResult;
    }
}
