package com.shinet.core.safe.lock.filter;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.shinet.core.safe.lock.bean.CheckResult;
import com.shinet.core.safe.lock.config.LockConfigLoadService;
import com.shinet.core.safe.lock.enums.LockReason;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/7/13
 */
@Slf4j
@Service
public class CityFilter extends LockCheck{

    @Autowired
    private LockConfigLoadService lockConfigLoadService;

    @ApolloJsonValue("${hwm.lock.ntf.app:[948]}")
    private List<Integer> hwxmAlLockNtfAppList;

    @ApolloJsonValue("${no.ocpc.join.app.list:[696,668]}")
    private List<Integer> noOcpcToJoinLockAreaAppList;

    @ApolloJsonValue("${gdt.no.ocpc.join.app.list:[668]}")
    private List<Integer> gdtNoOcpcToJoinLockAreaAppList;

    @Override
    protected CheckResult doInvoke(CommonHeaderDTO commonHeaderDTO, Integer appId, String pkgNames, String trans, CheckResult checkResult) {
        // 非OCPC特殊处理
        if (!checkResult.isOcpc()) {
            if ("hw".equalsIgnoreCase(commonHeaderDTO.getChannel()) ||
                    "mi".equalsIgnoreCase(commonHeaderDTO.getChannel()) ||
                    commonHeaderDTO.getChannel().contains("xiaomi") ||
                    commonHeaderDTO.getChannel().contains("huawei")) {
                log.info("华为小米渠道-非ocpc自然量 {} {} {} {} {}", trans, appId,
                        commonHeaderDTO.getDeviceId(), commonHeaderDTO.getOaid(),
                        commonHeaderDTO.getChannel());
                if (hwxmAlLockNtfAppList.contains(appId) || appId > 950) {
                    log.info("华为小米渠道-非ocpc执行全锁区 {} {}", trans, commonHeaderDTO.getDeviceId());
                    checkResult.setLocked(true);
                    checkResult.setLockReason(LockReason.IP);
                    checkResult.setCheck(false);
                }
            }

            // 非ocpc直接锁区 ==> 20220923 mengyazhou
            if (noOcpcToJoinLockAreaAppList.contains(appId) && commonHeaderDTO.getChannel().startsWith("ks")){
                log.info("[{}] KS Not OCPC ,Return true",trans);
                checkResult.setLocked(Boolean.TRUE);
                checkResult.setLockReason(LockReason.IP);
                checkResult.setCheck(false);
            }

            if (gdtNoOcpcToJoinLockAreaAppList.contains(appId) &&  commonHeaderDTO.getChannel().startsWith("gdt")){
                log.info("[{}] GDT Not OCPC ,Return true",trans);
                checkResult.setLocked(Boolean.TRUE);
                checkResult.setLockReason(LockReason.IP);
                checkResult.setCheck(false);
            }
        }

        if (lockConfigLoadService.cityInLockCity(checkResult.getCity(),appId)){
            checkResult.setLocked(true);
            checkResult.setLockReason(LockReason.IP);
            checkResult.setCheck(false);
        }

        return checkResult;
    }
}
