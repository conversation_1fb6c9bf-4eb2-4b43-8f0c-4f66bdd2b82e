package com.shinet.core.safe.lock.filter;

import com.alibaba.fastjson.JSON;
import com.shinet.core.safe.lock.bean.CheckResult;
import com.shinet.core.safe.lock.config.LockConfigLoadService;
import com.shinet.core.safe.lock.enums.LockReason;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @since 2023/9/12
 */
@Slf4j
@Service
public class CityIosFilter extends LockCheck{

    @Autowired
    private LockConfigLoadService lockConfigLoadService;

    @Override
    protected CheckResult doInvoke(CommonHeaderDTO commonHeaderDTO, Integer appId, String pkgNames, String trans, CheckResult checkResult) {

        String city = checkResult.getCity();
        // 海外IP
        if (StringUtils.isEmpty(city) || (StringUtils.isNotBlank(city) && "海外".equals(city))) {
            log.info("{} {} {} 城市命中海外 按照非OCPC处理", trans, commonHeaderDTO.getCaid(), city);
            checkResult.setLocked(true);
            checkResult.setLockReason(LockReason.IP);
            checkResult.setCheck(false);
            return checkResult;
        }

        if (!checkResult.isOcpc()) {
            // 北京策略
            if (lockConfigLoadService.bjLocalIosStrategy(appId)) {
                if (StringUtils.isNotEmpty(commonHeaderDTO.getCaid())) {
                    if (StringUtils.isEmpty(city)) {
                        checkResult.setLocked(true);
                        checkResult.setCheck(false);
                        return checkResult;
                    } else {
                        // 查询注册时间
                        boolean contains = Stream.of("海外", "北京", "廊坊").anyMatch(city::contains);
                        if (contains) {
                            log.info("{} {} {} 城市命中 按照非OCPC处理", trans, JSON.toJSONString(commonHeaderDTO), city);
                            checkResult.setLocked(true);
                            checkResult.setCheck(false);
                            if (!city.equals("海外") && appId == 947) {
                                checkResult.setLocked(false);
                                checkResult.setCheck(true);
                            }
                            return checkResult;
                        } else {
                            log.info("{} {} {} 命中国内-非OCPC处理", trans, commonHeaderDTO.getCaid(), city);
                            checkResult.setLocked(false);
                            return checkResult;
                        }
                    }
                } else {
                    log.info("{} {} {} 命中国内-非OCPC处理", trans, commonHeaderDTO.getCaid(), city);
                    checkResult.setLocked(false);
                    return checkResult;
                }
            }
            // 默认宽松策略
            if (lockConfigLoadService.defaultIosStrategy(appId)){
                log.info("{} {} {} 命中国内-非OCPC处理", trans, commonHeaderDTO.getCaid(), city);
                checkResult.setLocked(false);
                return checkResult;
            }
        }

        return checkResult;
    }
}
