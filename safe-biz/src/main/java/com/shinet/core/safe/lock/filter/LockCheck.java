package com.shinet.core.safe.lock.filter;

import com.shinet.core.safe.lock.bean.CheckResult;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;


/**
 * <AUTHOR>
 * @since 2023/8/22
 */
public abstract class LockCheck implements LockFilter{

    private LockCheck next;

    public LockCheck getNext() {
        return next;
    }

    public void setNext(LockCheck valve) {
        this.next = valve;
    }

    @Override
    public CheckResult check(CommonHeaderDTO commonHeaderDTO, Integer appId, String pkgNames, String trans, CheckResult checkResult) {
        CheckResult checkResultCr = doInvoke(commonHeaderDTO,appId,pkgNames,trans,checkResult);
        if (getNext() != null && checkResult.isCheck()) {
            checkResultCr = getNext().doInvoke(commonHeaderDTO,appId,pkgNames,trans,checkResultCr);
        }
        return checkResultCr;
    }

    protected abstract CheckResult doInvoke(CommonHeaderDTO commonHeaderDTO,Integer appId, String pkgNames, String trans,CheckResult checkResult);

    public static class Builder{
        private LockCheck head;
        private LockCheck tail;

        public Builder addLockFilter(LockCheck riskFilter){
            if (this.head == null){
                this.head = this.tail = riskFilter;
                return this;
            }
            this.tail.setNext(riskFilter);
            this.tail = riskFilter;
            return this;
        }

        public LockCheck build(){
            return this.head;
        }
    }
}
