package com.shinet.core.safe.lock.filter;

import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shinet.core.safe.hsq.rsp.GdIpRsp;
import com.shinet.core.safe.lock.bean.CheckResult;
import com.shinet.core.safe.lock.config.LockConfigLoadService;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.service.IpService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.directory.api.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/9/19
 */
@Slf4j
@Service
public class OcCityInfoFilter extends LockCheck{

    @Autowired
    private IpService ipService;
    @Autowired
    private LockConfigLoadService lockConfigLoadService;


    @Override
    protected CheckResult doInvoke(CommonHeaderDTO commonHeaderDTO, Integer appId, String pkgNames, String trans, CheckResult checkResult) {
        String product = lockConfigLoadService.getProductByAppId(appId);
        checkResult.setCity(getCity(commonHeaderDTO,appId,trans));
        checkResult.setCheck(true);
        // 如果不是 Nodsp 即投放设备
        checkResult.setOcpc(!"nodsp".equals(userIsOCPC(commonHeaderDTO,appId,product)));
        return checkResult;
    }

    private String getCity(CommonHeaderDTO commonHeaderDTO,Integer appId,String trans){
        String defalutQueryCity = "海外";
        if (lockConfigLoadService.defaultIosStrategy(appId)){
            defalutQueryCity = "未知";
        }
        try {
            String saveKey = ipService.getSaveKey(commonHeaderDTO);
            GdIpRsp gdIpRsp = ipService.getFormHBase(saveKey);
            if (gdIpRsp == null) {
                gdIpRsp = ipService.getIpLocation(commonHeaderDTO.getIp());
            }
            // 缓存
            if (StringUtils.isEmpty(gdIpRsp.getCity()) || gdIpRsp.getCity().contains("[")){
                // 使用ipPlus
                String city = ipService.getCityByIpPlus(commonHeaderDTO.getIp(),commonHeaderDTO.getGps());
                if (Strings.isNotEmpty(city)) {
                    gdIpRsp.setCity(city);
                    log.info("[{}] Get By GD Failed Succeed Use IpPlus Get city: {}",trans,city);
                    // 记录缓存
                    ipService.saveHBase(gdIpRsp,saveKey);
                    return city;
                }else {
                    log.info("[{}] Get By GD Failed IpPlus Also Failed",trans);
                }
            }else {
                return  gdIpRsp.getCity();
            }
            // 成功则判断
            if (StringUtils.isBlank(gdIpRsp.getCity())){
                log.warn("[{}] GET IP FAILED,Rs:{}",trans, JSON.toJSONString(gdIpRsp));
                if ("ios".equalsIgnoreCase(commonHeaderDTO.getOs())){
                    return defalutQueryCity;
                }
            }
            return defalutQueryCity;
        }catch (Exception e){
            log.info("获取Ip失败..",e);
            // IOS若判定的确异常 直接认为是锁区
            return defalutQueryCity;
        }
    }

     private static String userIsOCPC(CommonHeaderDTO commonHeaderDTO,Integer appId,String product){
        try {
            Map<String,Object> param = new HashMap<>();
            param.put("product",product);
            param.put("pkgChannel",commonHeaderDTO.getChannel());
            param.put("os",commonHeaderDTO.getOs().toLowerCase());
            param.put("ocpcDeviceId", MD5.create().digestHex(commonHeaderDTO.getDeviceId()));
            param.put("SourceDeviceId", commonHeaderDTO.getDeviceId());
            param.put("oaid",commonHeaderDTO.getOaid());
            param.put("androidId",commonHeaderDTO.getAndroidId());
            param.put("appId",appId);
            param.put("mac", commonHeaderDTO.getMac());
            param.put("model", commonHeaderDTO.getModel());
            param.put("ip", commonHeaderDTO.getIp());
            param.put("caid", commonHeaderDTO.getCaid());
            param.put("ua", commonHeaderDTO.getUa());

            String result = HttpUtil.get("http://ocpc-api.shinet-inc.com/dispense/user/event/guiDsp",param);

            if (StringUtils.isNotBlank(result)){
                JSONObject jsonObject = JSONObject.parseObject(result);
                // 2022-12-26 非nosDsp 均为买量
                return jsonObject.getString("data");
            }
        }catch (Exception e){
            log.error("Query Er:",e);
        }
        return "";
    }
}
