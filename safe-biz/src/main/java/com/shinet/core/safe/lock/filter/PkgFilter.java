package com.shinet.core.safe.lock.filter;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.shinet.core.safe.lock.bean.CheckResult;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/7/13
 */
@Slf4j
@Service
public class PkgFilter extends LockCheck{
    @ApolloJsonValue("${hw.ex.channel.pkg:[]}")
    private List<String> hwExChannelPkg;

    @Override
    protected CheckResult doInvoke(CommonHeaderDTO commonHeaderDTO, Integer appId, String pkgNames, String trans, CheckResult checkResult) {
        boolean pkgLock = false;
        if ("hw".equalsIgnoreCase(commonHeaderDTO.getChannel())||
                "mi".equalsIgnoreCase(commonHeaderDTO.getChannel()) ||
                commonHeaderDTO.getChannel().contains("xiaomi")||
                commonHeaderDTO.getChannel().contains("huawei")) {
            try {
                for (String dkPkg : hwExChannelPkg){
                    if (pkgNames.contains(dkPkg)){
                        log.info("{} {} {} {} {} {} oaid:{} android_id:{} 华米审核 包名拉黑 直接锁区 命中包名 {}",trans,commonHeaderDTO.getDeviceId(),
                                appId,
                                commonHeaderDTO.getChannel(),commonHeaderDTO.getAppVersion(),commonHeaderDTO.getModel()
                                ,commonHeaderDTO.getOaid(),commonHeaderDTO.getAndroidId(),dkPkg);
                        pkgLock = true;
                        break;
                    }
                }
                if (!pkgLock) {
                    log.info("{} {} {} {} {} {}  oaid:{} android_id:{} 华米审核 包名未命中 记录日志", trans, commonHeaderDTO.getDeviceId()
                            ,appId,commonHeaderDTO.getChannel(),commonHeaderDTO.getAppVersion(),commonHeaderDTO.getModel()
                            ,commonHeaderDTO.getOaid(),commonHeaderDTO.getAndroidId());
                }
            }catch (Exception e){
                log.warn("Match DkPkg Ex:",e);
            }
        }

        if (pkgLock){
            checkResult.setLocked(true);
            checkResult.setCheck(false);
        }
        return checkResult;
    }
}
