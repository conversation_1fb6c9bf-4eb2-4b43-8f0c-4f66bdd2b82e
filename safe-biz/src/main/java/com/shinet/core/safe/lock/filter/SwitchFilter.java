package com.shinet.core.safe.lock.filter;

import com.shinet.core.safe.lock.bean.CheckResult;
import com.shinet.core.safe.lock.config.LockConfigLoadService;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/7/13
 */
@Slf4j
@Service
public class SwitchFilter extends LockCheck{

    @Autowired
    private LockConfigLoadService lockConfigLoadService;

    @Override
    protected CheckResult doInvoke(CommonHeaderDTO commonHeaderDTO, Integer appId, String pkgNames, String trans, CheckResult checkResult) {
        if (!lockConfigLoadService.channelNeedCheck(commonHeaderDTO,appId)){
            checkResult.setCheck(false);
            checkResult.setLocked(false);
            return checkResult;
        }

        // 产品纬度全锁
        if (lockConfigLoadService.lockAll(commonHeaderDTO,appId)){
            checkResult.setCheck(false);
            checkResult.setLocked(true);
            return checkResult;
        }

        // 分版本开关
        if (lockConfigLoadService.lockVersion(commonHeaderDTO,appId)){
            checkResult.setCheck(false);
            checkResult.setLocked(true);
            return checkResult;
        }



        return checkResult;
    }
}
