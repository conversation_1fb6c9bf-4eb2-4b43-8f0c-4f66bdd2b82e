package com.shinet.core.safe.lock.service;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.shinet.core.safe.dto.LockKeyResult;
import com.shinet.core.safe.lock.bean.CheckResult;
import com.shinet.core.safe.lock.config.LockConfigLoadService;
import com.shinet.core.safe.lock.strategy.AndroidLockConfigStrategy;
import com.shinet.core.safe.lock.strategy.IosLockConfigStrategy;
import com.shinet.core.safe.lock.strategy.LockConfigStrategy;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.service.LockedAreaRecordAuService;
import com.shinet.core.safe.msql.service.LockedAreaRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @since 2023/7/13
 */
@Slf4j
@Service
public class LockConfigBaseService {


    @Autowired
    private AndroidLockConfigStrategy androidLockConfigStrategy;
    @Autowired
    private IosLockConfigStrategy iosLockConfigStrategy;
    @Autowired
    private LockedAreaRecordService lockedAreaRecordService;
    @Autowired
    private LockedAreaRecordAuService lockedAreaRecordAuService;
    @Autowired
    private LockConfigLoadService lockConfigLoadService;

    @ApolloJsonValue("${lock.no.cache.app:[810]}")
    private List<Integer> noCacheAppList;

    @Autowired
    private LockHbaseService lockHbaseService;

    public LockKeyResult queryAndSaveLock(CommonHeaderDTO commonHeaderDTO, Integer appId, String pkgNames){
        String trans = UUID.randomUUID().toString();
        // 获取策略
        LockConfigStrategy lockConfigStrategy = getStrategy(commonHeaderDTO);
        // 判定锁区结果
        CheckResult checkResult = lockConfigStrategy.lockCheck(commonHeaderDTO,appId,pkgNames,trans);

        LockKeyResult lockKeyResult = new LockKeyResult();
        lockKeyResult.setLocked(checkResult.isLocked());
        lockKeyResult.setOcpc(checkResult.isOcpc());
        lockKeyResult.setCity(checkResult.getCity());

        // 记录
        if ("ios".equalsIgnoreCase(commonHeaderDTO.getOs())){
            commonHeaderDTO.setOaid(commonHeaderDTO.getCaid());
        }
        refreshLockKeyResult(appId,commonHeaderDTO,checkResult);

        return lockKeyResult;
    }

    private LockConfigStrategy getStrategy(CommonHeaderDTO commonHeaderDTO){
        if ("android".equalsIgnoreCase(commonHeaderDTO.getOs())){
            return androidLockConfigStrategy;
        }else {
            return iosLockConfigStrategy;
        }
    }

    public void refreshLockKeyResult(Integer appId,CommonHeaderDTO commonHeaderDTO ,CheckResult checkResult){
        CompletableFuture.runAsync(()->{
            try {
                // 命中锁区 记录信息
                if (checkResult.isLocked()){
                    lockHbaseService.save(commonHeaderDTO,appId);
                    // 记录锁区用户Mysql
                    String product = lockConfigLoadService.getProductByAppId(appId);
                    String city = checkResult.getCity();

                    if ("ios".equalsIgnoreCase(commonHeaderDTO.getOs())){
                        commonHeaderDTO.setOaid(commonHeaderDTO.getCaid());
                    }
                    lockedAreaRecordService.saveIfNotExist(product,appId,commonHeaderDTO,city,checkResult.getLockReason().getDesc());
                    lockedAreaRecordAuService.saveIfNotExist(product,appId,commonHeaderDTO,city);
                }else {
                    if (noCacheAppList.contains(appId)) {
                        // 查询一次 若锁区 释放
                        List<String> deviceIdList = new ArrayList<>();
                        deviceIdList.add(commonHeaderDTO.getDeviceId());
                        deviceIdList.add(commonHeaderDTO.getOaid());
                        deviceIdList.add(commonHeaderDTO.getImei());
                        boolean isLocked = lockHbaseService.isLocked(deviceIdList, appId, commonHeaderDTO.getUserId());
                        if (isLocked) {
                            lockHbaseService.delLocked(deviceIdList,appId,commonHeaderDTO.getUserId());
                        }
                    }
                }
            }catch (Exception e){
                log.error("Save LockEx:",e);
            }
        });
    }




}
