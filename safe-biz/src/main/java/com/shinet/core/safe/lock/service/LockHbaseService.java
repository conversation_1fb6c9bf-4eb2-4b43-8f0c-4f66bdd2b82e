package com.shinet.core.safe.lock.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.bp.user.remote.api.UserRPC;
import com.coohua.bp.user.remote.dto.WechatOpenIdDTO;
import com.shinet.core.safe.lock.bean.CaidRegisterBean;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.util.HBaseUtils;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.TableNotFoundException;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.io.compress.Compression;
import org.apache.hadoop.hbase.io.encoding.DataBlockEncoding;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/9/19
 */
@Slf4j
@Service
public class LockHbaseService {

    private static final String TABLE_LOCKED = "user_locked";

    private static final String TABLE_CA = "user_caid_register";

    @Resource
    private Connection hbaseConnection;

    @MotanReferer(basicReferer = "bp-user")
    private UserRPC userRPC;

    private String buildDeviceKey(Integer appId,String deviceId){
        return String.format("locked:device:%s:%s", appId,deviceId);
    }

    private String buildUserIdKey(Integer appId,Long userId){
        return String.format("locked:user:%s:%s", appId,userId);
    }

    private String buildUnionidKey(String unionId){
        return String.format("locked:unionId:%s", unionId);
    }

    public void save(CommonHeaderDTO commonHeaderDTO,Integer appId){
        Map<String,byte[]> saveToHadoopBatch = new HashMap<>();
        byte[] result = JSON.toJSONBytes(true);
        // device_id
        if (StringUtils.isNotBlank(commonHeaderDTO.getDeviceId()) && !commonHeaderDTO.getDeviceId().startsWith("0000")){
            saveToHadoopBatch.put(buildDeviceKey(appId,commonHeaderDTO.getDeviceId()),result);
        }
        // oa_id
        if (StringUtils.isNotBlank(commonHeaderDTO.getOaid())){
            saveToHadoopBatch.put(buildDeviceKey(appId,commonHeaderDTO.getOaid()),result);
        }
        if (StringUtils.isNotBlank(commonHeaderDTO.getImei())){
            saveToHadoopBatch.put(buildDeviceKey(appId,commonHeaderDTO.getImei()),result);
        }
        // ak_user
        String unionId = null;
        if (StringUtils.isNotBlank(commonHeaderDTO.getAccessKey()) && commonHeaderDTO.getUserId() > 0L){
            saveToHadoopBatch.put(buildUserIdKey(appId,commonHeaderDTO.getUserId()),result);
            unionId = getUnionId(commonHeaderDTO, appId);
            if(StringUtils.isNotBlank(unionId)){
                saveToHadoopBatch.put(buildUnionidKey(unionId),result);
            }
        }
        if (saveToHadoopBatch.keySet().size() > 0) {
            log.info("appId : {} DeviceId : {} Oaid : {} getImei : {} AccessKey : {} unionId : {} 触发锁区,记录",appId,commonHeaderDTO.getDeviceId(),commonHeaderDTO.getOaid(),commonHeaderDTO.getImei(),commonHeaderDTO.getAccessKey(), unionId);
            HBaseUtils.saveToHadoopBatch(hbaseConnection, TABLE_LOCKED, saveToHadoopBatch);
        }
    }

    private String getUnionId(Long user, Integer appId) {
        String unionId = null;
        try {
            WechatOpenIdDTO wc = userRPC.getWechatOpenIdDTOByAppId(user, Long.valueOf(appId));
            if(wc != null){
                unionId = wc.getUnionId();
            }
        }catch (Exception e){
            log.error("userId : {} appId : {}", user, appId, e);
        }
        return unionId;
    }

    private String getUnionId(CommonHeaderDTO commonHeaderDTO, Integer appId) {
        return getUnionId(commonHeaderDTO.getUserId(), appId);
    }

    public void delLocked(List<String> deviceIdList, Integer appId, Long userId){
        try (Table table = hbaseConnection.getTable(TableName.valueOf("user_locked"))) {
            List<Delete> deletes = deviceIdList.stream()
                    .map(id -> new Delete(Bytes.toBytes(buildDeviceKey(appId, id))))
                    .collect(Collectors.toList());
            // add UserId
            Delete delete = new Delete(Bytes.toBytes(buildUserIdKey(appId, userId)));
            deletes.add(delete);

            if(userId != null && userId > 0L){
                String unionId = getUnionId(userId, appId);
                if(StringUtils.isNotBlank(unionId)){
                    deletes.add(new Delete(Bytes.toBytes(buildUnionidKey(unionId))));
                }
            }

            table.delete(deletes);
            log.info("已释放 {}", JSON.toJSONString(deviceIdList));
        } catch (Exception e) {
            log.error("Hadoop表删除失败 ", e);
        }
    }

    public boolean isLocked(List<String> deviceIdList,Integer appId,Long userId){
        try (Table table = hbaseConnection.getTable(TableName.valueOf("user_locked"))) {
            List<Get> gets = deviceIdList.stream()
                    .map(id -> new Get(Bytes.toBytes(buildDeviceKey(appId, id))))
                    .collect(Collectors.toList());
            // UserId
            Get user = new Get(Bytes.toBytes(buildUserIdKey(appId, userId)));
            gets.add(user);

            if(userId != null && userId > 0L){
                String unionId = getUnionId(userId, appId);
                if(StringUtils.isNotBlank(unionId)){
                    gets.add(new Get(Bytes.toBytes(buildUnionidKey(unionId))));
                }
            }

            Result[] results = table.get(gets);
            if (results != null) {
                for (Result result : results) {
                    byte[] bytes = result.getValue(Bytes.toBytes("family"), Bytes.toBytes("qualifier"));
                    boolean locked = bytes == null ? false : JSONObject.parseObject(bytes, Boolean.class);
                    if (locked){
                        return true;
                    }
                }
            }
            return false;
        } catch (Exception e) {
            log.error("Hadoop表查询失败 ", e);
            log.info("{} {} ERROR 非锁区..",appId,userId);
            return false;
        }
    }

    private final static  byte[] families = Bytes.toBytes("family");

    @PostConstruct
    public void createTable() throws IOException {
        try (Admin admin = hbaseConnection.getAdmin()) {
            // 建表
            try {
                admin.getDescriptor(TableName.valueOf(TABLE_CA));
            } catch (TableNotFoundException te) {
                ColumnFamilyDescriptor familyDescriptor = ColumnFamilyDescriptorBuilder
                        .newBuilder(families)
                        .setCompressionType(Compression.Algorithm.ZSTD)
                        .setDataBlockEncoding(DataBlockEncoding.DIFF)
                        .build();

                TableDescriptor tableDescriptor = TableDescriptorBuilder.newBuilder(TableName.valueOf(TABLE_CA))
                        .setColumnFamily(familyDescriptor)
                        .setCompactionEnabled(true)
                        .build();

                admin.createTable(tableDescriptor, HBaseUtils.ONLY_SPLIT_KEYS);
            }
        } catch (IOException e) {
            log.error("{}表创建失败:{}", TABLE_CA, e);
            throw e;
        }
    }

    public CaidRegisterBean queryIfNotExistSave(String product, String caid){
        if (StringUtils.isEmpty(caid)){
            return null;
        }

        CaidRegisterBean caidRegisterBean = queryByCaid(product,caid);
        if (caidRegisterBean == null){
            caidRegisterBean = new CaidRegisterBean();
            caidRegisterBean.setCaid(caid);
            caidRegisterBean.setProduct(product);
            Long time = System.currentTimeMillis();
            caidRegisterBean.setCreateTime(time);
            try (Table table = hbaseConnection.getTable(TableName.valueOf(TABLE_CA))) {
                String key = String.format("%s:%s",product,caid);
                Put put = new Put(Bytes.toBytes(key));
                put.addColumn(families, Bytes.toBytes("caid"), Bytes.toBytes(JSONObject.toJSONString(caidRegisterBean)));
                table.put(put);
                log.info("product {} caid {} {} 已保存",product,caid,time);
            } catch (IOException e) {
                log.error("Save Caid Ex:",e);
            }
        }
        return caidRegisterBean;
    }

    public CaidRegisterBean queryByCaid(String product,String caid){
        try (Table table = hbaseConnection.getTable(TableName.valueOf(TABLE_CA))) {
            String key = String.format("%s:%s",product,caid);
            Get get = new Get(Bytes.toBytes(key));
            get.addColumn(Bytes.toBytes("family"), Bytes.toBytes("caid"));
            Result result = table.get(get);

            byte[] resultByte = result.getValue(Bytes.toBytes("family"), Bytes.toBytes("caid"));
            if (resultByte != null){
                return JSON.parseObject(resultByte,CaidRegisterBean.class);
            }
        } catch (Exception e) {
            log.warn("Hadoop表检索失败:", e);
        }
        return null;
    }


}
