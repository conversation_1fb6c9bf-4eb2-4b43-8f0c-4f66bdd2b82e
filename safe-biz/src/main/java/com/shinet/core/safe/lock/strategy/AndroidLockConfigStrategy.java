package com.shinet.core.safe.lock.strategy;

import com.shinet.core.safe.lock.bean.CheckResult;
import com.shinet.core.safe.lock.enums.LockReason;
import com.shinet.core.safe.lock.filter.*;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/7/13
 */
@Service
public class AndroidLockConfigStrategy implements LockConfigStrategy{

    @Autowired
    private BlackWhitelistFilter blackWhitelistFilter;
    @Autowired
    private SwitchFilter switchFilter;
    @Autowired
    private PkgFilter pkgFilter;
    @Autowired
    private CityFilter cityFilter;
    @Autowired
    private OcCityInfoFilter ocCityInfoFilter;


    @Override
    public CheckResult lockCheck(CommonHeaderDTO commonHeaderDTO, Integer appId, String pkgNames, String trans) {
        LockCheck.Builder builder = new LockCheck.Builder();
        CheckResult checkResultR = new CheckResult(false,false,null,true, LockReason.DEFAULT);
        return builder
                .addLockFilter(ocCityInfoFilter) // 补充信息
                .addLockFilter(blackWhitelistFilter) // 设备、IP黑名单
                .addLockFilter(switchFilter) // 全局开关 分版本、分渠道开关
                .addLockFilter(pkgFilter) // 包名检测
                .addLockFilter(cityFilter) // IP检测
                .build()
                .check(commonHeaderDTO,appId,pkgNames,trans,checkResultR);
    }
}
