package com.shinet.core.safe.lock.strategy;

import com.shinet.core.safe.lock.bean.CheckResult;
import com.shinet.core.safe.lock.enums.LockReason;
import com.shinet.core.safe.lock.filter.*;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/7/13
 */
@Service
public class IosLockConfigStrategy implements LockConfigStrategy{

    @Autowired
    private BlackWhitelistFilter blackWhitelistFilter;
    @Autowired
    private SwitchFilter switchFilter;
    @Autowired
    private CityIosFilter cityIosFilter;
    @Autowired
    private OcCityInfoFilter getInfoFilter;

    @Override
    public CheckResult lockCheck(CommonHeaderDTO commonHeaderDTO, Integer appId, String pkgNames, String trans) {
        LockCheck.Builder builder = new LockCheck.Builder();
        CheckResult checkResultR = new CheckResult(false,false,null,true, LockReason.DEFAULT);
        // 获取城市和OCPC信息
       return builder
                .addLockFilter(getInfoFilter) // 补充信息
                .addLockFilter(blackWhitelistFilter) // 设备、IP黑名单
                .addLockFilter(switchFilter) // 全局开关 分版本、分渠道开关
                .addLockFilter(cityIosFilter)// 地理位置单独检测
                .build()
                .check(commonHeaderDTO,appId,pkgNames,trans,checkResultR);
    }
}
