package com.shinet.core.safe.monitor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池监控服务
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@Service
public class ThreadPoolMonitorService {

    @Resource
    @Qualifier("dataPersistenceExecutor")
    private ThreadPoolTaskExecutor dataPersistenceExecutor;

//    @Resource
//    @Qualifier("ipQueryExecutor")
//    private ThreadPoolTaskExecutor ipQueryExecutor;

    /**
     * 每30秒监控一次线程池状态
     */
    @Scheduled(fixedRate = 30000)
    public void monitorThreadPools() {
        try {
            monitorSingleThreadPool("DataPersistence", dataPersistenceExecutor);
//            monitorSingleThreadPool("ipQueryExecutor", ipQueryExecutor);

            logOverallStatistics();
            
        } catch (Exception e) {
            log.error("线程池监控异常", e);
        }
    }

    /**
     * 监控单个线程池
     */
    private void monitorSingleThreadPool(String poolName, ThreadPoolTaskExecutor executor) {
        ThreadPoolExecutor threadPool = executor.getThreadPoolExecutor();
        
        int activeCount = threadPool.getActiveCount();
        int poolSize = threadPool.getPoolSize();
        int corePoolSize = threadPool.getCorePoolSize();
        int maximumPoolSize = threadPool.getMaximumPoolSize();
        int queueSize = threadPool.getQueue().size();
        long completedTaskCount = threadPool.getCompletedTaskCount();
        long taskCount = threadPool.getTaskCount();
        
        // 计算利用率
        double threadUtilization = poolSize > 0 ? (double) activeCount / poolSize * 100 : 0;
        double queueUtilization = threadPool.getQueue().remainingCapacity() > 0 ? 
            (double) queueSize / (queueSize + threadPool.getQueue().remainingCapacity()) * 100 : 0;
        
        log.info("{}线程池监控: 活跃线程={}/{}, 队列长度={}, 线程利用率={}%, 队列利用率={}%, 已完成任务={}",
                poolName, activeCount, poolSize, queueSize, threadUtilization, queueUtilization, completedTaskCount);
        
        // 告警检查
        checkAlerts(poolName, activeCount, poolSize, maximumPoolSize, queueSize, threadUtilization, queueUtilization);
    }

    /**
     * 告警检查
     */
    private void checkAlerts(String poolName, int activeCount, int poolSize, int maximumPoolSize, 
                           int queueSize, double threadUtilization, double queueUtilization) {
        
        // 线程池使用率告警
        if (threadUtilization > 80) {
            log.warn("【告警】{}线程池使用率过高: {}%, 活跃线程={}/{}",
                    poolName, threadUtilization, activeCount, poolSize);
        }
        
        // 队列积压告警
        if (queueUtilization > 70) {
            log.warn("【告警】{}线程池队列积压: {}%, 队列长度={}",
                    poolName, queueUtilization, queueSize);
        }
        
        // 线程池扩容告警
        if (poolSize >= maximumPoolSize * 0.9) {
            log.warn("【告警】{}线程池接近最大容量: {}/{}", 
                    poolName, poolSize, maximumPoolSize);
        }
        
        // 特定业务告警
        if ("CoreBusiness".equals(poolName)) {
            if (queueSize > 400) {
                log.error("【严重告警】核心业务线程池队列积压严重: {}, 可能影响用户体验", queueSize);
            }
        }
    }

    /**
     * 记录总体统计
     */
    private void logOverallStatistics() {
        int totalActiveThreads =
                               dataPersistenceExecutor.getActiveCount()
//                                       + ipQueryExecutor.getActiveCount()
                ;
        
        int totalPoolSize =
                          dataPersistenceExecutor.getPoolSize()
//                                  + ipQueryExecutor.getPoolSize()
                ;
        
        int totalQueueSize =
                           dataPersistenceExecutor.getThreadPoolExecutor().getQueue().size()
//                                   + ipQueryExecutor.getThreadPoolExecutor().getQueue().size()
                ;
        
        log.info("=== 线程池总体统计 === 总活跃线程={}, 总线程数={}, 总队列长度={}", 
                totalActiveThreads, totalPoolSize, totalQueueSize);
    }

    /**
     * 获取线程池健康状态
     */
    public String getThreadPoolHealthStatus() {
        StringBuilder status = new StringBuilder();
        status.append("线程池健康状态:\n");
        
        appendPoolStatus(status, "数据持久化", dataPersistenceExecutor);
//        appendPoolStatus(status, "消息发送", ipQueryExecutor);
        
        return status.toString();
    }

    private void appendPoolStatus(StringBuilder status, String poolName, ThreadPoolTaskExecutor executor) {
        ThreadPoolExecutor threadPool = executor.getThreadPoolExecutor();
        status.append(String.format("%s: 活跃=%d, 总数=%d, 队列=%d\n", 
                poolName, 
                threadPool.getActiveCount(),
                threadPool.getPoolSize(),
                threadPool.getQueue().size()));
    }
}
