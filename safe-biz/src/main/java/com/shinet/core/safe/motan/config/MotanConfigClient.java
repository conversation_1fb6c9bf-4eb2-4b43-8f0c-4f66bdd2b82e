package com.shinet.core.safe.motan.config;

import com.weibo.api.motan.config.springsupport.BasicRefererConfigBean;
import com.weibo.api.motan.config.springsupport.RegistryConfigBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @since 2023/3/31
 */
@Configuration
public class MotanConfigClient {

    // Zk的配置 由于@import支持.Class的扫描 但是这个 Config 被重命名了
    // 使用 Resource 注解强制依赖导入 无需新建 Bean
    @Resource(name = "registryConfig")
    private RegistryConfigBean registryConfigBean;


    @Bean(name = "bp-data-retrieve")
    @ConfigurationProperties(prefix = "app.motan.bp-data-retrieve.basic-referer")
    public BasicRefererConfigBean userClientConfig() {
        return new BasicRefererConfigBean();
    }


    @Bean(name = "bp-user")
    @ConfigurationProperties(prefix = "app.motan.bp-user.basic-referer")
    public BasicRefererConfigBean bpUserClientConfig() {
        return new BasicRefererConfigBean();
    }

    @Bean(name = "bp-account")
    @ConfigurationProperties(prefix = "app.motan.bp-account.basic-referer")
    public BasicRefererConfigBean bpAccountClientConfig() {
        return new BasicRefererConfigBean();
    }


    @Bean(name = "user-event")
    @ConfigurationProperties(prefix = "app.motan.user-event.basic-referer")
    public BasicRefererConfigBean userEventClientConfig() {
        return new BasicRefererConfigBean();
    }


}
