package com.shinet.core.safe.msql.config;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import io.swagger.models.auth.In;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
@Data
public class SafeSwitcher {
    @Value("${safe.req.log:false}")
    public boolean reqLog;

    @Value("${safe.ali.req:true}")
    public boolean aliReq;

    @Value("${safe.byte.qdb:true}")
    public boolean byteQdb;

    @Value("${safe.checkauth:false}")
    public boolean checkauth;
    @Value("${safe.ios.lock:40}")
    public Integer iosLockDays;

    @Value("${safe.log.flag:false}")
    public boolean logFlag;

    @ApolloJsonValue("${ocpc.ip.lockcaid:[\"************\"]}")
    public Set<String> ipShinetZiyou;


    @ApolloJsonValue("${safe.loading.projects:[\"yyddddss\"]}")
    public Set<String> loadingLockProjects;

    @ApolloJsonValue("${ios.lock.ip.white.map:{\"jj\":[\"************\"]}}")
    public Map<String, List<String>> iosIpWhiteMap;

    @ApolloJsonValue("${safe.lock.projects:[\"ddgs2\"]}")
    public Set<String> lockPrjs;

    @ApolloJsonValue("${safe.shownoty.deviceIds:[\"77a93ca1-2ff7-4a2b-a556-1d41bdfe2dc4\"]}")
    public Set<String> notyDeviceSet;

    @ApolloJsonValue("${safe.bytemin.openids.oa:[\"_0009qoBZwlxEksDmznZInippbXfyLQqK6C5\"]}")
    public Set<String> openOpenids;

    @Value("${safe.aliyun.reqstg:1}")
    public Integer reqstg;

    @Value("${safe.aliyun.adrcheck:2}")
    public Integer adrcheck;


    @ApolloJsonValue("${lock.ip.wt:[\"************\"]}")
    public Set<String> wtLockIps;


    @ApolloJsonValue("${lock.newpjs.mwrj:[\"gsdcg\"]}")
    public Set<String> adNewLockPros;
}
