package com.shinet.core.safe.msql.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-14
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AliUserDevice对象", description="")
public class AliUserDevice implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Long userId;

    private String deviceId;

    private Integer appId;

    private String accessKey;

    private String product;

    private String ip;

    private Double ipScore;

    @ApiModelProperty(value = "代理")
    private Integer ipProxy;

    @ApiModelProperty(value = "idc机房")
    private Integer ipIdc;

    private Integer ipNat;

    @ApiModelProperty(value = "基站")
    private Integer ipBase;

    private String os;

    private String channel;

    private String brand;

    private String pkgId;

    private String gps;

    private String appVersion;

    private String osVersion;

    private String romVersion;

    private String aliDtoken;

    private String tags;

    private Double score;

    private Integer deviceStatus;

    private Date createTime;

    private Date updateTime;


}
