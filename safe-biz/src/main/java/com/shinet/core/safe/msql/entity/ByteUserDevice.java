package com.shinet.core.safe.msql.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ByteUserDevice对象", description="")
public class ByteUserDevice implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Long userId;

    private String deviceId;

    private String byteDid;

    private String byteService;

    private Integer appId;

    private String accessKey;

    private String product;

    private String ip;

    private String os;

    private String channel;

    private String brand;

    private String pkgId;

    private String gps;

    private String appVersion;

    private String osVersion;

    private String romVersion;

    private String tags;

    private String tagsName;

    private Integer score;

    private String detail;

    private String transId;

    private Integer deviceStatus;

    private Date createTime;

    private Date updateTime;


}
