package com.shinet.core.safe.msql.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
public class HighQualityDeviceReleaseRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private String os;
    private Integer appId;
    private String product;
    private String deviceType;
    private String deviceId;
    private Integer strategy;
    private String logday;
    private Date createTime;
    private Date updateTime;
}
