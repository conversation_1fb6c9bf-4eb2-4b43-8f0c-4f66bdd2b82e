package com.shinet.core.safe.msql.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 
 * @TableName ios_check_url
 */
@TableName(value ="ios_check_url")
@Data
public class IosCheckUrl {
    /**
     * 
     */
    @TableId
    private String product;

    /**
     * 
     */
    private String productName;

    /**
     * 
     */
    private String url;

    /**
     * 
     */
    private String appStoreUrl;

    /**
     * 上架状态0-已下架 1-已上架
     */
    private Integer upStatus;

    /**
     * 上架状态0-待检测 2-下榜
     */
    private Integer noStatus;

    /**
     * 上线人
     */
    private String engineer;

    /**
     * 
     */
    private String phone;

    /**
     * 
     */
    private String email;

    /**
     * 
     */
    private String authCode;

    /**
     * 
     */
    private String password;
}