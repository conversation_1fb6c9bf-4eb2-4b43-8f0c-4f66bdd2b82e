package com.shinet.core.safe.msql.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 锁区黑名单表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="LockBlackList对象", description="锁区黑名单表")
public class LockBlackList implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "类型1-ip,2-oaid,3-caid,4-包名")
    private Integer targetType;

    private String targetInfo;

    @ApiModelProperty(value = "拉黑原因备注")
    private String remark;

    private String opName;

    private Date createTime;

    private Date updateTime;


}
