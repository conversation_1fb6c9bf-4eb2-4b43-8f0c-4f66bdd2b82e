package com.shinet.core.safe.msql.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="LockIosBlack对象", description="")
public class LockIosBlack implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "caid", type = IdType.INPUT)
    private String caid;

    private Integer lockA;

    private String city;

    private String reason;

    private Integer pnum;

    private Date createTime;

    private Date updateTime;


}
