package com.shinet.core.safe.msql.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 锁区IOS特殊策略
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="LockIosConfig对象", description="锁区IOS特殊策略")
public class LockIosConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Integer appId;

    private String product;

    private String productName;

    @ApiModelProperty(value = "1-只锁区海 0-默认策略 ")
    private Integer lockHw;

    @ApiModelProperty(value = "1-tf20天之后 0-投放前20天")
    private Integer lockBj;

    private Integer isDown;

    @ApiModelProperty(value = "投放日期")
    private Date toufangTime;

    private String versionLocks;

    private Integer allLock;

    private Integer allOpen;

    private Integer wxLock;

    @ApiModelProperty(value = "关联配置项ID")
    private Integer linkId;

    private Date createTime;

    private Date updateTime;


}
