package com.shinet.core.safe.msql.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="AndroidLockWhiteStrategy对象", description="")
public class LockWhiteStrategy implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private Integer appId;
    private String product;
    private String productName;
    private Integer strategy;
    private Integer intValue1;
    private Integer intValue2;
    private String strValue1;
    private String strValue2;
    private Integer delFlag;
}
