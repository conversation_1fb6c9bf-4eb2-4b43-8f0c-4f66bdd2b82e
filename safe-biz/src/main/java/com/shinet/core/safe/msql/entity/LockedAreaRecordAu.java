package com.shinet.core.safe.msql.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="LockedAreaRecordAu对象", description="")
public class LockedAreaRecordAu implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Date logday;

    private String product;

    private Integer appId;

    private String os;

    private String accessKey;

    private String deviceId;

    private String oaId;

    private String imei;

    private String ip;

    private Integer locked;

    private String city;

    private Date createTime;

    private Date updateTime;

    private String channel;

    private String appVersion;


}
