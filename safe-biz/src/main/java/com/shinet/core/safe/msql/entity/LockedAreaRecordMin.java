package com.shinet.core.safe.msql.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="LockedAreaRecord对象", description="")
public class LockedAreaRecordMin implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String product;

    private Integer appId;

    private String os;

    private String accessKey;

    private String openId;

    private String ip;

    private Integer locked;

    private String city;

    private Date createTime;

    private Date updateTime;

    private String lockedReason;
    private Long userId;
    private Integer isOcpc;


}
