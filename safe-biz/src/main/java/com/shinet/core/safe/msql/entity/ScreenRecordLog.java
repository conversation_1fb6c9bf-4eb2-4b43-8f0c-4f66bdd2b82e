package com.shinet.core.safe.msql.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName screen_record_log
 */
@TableName(value ="screen_record_log")
@Data
public class ScreenRecordLog implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 产品
     */
    private String product;

    /**
     * ios、安卓
     */
    private String os;

    /**
     * 
     */
    private String oaid;

    /**
     * 
     */
    private String imei;

    /**
     * 
     */
    private String deviceId;

    /**
     * 
     */
    private String ip;

    /**
     * 
     */
    private String city;

    /**
     * 
     */
    private String dsp;

    /**
     * 
     */
    private Integer isOcpc;

    /**
     * 
     */
    private String storeName;

    /**
     * 
     */
    private String channel;

    /**
     * 
     */
    private String appVersion;

    /**
     * 
     */
    private String showNoty;

    /**
     * 
     */
    private String userId;

    /**
     * 
     */
    private Integer isVpn;

    /**
     * 用户pv数
     */
    private Integer pv;

    /**
     * 是否命中白名单
     */
    private String hitWhiteListFlag;

    /**
     * 是否命中pv数
     */
    private String hitPvFlag;

    /**
     * 全局配置pv数
     */
    private Integer configPv;

    /**
     * 全局配置使用时间-单位为秒
     */
    private Integer configUsageTime;

    /**
     * 
     */
    private String remark;

    /**
     * 
     */
    private String extend1;

    /**
     * 
     */
    private String extend2;

    /**
     * 
     */
    private String extend3;

    /**
     * 
     */
    private String extend4;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Date updateTime;

    private Integer appId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}