package com.shinet.core.safe.msql.entity;


import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.io.Serializable;

/**
 * 屏幕录制请求参数
 */
@Data
public class ScreenRecordReq implements Serializable {
    String product;
    String channel;
    String oaid;
    String imei;
    String deviceId;
    String pkgs;
    Long userId;
    String appVersion;
    String model;
    private Integer appId;
    /**
     * ios、安卓
     */
    private String os;

    Boolean isVp = false;
}
