package com.shinet.core.safe.msql.entity;

import com.baomidou.mybatisplus.annotation.IdType;

import java.math.BigInteger;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="ShumeiRiskInfo对象", description="")
public class ShumeiRiskInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private Long userId;

    // 用户账号id
    private String tokenId;

    @ApiModelProperty(value = "设备id")
    private String deviceId;

    @ApiModelProperty(value = "数美设备id")
    private String shumeiDeviceId;

    @ApiModelProperty(value = "请求id")
    private String requestId;

    // 事件请求id
    private String eventRequestId;
    // 事件类型
    private String eventType;

    private Integer appId;

    private Long pkgId;

    private String product;

    private String ip;

    // 事件处理建议
    private String riskLevel;
    // 事件风险描述
    private String eventRiskDesc;

    // 事件命中规则 最高规则标识
    private String modelHigh;

    // 事件命中规则
    private String hits;

    // 渠道
    private String channel;

    @ApiModelProperty(value = "风险ip 0-非, 1-是")
    private Integer riskIp;

    private String os;

    private String osVersion;

    @ApiModelProperty(value = "app版本")
    private String appVersion;

    private String imei;

    private String oaid;

    private String mac;

    private String androidId;

    @ApiModelProperty(value = "手机品牌型号")
    private String brand;

    @ApiModelProperty(value = "是否PC模拟器 0-否, 1-是")
    private Integer pcEmulator;

    @ApiModelProperty(value = "pc模拟时间")
    private BigInteger pcEmulatorLastTs;

    @ApiModelProperty(value = "是否云端设备 0-否, 1-是")
    private Integer cloudDevice;

    @ApiModelProperty(value = "云手机设备时间")
    private BigInteger cloudDeviceLastTs;

    @ApiModelProperty(value = "是否篡改设备 0-否, 1- 是")
    private Integer alteredDevice;

    @ApiModelProperty(value = "篡改设备时间")
    private BigInteger alteredDeviceLastTs;

    @ApiModelProperty(value = "是否多开环境 0-否, 1-是")
    private Integer multiBoxing;

    @ApiModelProperty(value = "多开设备时间")
    private BigInteger multiBoxingLastTs;

    @ApiModelProperty(value = "是否伪造设备 0-否, 1-是")
    private Integer fakerDevice;

    @ApiModelProperty(value = "伪造设备时间")
    private BigInteger fakerDeviceLastTs;

    @ApiModelProperty(value = "是否农场设备 0-否, 1-是")
    private Integer farmerDevice;

    @ApiModelProperty(value = "农场设备时间")
    private BigInteger farmerDeviceLastTs;

    @ApiModelProperty(value = "是否积分墙设备 0-否, 1-是")
    private Integer offerwallDevice;

    @ApiModelProperty(value = "积分墙设备时间")
    private BigInteger offerwallDeviceLastTs;

    @ApiModelProperty(value = "是否手机模拟器 0-否, 1-是")
    private Integer phoneEmulator;

    @ApiModelProperty(value = "手机模拟器时间")
    private BigInteger phoneEmulatorLastTs;

    @ApiModelProperty(value = "一级风险标签")
    private String label1;

    @ApiModelProperty(value = "二级风险标签")
    private String label2;

    @ApiModelProperty(value = "三级风险标签")
    private String label3;

    @ApiModelProperty(value = "风险描述")
    private String description;

    @ApiModelProperty(value = "命中策略时间")
    private Integer hitTime;

    @ApiModelProperty(value = "详情")
    private String hitDetail;

    private Date createTime;

    private Date updateTime;


}
