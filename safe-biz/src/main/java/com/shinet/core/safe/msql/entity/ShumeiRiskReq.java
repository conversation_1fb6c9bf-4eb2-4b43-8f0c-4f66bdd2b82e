package com.shinet.core.safe.msql.entity;


import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ShumeiRiskReq implements Serializable {
    /**
     * 数美设备id
     */
    private String shuMeiDeviceId;

    /**
     * 产品
     */
    private String product;

    /**
     * 事件
     */
    private String eventId;

    /**
     * 用户账号id
     */
    private String tokenId;

    /**
     * 激活渠道
     */
    private String channel;

    // 注册登录所需参数
    /**
     * 登录、注册类型
     * 注册 ： 手机号码一键注册:phoneOnePass、 手机号验证码注册:phoneMessage、第三方授权:signupPlatform、用户名密码注册:userPassword
     * 登录 ： fastLogin：快速登录   phoneOneLogin：本机号码一键登录   phonePassword：手机号密码登录   phoneMessage：手机号验证码登录   signupPlatform：第三方授权登录   userPassword：用户名密码登录    biometric：生物识别
     */
    private String type;


    // 提现所需参数
    /**
     * 提现金额 单位:元
     */
    private BigDecimal withdrawAmount;

    /**
     * 提现账户id (例如支付宝账户id)
     */
    private String withdrawAccountId;


    // 广告事件所需参数
    /**
     * 广告类型
     */
    private String adType;

    /**
     * 广告代码位id
     */
    private String posId;


    // 发放奖励所需参数
    /**
     * 激励金额 单位:元
     */
    private BigDecimal rewardAmount;
    /**
     * 观看广告的活动入口id 例如：签到活动、道具领取活动等
     */
    private String taskId;

    // 接受邀请
    /**
     * 邀请人 id
     */
    private String inviteUserId;

    // 广告消失
    /**
     * 广告消失方式
     * 广告消失方式可选值：
     * CLICK_CLOSE_BUTTON:关闭按钮消失；
     * COMPLETE_AD:播放完成；
     * SKIP_AD:跳过广告；
     * QUIT_APP:退出app;
     * SHOW_AD_ERROR:广告播放异常
     */
    private String adFinishType;

}
