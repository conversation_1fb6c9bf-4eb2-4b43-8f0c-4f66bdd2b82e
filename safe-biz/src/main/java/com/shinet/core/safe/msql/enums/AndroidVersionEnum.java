package com.shinet.core.safe.msql.enums;

import lombok.Getter;

@Getter
public enum AndroidVersionEnum {

    ANDROID11("30","11","Android 11"),
    ANDROID12("31","12","Android 12")
    ;


    private String api;
    private String version;
    private String name;

    AndroidVersionEnum(String api, String version, String name) {
        this.api = api;
        this.version = version;
        this.name = name;
    }

    public static AndroidVersionEnum getByApi(String api) {
        for (AndroidVersionEnum androidVersionEnum : values()) {
            if (androidVersionEnum.api.equals(api) || api.startsWith(androidVersionEnum.api)) {
                return androidVersionEnum;
            }
        }
        return null;
    }
}
