package com.shinet.core.safe.msql.enums;

public enum ByteEventEnums {
	login(1,"login", "登录"),
	withdrawal(2,"withdrawal", "提现"),
	register(3,"register", "注册"),
	activity(4,"activity", "获利"),
	send_invite(5,"send_invite", "邀请"),
	accept_invite(6,"accept_invite", "接受邀请"),
	;

	public String value;
	public String name;
	public Integer code;

	ByteEventEnums(Integer code,String value, String name) {
		this.code = code;
		this.value = value;
		this.name = name;
	}

	public static ByteEventEnums getByCode(Integer code){
		for (ByteEventEnums byteEventEnums : values()){
			if (byteEventEnums.code.equals(code)){
				return byteEventEnums;
			}
		}
		return null;
	}

	public static ByteEventEnums getStatus(Integer value) {
		if (value != null) {
			ByteEventEnums[] otypes = ByteEventEnums.values();
			for (ByteEventEnums memberType : otypes) {
				if (value.equals(memberType.value)) {
					return memberType;
				}
			}
		}
		return null;
	}
}
