package com.shinet.core.safe.msql.enums;

public enum DeviceStatus {
	INIT(0, "初始"),
	DISABLE(-1, "拉黑"),
	;

	public Integer value;
	public String name;

	DeviceStatus(int value, String name) {
		this.value = value;
		this.name = name;
	}

	public static DeviceStatus getStatus(Integer value) {
		if (value != null) {
			DeviceStatus[] otypes = DeviceStatus.values();
			for (DeviceStatus memberType : otypes) {
				if (value.equals(memberType.value)) {
					return memberType;
				}
			}
		}
		return null;
	}
}
