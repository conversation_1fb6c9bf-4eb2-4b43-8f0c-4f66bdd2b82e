package com.shinet.core.safe.msql.enums;

/**
 * <AUTHOR>
 * @since 2022/4/19
 */
public enum SubEventEnums {
    sign(1,"sign","签到"),
    lottery(2,"lottery","抽奖"),
    coupon(3,"coupon","领券"),
    envelope(4,"envelope","抢红包"),
    game(5,"game","玩游戏"),
    other(6,"other","其他"),
    ;


    private Integer code;
    private String activity;
    private String desc;

    SubEventEnums(Integer code, String activity, String desc) {
        this.code = code;
        this.activity = activity;
        this.desc = desc;
    }

    public static SubEventEnums getByCode(Integer code){
        if (code == null){
            return  SubEventEnums.game;
        }
        for (SubEventEnums subEventEnums : values()){
            if (subEventEnums.getCode().equals(code)){
                return subEventEnums;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getActivity() {
        return activity;
    }

    public void setActivity(String activity) {
        this.activity = activity;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
