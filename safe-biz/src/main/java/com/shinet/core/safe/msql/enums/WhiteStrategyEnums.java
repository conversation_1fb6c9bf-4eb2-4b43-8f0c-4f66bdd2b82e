package com.shinet.core.safe.msql.enums;

/**
 * 按校验先后顺序写
 */
public enum WhiteStrategyEnums {
    PV(1, "pvStrategyService", "历史高价值用户", "设备用户历史最大pv大于设定值, 不锁"),
    ;

    private int strategy;
    private String serviceName;
    private String template;
    private String desc;

    WhiteStrategyEnums(int strategy, String serviceName, String template, String desc) {
        this.strategy = strategy;
        this.serviceName = serviceName;
        this.template = template;
        this.desc = desc;
    }

    public int getStrategy() {
        return strategy;
    }

    public String getServiceName() {
        return serviceName;
    }

    public String getTemplate() {
        return template;
    }

    public String getDesc() {
        return desc;
    }
}
