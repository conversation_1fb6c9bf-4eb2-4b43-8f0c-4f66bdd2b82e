package com.shinet.core.safe.msql.mapper;

import com.shinet.core.safe.msql.entity.AndroidLockRst;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
public interface AndroidLockRstMapper extends BaseMapper<AndroidLockRst> {
    @Update("create table if not exists  `${tablePre}` like ${tablePre}_tmp")
    int createOrderUnionTable(@Param("tablePre") String tablePre);
    @Update("rename table  ${tablePre} to ${tablePre}_${tableEnd}")
    void renameTable(@Param("tablePre") String tablePre,@Param("tableEnd") String tableEnd);
    @Update("truncate table  ${tablePre}_${tableEnd}")
    void truncateOrderUnionTable(@Param("tablePre") String tablePre,@Param("tableEnd") String tableEnd);
    @Update("drop table  ${tablePre}_${tableEnd}")
    void dropOrderUnionTable(@Param("tablePre") String tablePre,@Param("tableEnd") String tableEnd);
}
