package com.shinet.core.safe.msql.mapper;

import com.shinet.core.safe.dto.BasicApp;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/5/8
 */
public interface BasicAppMapper {

    @Select("select * from `core-safe`.basic_app")
    List<BasicApp> queryAppList();

    @Insert("insert into basic_app(app_id,product,product_name,product_group) values(#{appId},#{product},#{productName},#{productGroup})")
    int addNewApp(BasicApp app);

    @Update("update basic_app set product = #{product} where app_id = #{appId}")
    int updateApp(String product, Integer appId);

    @Update("update basic_app set product_group = #{productGroup} where app_id = #{appId}")
    int updateAppProductGroup(String productGroup, Integer appId);
}
