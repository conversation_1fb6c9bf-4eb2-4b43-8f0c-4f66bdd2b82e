package com.shinet.core.safe.msql.mapper;

import com.shinet.core.safe.msql.entity.ByteUserDevice;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-18
 */
public interface ByteUserDeviceMapper extends BaseMapper<ByteUserDevice> {
    @Insert({
            "<script>",
            "INSERT INTO `core-safe`.`byte_user_device_${tend}` (" +
                    "`user_id`," +
                    "`device_id`," +
                    "`byte_did`," +
                    "`byte_service`," +
                    "`app_id`," +
                    "`access_key`," +
                    "`product`," +
                    "`ip`," +
                    "`os`," +
                    "`channel`," +
                    "`brand`," +
                    "`pkg_id`," +
                    "`gps`," +
                    "`app_version`," +
                    "`os_version`," +
                    "`rom_version`," +
                    "`tags`," +
                    "`tags_name`," +
                    "`score`," +
                    "`detail`," +
                    "`device_status`," +
                    "`create_time`," +
                    "`update_time`, " +
                    "`trans_id` " +
                    ")" +
                    "VALUES" +
                    "(" +
                    "#{byteUserDevice.userId}," +
                    "#{byteUserDevice.deviceId}," +
                    "#{byteUserDevice.byteDid}," +
                    "#{byteUserDevice.byteService}," +
                    "#{byteUserDevice.appId}," +
                    "#{byteUserDevice.accessKey}," +
                    "#{byteUserDevice.product}," +
                    "#{byteUserDevice.ip}," +
                    "#{byteUserDevice.os}," +
                    "#{byteUserDevice.channel}," +
                    "#{byteUserDevice.brand}," +
                    "#{byteUserDevice.pkgId}," +
                    "#{byteUserDevice.gps}," +
                    "#{byteUserDevice.appVersion}," +
                    "#{byteUserDevice.osVersion}," +
                    "#{byteUserDevice.romVersion}," +
                    "#{byteUserDevice.tags}," +
                    "#{byteUserDevice.tagsName}," +
                    "#{byteUserDevice.score}," +
                    "#{byteUserDevice.detail}," +
                    "#{byteUserDevice.deviceStatus}," +
                    "#{byteUserDevice.createTime}," +
                    "#{byteUserDevice.updateTime}, " +
                    "#{byteUserDevice.transId} " +
                    ");"+
            "</script>"
    })
    public int saveDevice(@Param("byteUserDevice")ByteUserDevice byteUserDevice,@Param("tend")String tend);
    @Select({
            "<script>",
            "Select count(*) from `core-safe`.`byte_user_device_${tend}`                        " +
            "   where   product = #{product}                                                       " +
            "       and      user_id = #{userId}                                                 " +
            "</script>"
    })
    public int countByteUser(@Param("product")String product, @Param("userId")Long userId, @Param("tend")String tend);
}
