package com.shinet.core.safe.msql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shinet.core.safe.msql.entity.HighQualityDeviceReleaseRecord;
import org.apache.ibatis.annotations.Insert;
import org.mapstruct.Mapper;

@Mapper
public interface HighQualityDeviceReleaseRecordMapper extends BaseMapper<HighQualityDeviceReleaseRecord> {

    @Insert("INSERT IGNORE INTO high_quality_device_release_record " +
            "(os, app_id, product, device_type, device_id, strategy, logday, create_time, update_time) " +
            "VALUES (#{os}, #{appId} ,#{product}, #{deviceType}, #{deviceId}, #{strategy}, #{logday}, #{createTime}, #{updateTime})")
    int insertIfNotExist(HighQualityDeviceReleaseRecord record);

}
