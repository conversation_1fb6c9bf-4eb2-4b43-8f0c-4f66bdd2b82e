package com.shinet.core.safe.msql.mapper;

import com.shinet.core.safe.msql.entity.LockIosBlack;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
public interface LockIosBlackMapper extends BaseMapper<LockIosBlack> {
    /**
     * select m.caid,m.city,count(1) as cnum from (
     * select product,caid,city  from lock_ios_rst where dsp='nodsp' and LENGTH(caid)>0 GROUP BY product,caid,city
     * ) as m GROUP BY m.caid ,m.city HAVING cnum>8 order by cnum desc;
     */

    @Select("select m.caid as caid,m.city as city,count(1) as pnum from ( select product,caid,city  from lock_ios_rst where dsp='nodsp' and ip not in ('************', '***************', '***************', '***************', '***************', '***************') and LENGTH(caid)>0 GROUP BY product,caid,city ) as m GROUP BY m.caid ,m.city HAVING pnum>'${tnum}' order by pnum desc ")
    List<LockIosBlack> queryLockCaid(@Param("tnum") Integer tnum);
}
