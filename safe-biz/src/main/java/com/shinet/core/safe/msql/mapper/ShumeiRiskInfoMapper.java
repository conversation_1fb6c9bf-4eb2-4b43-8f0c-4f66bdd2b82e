package com.shinet.core.safe.msql.mapper;

import com.shinet.core.safe.msql.entity.ShumeiRiskInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-24
 */
@Repository
public interface ShumeiRiskInfoMapper extends BaseMapper<ShumeiRiskInfo> {


    @Insert({
            "<script>",
            "INSERT INTO `core-safe`.`${table}` (" ,
                    "`user_id`," ,
                    "`device_id`," ,
                    "`shumei_device_id`," ,
                    "`request_id`," ,
                    "`event_request_id`," ,
                    "`event_type`," ,
                    "`app_id`," ,
                    "`pkg_id`," ,
                    "`product`," ,
                    "`ip`," ,
                    "`risk_level`," ,
                    "`event_risk_desc`," ,
                    "`model_high`," ,
                    "`hits`," ,
                    "`channel`," ,
                    "`risk_ip`," ,
                    "`os`," ,
                    "`os_version`," ,
                    "`app_version`," ,
                    "`imei`," ,
                    "`oaid`," ,
                    "`mac`," ,
                    "`android_id`," ,
                    "`brand`," ,
                    "`pc_emulator`," ,
                    "`pc_emulator_last_ts`," ,
                    "`cloud_device`," ,
                    "`cloud_device_last_ts`," ,
                    "`altered_device`," ,
                    "`altered_device_last_ts`," ,
                    "`multi_boxing`," ,
                    "`multi_boxing_last_ts`," ,
                    "`faker_device`," ,
                    "`faker_device_last_ts`," ,
                    "`farmer_device`," ,
                    "`farmer_device_last_ts`," ,
                    "`offerwall_device`," ,
                    "`offerwall_device_last_ts`," ,
                    "`phone_emulator`," ,
                    "`phone_emulator_last_ts`," ,
                    "`label1`," ,
                    "`label2`," ,
                    "`label3`," ,
                    "`description`," ,
                    "`hit_time`," ,
                    "`hit_detail`," ,
                    "`create_time`," ,
                    "`update_time` " ,
                    ")" ,
                    "VALUES" ,
                    "(" ,
                    "#{shumeiRiskInfo.userId}," ,
                    "#{shumeiRiskInfo.deviceId}," ,
                    "#{shumeiRiskInfo.shumeiDeviceId}," ,
                    "#{shumeiRiskInfo.requestId}," ,
                    "#{shumeiRiskInfo.eventRequestId}," ,
                    "#{shumeiRiskInfo.eventType}," ,
                    "#{shumeiRiskInfo.appId}," ,
                    "#{shumeiRiskInfo.pkgId}," ,
                    "#{shumeiRiskInfo.product}," ,
                    "#{shumeiRiskInfo.ip}," ,
                    "#{shumeiRiskInfo.riskLevel}," ,
                    "#{shumeiRiskInfo.eventRiskDesc}," ,
                    "#{shumeiRiskInfo.modelHigh}," ,
                    "#{shumeiRiskInfo.hits}," ,
                    "#{shumeiRiskInfo.channel}," ,
                    "#{shumeiRiskInfo.riskIp}," ,
                    "#{shumeiRiskInfo.os}," ,
                    "#{shumeiRiskInfo.osVersion}," ,
                    "#{shumeiRiskInfo.appVersion}," ,
                    "#{shumeiRiskInfo.imei}," ,
                    "#{shumeiRiskInfo.oaid}," ,
                    "#{shumeiRiskInfo.mac}," ,
                    "#{shumeiRiskInfo.androidId}," ,
                    "#{shumeiRiskInfo.brand}," ,
                    "#{shumeiRiskInfo.pcEmulator}," ,
                    "#{shumeiRiskInfo.pcEmulatorLastTs}," ,
                    "#{shumeiRiskInfo.cloudDevice}," ,
                    "#{shumeiRiskInfo.cloudDeviceLastTs}," ,
                    "#{shumeiRiskInfo.alteredDevice}," ,
                    "#{shumeiRiskInfo.alteredDeviceLastTs}," ,
                    "#{shumeiRiskInfo.multiBoxing}," ,
                    "#{shumeiRiskInfo.multiBoxingLastTs}," ,
                    "#{shumeiRiskInfo.fakerDevice}," ,
                    "#{shumeiRiskInfo.fakerDeviceLastTs}," ,
                    "#{shumeiRiskInfo.farmerDevice}," ,
                    "#{shumeiRiskInfo.farmerDeviceLastTs}," ,
                    "#{shumeiRiskInfo.offerwallDevice}," ,
                    "#{shumeiRiskInfo.offerwallDeviceLastTs}," ,
                    "#{shumeiRiskInfo.phoneEmulator}," ,
                    "#{shumeiRiskInfo.phoneEmulatorLastTs}," ,
                    "#{shumeiRiskInfo.label1}," ,
                    "#{shumeiRiskInfo.label2}," ,
                    "#{shumeiRiskInfo.label3}," ,
                    "#{shumeiRiskInfo.description}," ,
                    "#{shumeiRiskInfo.hitTime}," ,
                    "#{shumeiRiskInfo.hitDetail}," ,
                    "#{shumeiRiskInfo.createTime}," ,
                    "#{shumeiRiskInfo.updateTime} " ,
                    ");",
            "</script>"
    })
    void saveRiskInfo(@Param("shumeiRiskInfo") ShumeiRiskInfo shumeiRiskInfo, @Param("table") String table);
}
