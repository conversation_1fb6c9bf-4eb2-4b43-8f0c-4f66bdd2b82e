package com.shinet.core.safe.msql.mapper;

import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

@Repository
public interface TableExtMap {
    @Update("create table if not exists  `byte_user_device_${tableEnd}` like byte_user_device")
    int createByteClick(@Param("tableEnd") String tableEnd);

    /**
     * 判断表是否存在
     *
     * @param tableEnd
     * @return
     */
    @Select("select count(*) > 0 from `information_schema`.tables where table_name ='byte_user_device_${tableEnd}'")
    Boolean existByteTable(@Param("tableEnd") String tableEnd);


    /**
     * 创建数美风险识别表
     * @param dateStr
     * @return
     */
    @Update("create table if not exists  `${tableName}_${dateStr}` like ${tableName}")
    int createShuMeiRiskInfoTable(@Param("tableName")String tableName, @Param("dateStr") String dateStr);

    @Select("select count(*) > 0 from `information_schema`.tables where table_name ='${tableName}_${dateStr}'")
    Boolean existShuMeiTable(@Param("tableName")String tableName, @Param("dateStr") String dateStr);


    @Delete("DROP TABLE IF EXISTS `byte_user_device_${before8Str}`")
    void dropIfExist(@Param("before8Str") String before8Str);
}
