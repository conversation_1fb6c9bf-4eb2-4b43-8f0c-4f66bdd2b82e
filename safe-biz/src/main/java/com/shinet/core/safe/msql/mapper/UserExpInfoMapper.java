package com.shinet.core.safe.msql.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shinet.core.safe.msql.entity.UserExpInfo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-10
 */
public interface UserExpInfoMapper extends BaseMapper<UserExpInfo> {

    @Insert("insert into `core-safe`.client_test_record(type,user_id,pos_id,ad_type,create_time,update_time) values(#{type},#{userId},#{posId},#{adType},#{date},#{date})")
    int insertClientTestRecord(@Param("type") String type, @Param("userId") String userId, @Param("posId") String posId, @Param("adType") String adType, @Param("date") Date date);
}
