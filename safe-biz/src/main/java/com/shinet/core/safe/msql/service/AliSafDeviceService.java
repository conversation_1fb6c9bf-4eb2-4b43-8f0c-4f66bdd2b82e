package com.shinet.core.safe.msql.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.aliyuncs.saf.model.v20180919.ExecuteRequestRequest;
import com.aliyuncs.saf.model.v20180919.ExecuteRequestResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class AliSafDeviceService {

    private static String regionId = "cn-beijing";
    public static final String AcesskeyId = "ZQLAa4h98ulSs0Uw";
    public static final String secertKey = "eCRMEppAVqSnIqk5yoi5VrQLsN85nd";

    protected static String getDomain(boolean isVpc) {
        String product = "saf";

        if (isVpc) {
            product += "-vpc";
        }

        if ("cn-shanghai".equals(regionId)) {
            return product + ".cn-shanghai.aliyuncs.com";
        }

        if ("cn-hangzhou".equals(regionId)) {
            return product + ".cn-hangzhou.aliyuncs.com";
        }

        if ("cn-shenzhen".equals(regionId)) {
            return product + ".cn-shenzhen.aliyuncs.com";
        }

        if ("cn-zhangjiakou".equals(regionId)) {
            return product + ".cn-zhangjiakou.aliyuncs.com";
        }
        if ("cn-beijing".equals(regionId)) {
            return product + ".cn-beijing.aliyuncs.com";
        }

        return "saf.cn-shanghai.aliyuncs.com";
    }


    public ExecuteRequestResponse.Data veryfyDeviceIp(String ip) {
        ExecuteRequestRequest executeRequestRequest = new ExecuteRequestRequest();
        executeRequestRequest.setMethod(com.aliyuncs.http.MethodType.POST); // 指定请求方法
        //服务的产品码：address_validation/email_risk/coupon_abuse/account_abuse等
        executeRequestRequest.setService("risk_intelligence_ip");
        // 业务详细参数，具体见文档里的业务参数部分,不需要的参数就不需要设置
        Map<String, Object> serviceParams = new HashMap<String, Object>();

        // 调用参数
        serviceParams.put("ip", ip);

        executeRequestRequest.setServiceParameters(JSONObject.toJSONString(serviceParams));
        /**
         * 请务必设置超时时间
         */
        executeRequestRequest.setReadTimeout(3000);

        try {
            executeRequestRequest.setHttpContent(JSONObject.toJSONString(serviceParams).getBytes("UTF-8"), "UTF-8", FormatType.JSON);
            IClientProfile profile = DefaultProfile.getProfile(regionId, AcesskeyId, secertKey);
            DefaultProfile.addEndpoint(regionId, "saf", getDomain(false));
            DefaultAcsClient client = new DefaultAcsClient(profile);
            ExecuteRequestResponse httpResponse = client.getAcsResponse(executeRequestRequest);
            log.info("requestId=" + httpResponse.getRequestId()+
                    " data=" + JSONObject.toJSONString(httpResponse.getData())+" message=" + JSONObject.toJSONString(httpResponse.getMessage())+" code=" + JSONObject.toJSONString(httpResponse.getCode()));
            return httpResponse.getData();
        } catch (Exception e) {
            log.error("",e);
        }
        return null;
    }

    public ExecuteRequestResponse.Data veryfyDevice(String deviceToken) {
        long dtime = System.currentTimeMillis();
        ExecuteRequestRequest executeRequestRequest = new ExecuteRequestRequest();
        executeRequestRequest.setMethod(com.aliyuncs.http.MethodType.POST); // 指定请求方法
        //服务的产品码：address_validation/email_risk/coupon_abuse/account_abuse。
        String service = "device_risk";
        executeRequestRequest.setService(service);

        // 业务详细参数，具体见文档里的业务参数部分,不需要的参数就不需要设置
        Map<String, Object> serviceParams = new HashMap<String, Object>();

        //手机号，国内11位，国外参照文档
        serviceParams.put("deviceToken", deviceToken);
        executeRequestRequest.setServiceParameters(JSONObject.toJSONString(serviceParams));

        dtime = System.currentTimeMillis();
        /**
         * 请务必设置超时时间
         */
        // executeRequestRequest.setConnectTimeout(10000);
        executeRequestRequest.setReadTimeout(2000);
        try {
            executeRequestRequest.setHttpContent(JSONObject.toJSONString(serviceParams).getBytes("UTF-8"), "UTF-8", FormatType.JSON);
            IClientProfile profile = DefaultProfile.getProfile(regionId, AcesskeyId, secertKey);
            DefaultProfile.addEndpoint(regionId, "saf", getDomain(false));
            DefaultAcsClient client = new DefaultAcsClient(profile);
            ExecuteRequestResponse httpResponse = client.getAcsResponse(executeRequestRequest);
            log.info("cost " + (System.currentTimeMillis() - dtime)+" "+ JSON.toJSONString(httpResponse));
            return httpResponse.getData();
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

}