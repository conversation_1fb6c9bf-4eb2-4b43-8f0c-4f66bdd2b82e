package com.shinet.core.safe.msql.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.saf.model.v20180919.ExecuteRequestResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.safe.hsq.req.RiskDeviceReq;
import com.shinet.core.safe.msql.config.SafeSwitcher;
import com.shinet.core.safe.msql.entity.AliUserDevice;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.enums.DeviceStatus;
import com.shinet.core.safe.msql.mapper.AliUserDeviceMapper;
import com.shinet.core.safe.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-11
 */
@Service
@Slf4j
public class AliUserDeviceService extends ServiceImpl<AliUserDeviceMapper, AliUserDevice> {
    @Autowired
    AliSafDeviceService aliSafDeviceService;
    @Autowired
    UserAuthService userAuthService;
    @Autowired
    SafeSwitcher safeSwitcher;
    public boolean queryRiskDeivce(RiskDeviceReq riskDeviceReq, CommonHeaderDTO commonHeaderDTO){
        if(safeSwitcher.aliReq){
            if(userAuthService.checkAuth(commonHeaderDTO)){
                LambdaQueryWrapper<AliUserDevice> clickUserEvent = Wrappers.<AliUserDevice>lambdaQuery().eq(AliUserDevice::getProduct, riskDeviceReq.getProduct())
                        .eq(AliUserDevice::getUserId, CommonHeaderDTO.getUserId(commonHeaderDTO)).eq(AliUserDevice::getDeviceId,commonHeaderDTO.getDeviceId()).gt(AliUserDevice::getCreateTime, DateUtils.getStartOfDay());
//        Integer cnum = baseMapper.selectCount(clickUserEvent);
//        if(cnum>0){
//            log.info("调用过多 "+CommonHeaderDTO.getUserId(commonHeaderDTO));
//            return true;
//        }
                ExecuteRequestResponse.Data rdata = aliSafDeviceService.veryfyDevice(riskDeviceReq.getAliDevice());
//            ExecuteRequestResponse.Data ipData = aliSafDeviceService.veryfyDeviceIp(commonHeaderDTO.getIp());
                saveRiskToDb(riskDeviceReq,commonHeaderDTO, rdata,null);
            }else{
                log.warn(commonHeaderDTO.getUserId()+ " warntoalert "+ JSON.toJSONString(commonHeaderDTO));
            }
        }
        return true;
    }

    private void saveRiskToDb(RiskDeviceReq riskDeviceReq,CommonHeaderDTO commonHeaderDTO, ExecuteRequestResponse.Data data, ExecuteRequestResponse.Data ipData) {
        AliUserDevice aliUserDevice = new AliUserDevice();

        aliUserDevice.setAliDtoken(riskDeviceReq.getAliDevice());
        aliUserDevice.setProduct(riskDeviceReq.getProduct());
        aliUserDevice.setOs(commonHeaderDTO.getOs());
        aliUserDevice.setScore(data.getScore() == null ? 0d : Double.parseDouble(data.getScore()));
        aliUserDevice.setTags(data.getTags());
        aliUserDevice.setDeviceStatus(DeviceStatus.INIT.value);
        aliUserDevice.setDeviceId(commonHeaderDTO.getDeviceId());
        aliUserDevice.setAccessKey(commonHeaderDTO.getAccessKey());
        aliUserDevice.setAppId(Integer.parseInt(commonHeaderDTO.getAppId()));
        aliUserDevice.setCreateTime(new Date());
        aliUserDevice.setUpdateTime(new Date());
        aliUserDevice.setUserId(CommonHeaderDTO.getUserId(commonHeaderDTO));
        aliUserDevice.setPkgId(commonHeaderDTO.getPkgId());

        aliUserDevice.setAppVersion(commonHeaderDTO.getAppVersion());
        aliUserDevice.setBrand(commonHeaderDTO.getBrand());
        aliUserDevice.setGps(commonHeaderDTO.getGps());
        aliUserDevice.setChannel(commonHeaderDTO.getChannel());
        aliUserDevice.setOsVersion(commonHeaderDTO.getOsVersion());
        aliUserDevice.setRomVersion(commonHeaderDTO.getRomVersion());

        if(ipData!=null){
            aliUserDevice.setIp(commonHeaderDTO.getIp());
            aliUserDevice.setIpScore(ipData.getScore() == null? 0d : Double.parseDouble(ipData.getScore()));

            if(StringUtils.isNotEmpty(ipData.getExtend())){
                JSONObject jobj = JSON.parseObject(ipData.getExtend());
                aliUserDevice.setIpProxy(jobj.getInteger("isProxy"));
                aliUserDevice.setIpBase(jobj.getInteger("isBase"));
                aliUserDevice.setIpIdc(jobj.getInteger("isIdc"));
                aliUserDevice.setIpNat(jobj.getInteger("isNat"));
            }
        }

        save(aliUserDevice);
    }


    public List<AliUserDevice> queryRiskDeivce(int limitNum,List<String> productList) {
        LambdaQueryWrapper<AliUserDevice> clickUserEvent = Wrappers.<AliUserDevice>lambdaQuery()
                .eq(AliUserDevice::getDeviceStatus, DeviceStatus.INIT.value)
//                .gt(AliUserDevice::getCreateTime, DateUtils.getStartOfDay())
//                .in(AliUserDevice::getTags,tags)
                .isNotNull(AliUserDevice::getAccessKey)
                .isNotNull(AliUserDevice::getChannel)
                .ne(AliUserDevice::getTags,"no_tag")
                .last(" limit "+limitNum);

        if(productList.size()>0){
            clickUserEvent.in(AliUserDevice::getProduct,productList);
        }else {
            return new ArrayList<>();
        }
        return baseMapper.selectList(clickUserEvent);
    }

}
