package com.shinet.core.safe.msql.service;

import com.alibaba.fastjson.JSON;
import com.coohua.user.event.api.dto.ProductEntity;
import com.coohua.user.event.api.remote.rpc.AdPosRpc;
import com.shinet.core.safe.container.AppBuilder;
import com.shinet.core.safe.dto.BasicApp;
import com.shinet.core.safe.msql.mapper.BasicAppMapper;
import com.shinet.core.safe.msql.service.ioslock.IosLockService;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Service
public class BasicAppService {

    @MotanReferer(basicReferer = "user-event")
    private AdPosRpc adPosRpc;

    @Resource
    private BasicAppMapper basicAppMapper;
    @Value("${env}")
    private String env;
    @Autowired
    IosLockService iosLockService;

    public List<BasicApp> queryBasicList(){
        return basicAppMapper.queryAppList();
    }

    @Scheduled(fixedDelay = 10 * 60 * 1000)
    public void refreshApp(){
        log.info("开始更新 APP...");
        List<BasicApp> appList = basicAppMapper.queryAppList();
        AppBuilder.refreshBasicApp(appList);
        if ("fat".equals(env)){
            log.info(" 测试 不同步");
            return;
        }
        List<ProductEntity> productEntityList = adPosRpc.queryProductDict();
        // 同步到表，若变更一起同步，关注 product 和 appId 的关联关系
        Map<Integer,BasicApp> appMap = appList.stream().collect(Collectors.toMap(BasicApp::getAppId, r->r,(r1, r2)->r1));
        //iosLockService.allLockTest("北京","kuaishou","aaa",false, "fgmm", false);
        BasicApp app = new BasicApp();
        try {
            for (ProductEntity productEntity : productEntityList){
                app = appMap.get(productEntity.getId());
                if (app == null){
                    BasicApp record = new BasicApp();
                    record.setAppId(productEntity.getId());
                    record.setProduct(productEntity.getProduct());
                    record.setProductName(productEntity.getProductName());
                    record.setProductGroup(productEntity.getProductGroup());
                    int count = basicAppMapper.addNewApp(record);
                    if (count > 0){
                        log.info("==> 新增 App:{}", JSON.toJSONString(record));
                    }
                }else {
                    if (!app.getProduct().equals(productEntity.getProduct())){
                        app.setAppId(productEntity.getId());
                        app.setProduct(productEntity.getProduct());
                        int count = basicAppMapper.updateApp(app.getProduct(), app.getAppId());
                        if (count > 0){
                            log.info("==> 更新 App:{}", JSON.toJSONString(app));
                        }
                    }
                    if (app.getProductGroup() == null || !app.getProductGroup().equals(productEntity.getProductGroup())) {
                        app.setProductGroup(productEntity.getProductGroup());
                        app.setAppId(productEntity.getId());
                        int count = basicAppMapper.updateAppProductGroup(app.getProductGroup(), app.appId());
                        if (count > 0){
                            log.info("==> 更新 App:{}", JSON.toJSONString(app));
                        }
                    }
                }
            }
        }catch (Exception e){
            log.error("问题:" + app.getProduct() +e);
        }
//        iosLockService.allLockTest("北京","nodsp","111",true,"qzzysjios",true);
//        iosLockService.allLockTest("北京","nodsp","111",true,"ycdzz",true);
//        iosLockService.allLockTest("北京","nodsp","111",true,"kxssp2",true);

        log.info("结束更新 APP...");

    }


}
