package com.shinet.core.safe.msql.service;

import com.alibaba.fastjson.JSON;
import com.shinet.core.safe.hsq.req.CsRiskBean;
import com.shinet.core.safe.hsq.req.CsRiskDeviceReq;
import com.shinet.core.safe.msql.config.SafeSwitcher;
import com.shinet.core.safe.msql.entity.ByteAppConfig;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.enums.ByteEventEnums;
import com.shinet.core.safe.msql.enums.SubEventEnums;
import com.shinet.core.safe.util.CustomStringUtils;
import com.volcengine.model.request.RiskDetectionRequest;
import com.volcengine.model.response.RiskDetectionResponse;
import com.volcengine.service.businessSecurity.BusinessSecurityService;
import com.volcengine.service.businessSecurity.impl.BusinessSecurityServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.volcengine.helper.Const.RiskDetection;
import static java.util.stream.Collectors.toMap;

@Component
@Slf4j
public class ByteSafeService implements InitializingBean {
    @Autowired
    ByteUserDeviceService byteUserDeviceService;
    private static String accessKey = "AKLTM2RkOGMyN2VmZGZkNDA5YTg3YzQyYjQwMWVjNDRjYjM";
    private static String secretKey = "WTJVNE1UQTJOMlk1T0RsbU5HTXhNVGszTlRKaE1XVmlOelk1WVRsalpEaw==";

    private static final ScheduledThreadPoolExecutor APP_CONFIG_REFRESH_POOL = new ScheduledThreadPoolExecutor(1);

    /**
     * 火山SDK：产品-appId映射
     */
    private static Map<String,Integer> BYTE_APP_CONFIG;

    @Autowired
    UserAuthService userAuthService;

    @Resource
    private ByteAppConfigService byteAppConfigService;

    static BusinessSecurityService businessSecurityService = BusinessSecurityServiceImpl.getInstance();
    @Autowired
    SafeSwitcher safeSwitcher;
    public boolean csRisk(CommonHeaderDTO commonHeaderDTO, CsRiskDeviceReq csRiskDeviceReq,
                          ByteEventEnums byteEventEnums, SubEventEnums subEventEnums) {
        if(StringUtils.isBlank(csRiskDeviceReq.getDid())){
            log.warn(commonHeaderDTO.getUserId()+" 签名错误 "+JSON.toJSONString(commonHeaderDTO));
            return false;
        }

        if(safeSwitcher.checkauth){
            if(!userAuthService.checkAuth(commonHeaderDTO)){
                log.warn(commonHeaderDTO.getUserId()+" 签名错误 "+JSON.toJSONString(commonHeaderDTO));
                return false;
            }
        }

        boolean isInDb = false;
        if(safeSwitcher.byteQdb){
            isInDb = byteUserDeviceService.queryRiskDeivce(csRiskDeviceReq,commonHeaderDTO);
        }
        if(!isInDb){

            businessSecurityService.setAccessKey(accessKey);
            businessSecurityService.setSecretKey(secretKey);
            try {
                RiskDetectionRequest riskResultRequest = new RiskDetectionRequest();
                // 项目组请求信息错误 在这做处理 (appId 633 product 应为 zmcash  错写为 tyxy)
                if ("tyxy".equals(csRiskDeviceReq.getProduct()) && "633".equals(commonHeaderDTO.getAppId()) && "IOS".equalsIgnoreCase(commonHeaderDTO.getOs())){
                    boolean flag = safeSwitcher.reqLog && "633".equals(commonHeaderDTO.getAppId());
                    if (flag){
                        log.info("appId 633 项目组请求参数 commonHeader:{} , request:{}", JSON.toJSONString(commonHeaderDTO), JSON.toJSONString(csRiskDeviceReq));
                    }
                    csRiskDeviceReq.setProduct("zmcash");
                    if (flag){
                        log.info("矫正后 信息 commonHeader:{} csRiskDeviceReq:{}", JSON.toJSONString(commonHeaderDTO), JSON.toJSONString(csRiskDeviceReq));
                    }
                }
                riskResultRequest.setAppId(BYTE_APP_CONFIG.get(csRiskDeviceReq.getProduct()));
                riskResultRequest.setService(byteEventEnums.value);
                String did =csRiskDeviceReq.getDid();
                String userId = commonHeaderDTO.getUserId()+"";
                Date odate = new Date();
                String ip = commonHeaderDTO.getIp();
                String channel = commonHeaderDTO.getChannel();

                CsRiskBean csRiskBean = new CsRiskBean();
                csRiskBean.setAccount_id(userId+"");
                csRiskBean.setAccount_type("wechat");
                csRiskBean.setChannel(channel);
                if (CustomStringUtils.isNotPureNumber(did)) {
                    csRiskBean.setBd_did(did);
                } else {
                    csRiskBean.setDid(did);
                }
                csRiskBean.setIp(ip);
                csRiskBean.setOperate_time((odate.getTime() / 1000)+"");
                if (ByteEventEnums.activity.equals(byteEventEnums) && subEventEnums != null){
                    if (SubEventEnums.game.equals(subEventEnums)){
                        csRiskBean.setActivity_id(commonHeaderDTO.getAppId()+"_playground");
                    }else {
                        csRiskBean.setActivity_id(commonHeaderDTO.getAppId()+"_other");
                    }
                    csRiskBean.setActivity_type(subEventEnums.getActivity());
                }
                String paramStr = JSON.toJSONString(csRiskBean);
                riskResultRequest.setParameters(paramStr);
                if(safeSwitcher.reqLog){
                    log.info("{} {} 请求 {}", csRiskDeviceReq.getProduct(), commonHeaderDTO.getOs(), JSON.toJSONString(riskResultRequest));
                }
                RiskDetectionResponse riskDetectionResponse = businessSecurityService.RiskDetection(riskResultRequest);
                if(safeSwitcher.reqLog){
                    log.info("返回: {}", JSON.toJSONString(riskDetectionResponse));
                }
                if(riskDetectionResponse!= null
                        && riskDetectionResponse.getResult()!= null
                        && riskDetectionResponse.getResult().getCode()==0){
                    byteUserDeviceService.saveRiskToDb(csRiskDeviceReq,commonHeaderDTO,riskDetectionResponse,byteEventEnums);
                }else{
//                    log.warn("bytesafeerror "+JSON.toJSONString(riskDetectionResponse));
                }
                return true;
            } catch (Exception e) {
                log.error("{} Ex:{}",csRiskDeviceReq.getDid(),e);
                e.printStackTrace();
            }
        }
        return false;
    }


    public static void main(String[] args) throws Exception {

        BusinessSecurityService businessSecurityService = BusinessSecurityServiceImpl.getInstance();
        // call below method if you dont set ak and sk in ～/.volc/config

        businessSecurityService.setAccessKey("AKLTM2RkOGMyN2VmZGZkNDA5YTg3YzQyYjQwMWVjNDRjYjM");
        businessSecurityService.setSecretKey("WTJVNE1UQTJOMlk1T0RsbU5HTXhNVGszTlRKaE1XVmlOelk1WVRsalpEaw==");
        // risk result
        // {"account_id":"test123456","operate_time":"**********","account_type":"wechat","ip":"*******","channel":"baidu"}
        RiskDetectionRequest riskResultRequest = new RiskDetectionRequest();
        riskResultRequest.setAppId(238068);
        riskResultRequest.setService("register");
        String did = "***************";
//            riskResultRequest.setStartTime(new Long(System.currentTimeMillis()/1000- DateTimeConstants.SECONDS_PER_HOUR).intValue());
//            riskResultRequest.setEndTime(new Long(System.currentTimeMillis()/1000).intValue());
//            riskResultRequest.setPageNum(1);
//            riskResultRequest.setPageSize(10);
        String userId = "123";
        Date odate = new Date();
        String ip = "127.0.0.1";
        String channel = "csj";

        CsRiskBean csRiskBean = new CsRiskBean();
        csRiskBean.setAccount_id("238068");
        csRiskBean.setAccount_type("wechat");
        csRiskBean.setChannel(channel);
        csRiskBean.setDid(did);
        csRiskBean.setIp(ip);
        csRiskBean.setOperate_time((odate.getTime() / 1000)+"");
        String paramStr = JSON.toJSONString(csRiskBean);
        riskResultRequest.setParameters(paramStr);
        RiskDetectionResponse riskDetectionResponse = businessSecurityService.RiskDetection(riskResultRequest);
        log.info("返回：" + JSON.toJSONString(riskDetectionResponse));
        System.out.println(JSON.toJSONString(riskDetectionResponse));
        log.info(JSON.toJSONString(riskDetectionResponse));
    }

    /**
     * 更新火山SDK配置
     */
    private void refreshByteAppConfig() {

        BYTE_APP_CONFIG = byteAppConfigService.list().stream()
                .collect(toMap(ByteAppConfig::getProduct, ByteAppConfig::getByteAppId));

    }

    @Override
    public void afterPropertiesSet() throws Exception {
        refreshByteAppConfig();
        APP_CONFIG_REFRESH_POOL.scheduleAtFixedRate(this::refreshByteAppConfig, 300L, 300L, TimeUnit.SECONDS);
    }

}




