package com.shinet.core.safe.msql.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.shinet.core.safe.hsq.req.CsRiskDeviceReq;
import com.shinet.core.safe.hsq.req.RiskDeviceReq;
import com.shinet.core.safe.msql.config.SafeSwitcher;
import com.shinet.core.safe.msql.entity.AliUserDevice;
import com.shinet.core.safe.msql.entity.ByteUserDevice;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.enums.ByteEventEnums;
import com.shinet.core.safe.msql.enums.DeviceStatus;
import com.shinet.core.safe.msql.mapper.ByteUserDeviceMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.safe.msql.mapper.TableExtMap;
import com.shinet.core.safe.util.DateUtils;
import com.volcengine.model.response.RiskDetectionResponse;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.units.qual.A;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2021-08-18
*/
@Service
@Slf4j
public class ByteUserDeviceService extends ServiceImpl<ByteUserDeviceMapper, ByteUserDevice> {
    @Autowired
    UserAuthService userAuthService;
    @Autowired
    SafeSwitcher safeSwitcher;
    @Autowired
    private UserGrayService userGrayService;

    public boolean queryRiskDeivce(CsRiskDeviceReq riskDeviceReq, CommonHeaderDTO commonHeaderDTO){
        LambdaQueryWrapper<ByteUserDevice> clickUserEvent = Wrappers.<ByteUserDevice>lambdaQuery().eq(ByteUserDevice::getProduct, riskDeviceReq.getProduct())
                .eq(ByteUserDevice::getUserId, CommonHeaderDTO.getUserId(commonHeaderDTO)).eq(ByteUserDevice::getDeviceId,commonHeaderDTO.getDeviceId()).gt(ByteUserDevice::getCreateTime, DateUtils.getStartOfDay());
        Integer cnum = baseMapper.selectCount(clickUserEvent);
        if(cnum>0){
            log.info("调用过多 "+CommonHeaderDTO.getUserId(commonHeaderDTO));
            return true;
        }
        return false;
    }

    public boolean existsByteUser(String product,
                                  Long userId,
                                  int dayCount) {
        Date now = new Date();
        for(int i = 0; i < dayCount; i++){
            String cdateStr = DateUtils.formatDateForYMDSTR(dateIncreaseByDay(now, - i));
            String tend = "_" + cdateStr;

            if(this.baseMapper.countByteUser(product, userId,tend) > 0){
                return true;
            }
        }
        return false;
    }

    public static Date dateIncreaseByDay(Date date, int days) {

        Calendar cal = GregorianCalendar.getInstance(TimeZone
                .getTimeZone("GMT"));
        cal.setTime(date);
        cal.add(Calendar.DATE, days);

        return cal.getTime();
    }

    public void saveRiskToDb(CsRiskDeviceReq csRiskDeviceReq, CommonHeaderDTO commonHeaderDTO, RiskDetectionResponse riskDetectionResponse, ByteEventEnums byteEventEnums) {
        ByteUserDevice byteUserDevice = new ByteUserDevice();

        RiskDetectionResponse.RiskDetectionResult riskDetectionResult = riskDetectionResponse.getResult();
        byteUserDevice.setProduct(csRiskDeviceReq.getProduct());
        byteUserDevice.setOs(commonHeaderDTO.getOs());
        byteUserDevice.setByteDid(csRiskDeviceReq.getDid());
        byteUserDevice.setScore(riskDetectionResult.getData().getScore() == null ? 0 : riskDetectionResult.getData().getScore());
        byteUserDevice.setTags(JSON.toJSONString(riskDetectionResult.getData().getTags()));
        byteUserDevice.setTagsName(convertCodeNames(byteUserDevice.getTags()));
        byteUserDevice.setDeviceStatus(DeviceStatus.INIT.value);
        byteUserDevice.setDeviceId(commonHeaderDTO.getDeviceId());
//        byteUserDevice.setAccessKey(commonHeaderDTO.getAccessKey());
        byteUserDevice.setAppId(Integer.parseInt(commonHeaderDTO.getAppId()));
        byteUserDevice.setCreateTime(new Date());
        byteUserDevice.setUpdateTime(new Date());
        byteUserDevice.setUserId(CommonHeaderDTO.getUserId(commonHeaderDTO));
        byteUserDevice.setPkgId(commonHeaderDTO.getPkgId());
        byteUserDevice.setByteService(byteEventEnums.value);
//        byteUserDevice.setAppVersion(commonHeaderDTO.getAppVersion());
//        byteUserDevice.setBrand(commonHeaderDTO.getBrand());
//        byteUserDevice.setGps(commonHeaderDTO.getGps());
//        byteUserDevice.setChannel(commonHeaderDTO.getChannel());
//        byteUserDevice.setOsVersion(commonHeaderDTO.getOsVersion());
//        byteUserDevice.setRomVersion(commonHeaderDTO.getRomVersion());
        byteUserDevice.setIp(commonHeaderDTO.getIp());
        byteUserDevice.setTransId(csRiskDeviceReq.getTransId());

        // 检查是否投放用户
        CompletableFuture.runAsync(()-> userGrayService.check(commonHeaderDTO,byteUserDevice));
        String cdateStr = DateUtils.formatDateForYMDSTR((new Date()));
        String tend = "_"+cdateStr;
        isExits(tend);
        this.baseMapper.saveDevice(byteUserDevice,tend);
    }
    @Autowired
    TableExtMap tableExtMap;
    private static Map<String,Boolean> isEMap = new HashMap<>();
    private void isExits(String tend){
        Boolean isE = isEMap.get(tend);
        if(isE==null || !isE){
            isE = tableExtMap.existByteTable(tend);
            if(!isE){
                tableExtMap.createByteClick(tend);
            }
            isEMap.put(tend,isE);
        }
    }

    private static Map<String,String> dmap = new HashMap<>();
    static {
        dmap.put("11001","账户黑名单");
        dmap.put("11002","账户行为风险");
        dmap.put("11003","手机号风险");
        dmap.put("11004","邮箱域名异常");
        dmap.put("11005","账号异常");
        dmap.put("11006","批量注册账号风险");
        dmap.put("11007","用户小号风险");
        dmap.put("12001","行为群组");
        dmap.put("12002","设备群组");
        dmap.put("13001","IP环境异常");
        dmap.put("14001","设备黑名单");
        dmap.put("14002","设备聚集异常");
        dmap.put("14003","设备行为异常");
        dmap.put("14004","设备环境异常");
    }



    public static String convertCodeNames(String cods){
        String codeNames = "";
        if(!StringUtils.isEmpty(cods)){
            List<String> dlist = JSON.parseArray(cods,String.class);
            codeNames = dlist.stream().map(d->dmap.get(d)).collect(Collectors.joining(","));
        }

        return codeNames;
    }

    @Scheduled(cron = "0 05 1 * * ?")
    public void dropTable(){
        String before8Str = DateUtils.formatDateForYMDSTR(DateUtil.offsetDay(new Date(),-4));
        log.info("drop table if exist... {} ",before8Str);
        tableExtMap.dropIfExist("_"+before8Str);
        log.info("drop over...");
    }



    public List<ByteUserDevice> queryRiskDeivce(int limitNum, List<String> productList) {
        LambdaQueryWrapper<ByteUserDevice> clickUserEvent = Wrappers.<ByteUserDevice>lambdaQuery()
                .eq(ByteUserDevice::getDeviceStatus, DeviceStatus.INIT.value)
//                .gt(AliUserDevice::getCreateTime, DateUtils.getStartOfDay())
//                .in(AliUserDevice::getTags,tags)
                .isNotNull(ByteUserDevice::getAccessKey)
                .isNotNull(ByteUserDevice::getChannel)
                .last(" limit "+limitNum);

        if(productList.size()>0){
            clickUserEvent.in(ByteUserDevice::getProduct,productList);
        }else {
            return new ArrayList<>();
        }
        return baseMapper.selectList(clickUserEvent);
    }
}
