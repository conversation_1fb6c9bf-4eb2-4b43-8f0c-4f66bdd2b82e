package com.shinet.core.safe.msql.service;

import com.shinet.core.safe.msql.entity.ByteminConf;
import com.shinet.core.safe.msql.entity.IosIpRst;
import com.shinet.core.safe.msql.mapper.ByteminConfMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2024-04-26
*/
@Service
public class ByteminConfService extends ServiceImpl<ByteminConfMapper, ByteminConf> {
    public ByteminConf queryByPrj(String product){
        List<ByteminConf> ipRstList = lambdaQuery().eq(ByteminConf::getProduct, product).list();
        if(ipRstList.size()>0){
            return ipRstList.get(0);
        }
        return null;
    }
}
