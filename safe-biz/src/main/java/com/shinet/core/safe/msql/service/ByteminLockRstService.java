package com.shinet.core.safe.msql.service;

import java.util.Date;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.shinet.core.safe.msql.entity.ByteUserDevice;
import com.shinet.core.safe.msql.entity.ByteminLockRst;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.entity.IosIpRst;
import com.shinet.core.safe.msql.mapper.ByteminLockRstMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.safe.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-26
 */
@Service
public class ByteminLockRstService extends ServiceImpl<ByteminLockRstMapper, ByteminLockRst> {

    public ByteminLockRst queryByProductOpenId(String product, String openId) {
        if (StringUtils.isNotBlank(product) && StringUtils.isNotBlank(openId)) {
            List<ByteminLockRst> ipRstList = lambdaQuery().eq(ByteminLockRst::getProduct, product).eq(ByteminLockRst::getIp, openId).list();
            if (ipRstList.size() > 0) {
                return ipRstList.get(0);
            }
        }
        return null;
    }

    public ByteminLockRst nwByteminLockRst(
            String product,
            String os,
            String openId,
            String ip
    ) {
        ByteminLockRst byteminLockRst = new ByteminLockRst();
        byteminLockRst.setProduct(product);
        byteminLockRst.setOs(os);
        byteminLockRst.setOpenid(openId);
        byteminLockRst.setIp(ip);
        byteminLockRst.setStoreName("bytemin");
        byteminLockRst.setCreateTime(new Date());
        byteminLockRst.setUpdateTime(new Date());
        return byteminLockRst;
    }
}
