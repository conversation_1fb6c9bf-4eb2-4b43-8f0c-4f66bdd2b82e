package com.shinet.core.safe.msql.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.entity.CustomerSuggest;
import com.shinet.core.safe.msql.mapper.CustomerSuggestMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.concurrent.TimeUnit;


/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2022-08-31
*/
@Service
@Slf4j
public class CustomerSuggestService extends ServiceImpl<CustomerSuggestMapper, CustomerSuggest> {

    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate redisTemplate;

    private static String buildCacheKey(String product,long userId){
        return String.format("api:sug:save:%s:%s",product,userId);
    }

    public void preSave( CommonHeaderDTO commonHeaderDTO, CustomerSuggest customerSuggest){
        try {
            String product = commonHeaderDTO.getProduct();
            Long userId = CommonHeaderDTO.getUserIdNoEx(commonHeaderDTO);

            String cacheKey = buildCacheKey(product, userId);

            String value = redisTemplate.opsForValue().get(cacheKey);
            if (value == null) {
                customerSuggest.setIp(commonHeaderDTO.getIp());
                customerSuggest.setCaid(commonHeaderDTO.getCaid());
                customerSuggest.setProduct(product);
                customerSuggest.setUserId(userId);
                customerSuggest.setOs(commonHeaderDTO.getOs());
                customerSuggest.setAccessKey(commonHeaderDTO.getAccessKey());
                Date now = new Date();
                customerSuggest.setCreateTime(now);
                this.save(customerSuggest);
                redisTemplate.opsForValue().set(cacheKey, "1");
                redisTemplate.expire(cacheKey, 1, TimeUnit.MINUTES);
            } else {
                log.info("用户建议操作频繁 {} {} {}", product, userId, customerSuggest.getSuggest());
            }

        } catch (Exception e) {
            log.info("用户建议存储服务异常 ", e);
        }
    }

}
