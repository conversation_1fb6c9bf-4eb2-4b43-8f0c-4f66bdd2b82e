package com.shinet.core.safe.msql.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.shinet.core.safe.core.constants.GlobalRedisConstants;
import com.shinet.core.safe.core.entity.LockDeviceBlack;
import com.shinet.core.safe.core.mapper.LockDeviceBlackMapper;
import com.shinet.core.safe.core.vo.CommonHeaderVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.BatchResult;
import org.redisson.api.RBatch;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 设备拉黑统一管理服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DeviceBlackService {

    @Autowired
    private LockDeviceBlackMapper lockDeviceBlackMapper;

    @Resource(name = "redissonClient2")
    private RedissonClient redissonClient;

    @Value("${enable.check.black.device:false}")
    private boolean enableCheckBlackDevice;

    /**
     * 批量新增拉黑设备
     *
     * @param os            操作系统：android/ios
     * @param targetType    设备类型：1-IP, 2-OAID, 3-CAID, 4-IDFA
     * @param targetListStr 逗号分隔的设备列表（最大1000条）
     * @param remark        拉黑原因备注
     * @return 新增成功数量
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchAddBlackDevices(String os, Integer targetType, String targetListStr, String remark) {
        // 1. 参数校验
        if (!validateParams(os, targetType, targetListStr)) {
            return 0;
        }

        // 2. 解析设备列表
        List<String> targetIds = parseTargetList(targetListStr);
        if (targetIds.isEmpty()) {
            log.warn("设备拉黑批量新增：解析后的设备列表为空，os={}, targetType={}", os, targetType);
            return 0;
        }

        // 3. 去重检查
        List<String> newTargetIds = filterExistingDevices(os, targetType, targetIds);
        if (newTargetIds.isEmpty()) {
            log.info("设备拉黑批量新增：所有设备均已存在，无需新增，os={}, targetType={}", os, targetType);
            return 0;
        }

        // 4. 批量插入数据库
        List<LockDeviceBlack> newRecords = buildDeviceBlackRecords(os, targetType, newTargetIds, remark);
        int insertCount = lockDeviceBlackMapper.batchInsert(newRecords);

        // 5. 同步Redis
        if (insertCount > 0) {
            syncToRedis(os, newTargetIds);
            log.info("设备拉黑批量新增成功：os={}, targetType={}, 新增数量={}", os, targetType, insertCount);
        }

        return insertCount;
    }


    /**
     * 统一设备拉黑检测
     *
     * @param commonHeaderVo 请求头信息
     * @return 是否拉黑
     */
    public boolean isDeviceBlocked(CommonHeaderVo commonHeaderVo) {
        if (!enableCheckBlackDevice) {
            return false;
        }
        if (commonHeaderVo == null || StringUtils.isBlank(commonHeaderVo.getOs())) {
            return false;
        }

        try {
            // 1. 提取有效设备标识
            List<String> deviceValues = extractDeviceValues(commonHeaderVo);
            if (deviceValues.isEmpty()) {
                return false;
            }

            // 2. 构建Redis Key列表
            List<String> redisKeys = buildRedisKeys(commonHeaderVo.getOs(), deviceValues);
            if (redisKeys.isEmpty()) {
                return false;
            }

            // 3. 批量查询Redis
            return batchCheckRedis(redisKeys);

        } catch (Exception e) {
            log.error("设备拉黑检测异常，返回false保护主流程：os={}, error={}",
                    commonHeaderVo.getOs(), e.getMessage(), e);
            return false;
        }
    }


    /**
     * 参数校验
     */
    private boolean validateParams(String os, Integer targetType, String targetListStr) {
        if (StringUtils.isBlank(os) || targetType == null || StringUtils.isBlank(targetListStr)) {
            log.warn("设备拉黑批量新增：参数不完整，os={}, targetType={}, targetListStr={}",
                    os, targetType, StringUtils.isBlank(targetListStr) ? "空" : "非空");
            return false;
        }

        if (!Arrays.asList("android", "ios").contains(os.toLowerCase())) {
            log.warn("设备拉黑批量新增：不支持的操作系统，os={}", os);
            return false;
        }

        return true;
    }

    /**
     * 解析设备列表
     */
    private List<String> parseTargetList(String targetListStr) {
        return Arrays.stream(targetListStr.replace("\n", "").split(","))
                .map(String::trim)
                .filter(GlobalRedisConstants::isValidDeviceValue)
                .distinct()
                .limit(1000) // 最大1000条限制
                .collect(Collectors.toList());
    }

    /**
     * 过滤已存在的设备
     */
    private List<String> filterExistingDevices(String os, Integer targetType, List<String> targetIds) {
        List<LockDeviceBlack> existingRecords = lockDeviceBlackMapper.selectByOsAndTargetIds(os, targetIds);
        Set<String> existingTargetIds = existingRecords.stream()
                .filter(record -> record.getTargetType().equals(targetType))
                .map(LockDeviceBlack::getTargetId)
                .collect(Collectors.toSet());

        return targetIds.stream()
                .filter(targetId -> !existingTargetIds.contains(targetId))
                .collect(Collectors.toList());
    }

    /**
     * 构建设备拉黑记录
     */
    private List<LockDeviceBlack> buildDeviceBlackRecords(String os, Integer targetType,
                                                          List<String> targetIds, String remark) {
        return targetIds.stream()
                .map(targetId -> new LockDeviceBlack().setBasicInfo(os, targetType, targetId, remark))
                .collect(Collectors.toList());
    }

    /**
     * 同步到Redis
     */
    private void syncToRedis(String os, List<String> targetIds) {
        RBatch batch = redissonClient.createBatch();

        // 添加批量设置操作
        for (String targetId : targetIds) {
            String redisKey = GlobalRedisConstants.buildDeviceBlackKey(os, targetId);
            if (redisKey != null) {
                batch.getBucket(redisKey).setAsync("1");
            }
        }

        // 执行批量操作
        BatchResult<?> batchResult = batch.execute();
        log.info("设备拉黑Redis同步成功：os={}, 同步数量={}, 执行结果数量={}",
                os, targetIds.size(), batchResult.getResponses().size());
    }

    private void delRedis(String os, List<String> targetIds) {
        RBatch batch = redissonClient.createBatch();

        for (String targetId : targetIds) {
            String redisKey = GlobalRedisConstants.buildDeviceBlackKey(os, targetId);
            if (redisKey != null) {
                batch.getBucket(redisKey).deleteAsync();
            }
        }

        BatchResult<?> batchResult = batch.execute();
    }

    /**
     * 提取有效设备标识
     */
    private List<String> extractDeviceValues(CommonHeaderVo commonHeaderVo) {
        List<String> deviceValues = new ArrayList<>();

        if (GlobalRedisConstants.isValidDeviceValue(commonHeaderVo.getIp())) {
            deviceValues.add(commonHeaderVo.getIp());
        }

        if (commonHeaderVo.isAndroid()) {
            // Android：OAID
            if (GlobalRedisConstants.isValidDeviceValue(commonHeaderVo.getOaid())) {
                deviceValues.add(commonHeaderVo.getOaid());
            }
        } else if (commonHeaderVo.isIos()) {
            // iOS：CAID、IDFA
            if (GlobalRedisConstants.isValidDeviceValue(commonHeaderVo.getCaid())) {
                deviceValues.add(commonHeaderVo.getCaid());
            }
            if (GlobalRedisConstants.isValidDeviceValue(commonHeaderVo.getIdfa())) {
                deviceValues.add(commonHeaderVo.getIdfa());
            }
        }

        return deviceValues;
    }

    /**
     * 构建Redis Key列表
     */
    private List<String> buildRedisKeys(String os, List<String> deviceValues) {
        return deviceValues.stream()
                .map(deviceValue -> GlobalRedisConstants.buildDeviceBlackKey(os, deviceValue))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 批量查询Redis
     * 使用Redisson批量操作
     */
    private boolean batchCheckRedis(List<String> redisKeys) {
        if (redisKeys.isEmpty()) {
            return false;
        }

        try {
            RBatch batch = redissonClient.createBatch();

            // 添加批量查询操作
            for (String key : redisKeys) {
                batch.getBucket(key).getAsync();
            }

            // 执行批量操作并获取结果
            BatchResult<?> batchResult = batch.execute();
            List<?> responses = batchResult.getResponses();

            // 检查是否有任意key命中
            for (Object response : responses) {
                if (response != null && StringUtils.isNotBlank(response.toString())) {
                    log.info("设备拉黑检测命中：key存在值={}", response);
                    return true; // 任意命中即拉黑
                }
            }

            return false;

        } catch (Exception e) {
            log.error("设备拉黑Redis批量查询异常：keys={}, error={}", redisKeys, e.getMessage(), e);
            return false; // 异常时返回false保护主流程
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public int batchDelBlackDevices(String os, Integer targetType, String targetListStr, String remark) {
        // 1. 参数校验
        if (!validateParams(os, targetType, targetListStr)) {
            return 0;
        }

        // 2. 解析设备列表
        List<String> targetIds = parseTargetList(targetListStr);
        if (targetIds.isEmpty()) {
            log.warn("设备拉黑批量删除：解析后的设备列表为空，os={}, targetType={}", os, targetType);
            return 0;
        }

        UpdateWrapper<LockDeviceBlack> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(LockDeviceBlack::getOs, os)
                .in(LockDeviceBlack::getTargetId, targetIds);

        int delete = this.lockDeviceBlackMapper.delete(updateWrapper);

        delRedis(os, targetIds);

        return delete;
    }

    public LockDeviceBlack queryBlackDevice(String os, Integer targetType, String targetListStr, String remark) {
        // 1. 参数校验
        if (!validateParams(os, targetType, targetListStr)) {
            return null;
        }

        RBucket<Object> bucket = redissonClient.getBucket(GlobalRedisConstants.buildDeviceBlackKey(os, targetListStr));
        Object o = bucket.get();

        if (o == null) return null;

        QueryWrapper<LockDeviceBlack> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(LockDeviceBlack::getOs, os)
                .eq(LockDeviceBlack::getTargetId, targetListStr)
                .last("limit 1");

        return this.lockDeviceBlackMapper.selectOne(queryWrapper);
    }
}
