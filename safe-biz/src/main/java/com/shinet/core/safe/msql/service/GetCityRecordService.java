package com.shinet.core.safe.msql.service;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.shinet.core.safe.hsq.rsp.GdIpRsp;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.entity.GetCityRecord;
import com.shinet.core.safe.msql.entity.LockedAreaRecord;
import com.shinet.core.safe.msql.mapper.GetCityRecordMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2022-08-31
*/
@Slf4j
@Service
public class GetCityRecordService extends ServiceImpl<GetCityRecordMapper, GetCityRecord> {

    @ApolloJsonValue("${join.save.get.app.list:[680]}")
    private List<Integer> saveAppList;

    private Boolean isExist(String product,String oaId){
        return lambdaQuery().eq(GetCityRecord::getProduct,product)
                .eq(GetCityRecord::getOaId,oaId)
                .count() > 0;
    }

    public void saveIfNotExist(CommonHeaderDTO commonHeaderDTO,String city,Boolean ocpc, Boolean limitArea,
                               String product,Integer appId){
        if (!saveAppList.contains(appId)){
            return;
        }
        CompletableFuture.runAsync(()->{
            try {
                if (!isExist(product,commonHeaderDTO.getOaid())){
                    GetCityRecord cityRecord = new GetCityRecord();
                    cityRecord.setProduct(product);
                    cityRecord.setAppId(appId);
                    cityRecord.setOs(commonHeaderDTO.getOs());
                    cityRecord.setAccessKey(commonHeaderDTO.getAccessKey());
                    cityRecord.setDeviceId(commonHeaderDTO.getDeviceId());
                    cityRecord.setOaId(commonHeaderDTO.getOaid());
                    cityRecord.setImei(commonHeaderDTO.getImei());
                    cityRecord.setIp(commonHeaderDTO.getIp());
                    cityRecord.setOcpc(ocpc ?1:0);
                    cityRecord.setLimitArea(limitArea ?1:0);
                    cityRecord.setCity(city);

                    Date now = new Date();
                    cityRecord.setCreateTime(now);
                    cityRecord.setUpdateTime(now);
                    save(cityRecord);
                }
            }catch (Exception e){
                log.error("Ex:",e);
            }

        });

    }
}
