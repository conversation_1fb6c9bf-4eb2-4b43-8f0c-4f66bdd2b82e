package com.shinet.core.safe.msql.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shinet.core.safe.msql.entity.ByteUserDevice;
import com.shinet.core.safe.util.HBaseUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.TableNotFoundException;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.io.compress.Compression;
import org.apache.hadoop.hbase.io.encoding.DataBlockEncoding;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/5/9
 */
@Slf4j
@Service
public class HBaseHsService {

    @Resource
    private Connection ocpcHadoopConnection;

    private final static String TABLE_NAME = "hs_user_score";
    private final static  byte[] families = Bytes.toBytes("family");

    @PostConstruct
    public void createTable() throws IOException {
        try (Admin admin = ocpcHadoopConnection.getAdmin()) {
            // 建表
            try {
                admin.getDescriptor(TableName.valueOf(TABLE_NAME));
            } catch (TableNotFoundException te) {
                ColumnFamilyDescriptor familyDescriptor = ColumnFamilyDescriptorBuilder
                        .newBuilder(families)
                        .setCompressionType(Compression.Algorithm.ZSTD)
                        .setDataBlockEncoding(DataBlockEncoding.DIFF)
                        .build();

                TableDescriptor tableDescriptor = TableDescriptorBuilder.newBuilder(TableName.valueOf(TABLE_NAME))
                        .setColumnFamily(familyDescriptor)
                        .setCompactionEnabled(true)
                        .build();

                admin.createTable(tableDescriptor, HBaseUtils.ONLY_SPLIT_KEYS);
            }
        } catch (IOException e) {
            log.error("{}表创建失败:{}", TABLE_NAME, e);
            throw e;
        }
    }

    public void saveHsExUser(ByteUserDevice byteUserDevice){
        try (Table table = ocpcHadoopConnection.getTable(TableName.valueOf(TABLE_NAME))) {
            String key = String.format("%s:%s",byteUserDevice.getAppId(),byteUserDevice.getUserId());
            Put put = new Put(Bytes.toBytes(key));
            put.addColumn(families, Bytes.toBytes("user_id"), Bytes.toBytes(JSONObject.toJSONString(byteUserDevice)));
            table.put(put);
        } catch (IOException e) {
            log.error("QueryException:",e);
        }
    }
}
