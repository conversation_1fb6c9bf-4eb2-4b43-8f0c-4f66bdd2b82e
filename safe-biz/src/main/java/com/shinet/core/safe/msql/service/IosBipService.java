package com.shinet.core.safe.msql.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.safe.msql.entity.IosBip;
import com.shinet.core.safe.msql.mapper.IosBipMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-06-07
*/
@Service
@Slf4j
public class IosBipService extends ServiceImpl<IosBipMapper, IosBip> {
    public void addBip(String product,String ip){
        try {
            int cnum = queryBip(product,ip);
            if(cnum==0){
                IosBip iosBip = new IosBip();

                Date date = new Date();
                iosBip.setBip(ip);
                iosBip.setProduct(product);
                iosBip.setCreateTime(date);
                iosBip.setUpdateTime(date);

                save(iosBip);
                log.info("保存审核IP成功 "+product+"@"+ip);
            }
        }catch (Exception e){
            log.error("",e);
        }
    }

    public int queryBip(String product,String ip){
        LambdaQueryWrapper<IosBip> objectQueryWrapper = new QueryWrapper<IosBip>().lambda();
        objectQueryWrapper.eq(IosBip::getBip, ip);
        objectQueryWrapper.eq(IosBip::getProduct, product);
        int cnum = count(objectQueryWrapper);
        return cnum;
    }
}
