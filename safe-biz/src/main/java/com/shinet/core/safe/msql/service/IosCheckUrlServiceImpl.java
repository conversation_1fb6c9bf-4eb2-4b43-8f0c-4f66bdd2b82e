package com.shinet.core.safe.msql.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.safe.core.constants.GlobalRedisConstants;
import com.shinet.core.safe.msql.entity.IosCheckUrl;
import com.shinet.core.safe.msql.mapper.IosCheckUrlMapper;
import com.shinet.core.safe.util.RedisUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.time.Duration;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【ios_check_url】的数据库操作Service实现
 * @createDate 2025-05-29 09:44:50
 */
@Slf4j
@Service
public class IosCheckUrlServiceImpl extends ServiceImpl<IosCheckUrlMapper, IosCheckUrl> {

    @Resource(name = "stringRedisTemplate2")
    private StringRedisTemplate redisTemplate;

    @Resource
    private RedisUtils redisUtils;

    public List<IosCheckUrl> queryUpProductList() {
        QueryWrapper<IosCheckUrl> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .select(IosCheckUrl::getProduct, IosCheckUrl::getUrl, IosCheckUrl::getAppStoreUrl)
                .eq(IosCheckUrl::getUpStatus, 1);
        return this.list(wrapper);
    }

    public Integer getProductUpDays(String product) {
        String count = redisTemplate.opsForValue().get(GlobalRedisConstants.getIosProductUpDays(product));
        return StringUtils.isBlank(count) ? 0 : Integer.parseInt(count);
    }

//    @PostConstruct
//    @Scheduled(cron = "0 4/10 * * * *")
    public void refreshIosProductUpDays() {

        List<IosCheckUrl> iosCheckUrls = queryUpProductList();

        updateProductUpDays(iosCheckUrls);
    }

    private void updateProductUpDays(List<IosCheckUrl> iosCheckUrlList) {
        if (CollUtil.isEmpty(iosCheckUrlList)) return;

        for (IosCheckUrl iosCheckUrl : iosCheckUrlList) {
            try {
                if (StringUtils.isBlank(iosCheckUrl.getAppStoreUrl())) continue;

                boolean b = redisUtils.tryLock("lock:up:days:" + iosCheckUrl.getProduct(), "true", String.valueOf(1000 * 60 * 2));

                if (!b) continue;

                boolean res = checkOne(iosCheckUrl);

                if (res) {
                    String s = redisTemplate.opsForValue().get(GlobalRedisConstants.getIosProductUpDaysCanIncrCheck(iosCheckUrl.getProduct()));

                    if (StringUtils.isNotBlank(s)) continue;

                    redisTemplate.opsForValue().increment(GlobalRedisConstants.getIosProductUpDays(iosCheckUrl.getProduct()));

                    redisTemplate.expire(GlobalRedisConstants.getIosProductUpDays(iosCheckUrl.getProduct()), Duration.ofDays(7));

                    redisTemplate.opsForValue().set(GlobalRedisConstants.getIosProductUpDaysCanIncrCheck(iosCheckUrl.getProduct()), String.valueOf(System.currentTimeMillis()));
                    redisTemplate.expire(GlobalRedisConstants.getIosProductUpDaysCanIncrCheck(iosCheckUrl.getProduct()), Duration.ofDays(1));
                    continue;
                }

                redisTemplate.delete(GlobalRedisConstants.getIosProductUpDays(iosCheckUrl.getProduct()));
                redisTemplate.delete(GlobalRedisConstants.getIosProductUpDaysCanIncrCheck(iosCheckUrl.getProduct()));
            } catch (Exception e) {
                log.error("updateProductUpDays error, product: {}", JSONObject.toJSONString(iosCheckUrl), e);
            }
        }

    }

    private boolean checkOne(IosCheckUrl iosCheckUrl) {
        Res res = requestAppStore(iosCheckUrl.getAppStoreUrl());

        if (res == null || res.getFlag() == null || !res.getFlag()) {
            return false;
        }

        return true;
    }

    @Data
    public static class Res {
        private Boolean flag;
        private Integer no;
        private Double score;
    }

    private static Res requestAppStore(String url) {
        Res res = new Res();
        res.setFlag(true);
        CloseableHttpResponse newResponse = null;
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = null;
        try {
            httpClient = HttpClients.createDefault();
            HttpGet httpGet = new HttpGet(url);
            response = httpClient.execute(httpGet);
            int statusCode = response.getStatusLine().getStatusCode();

            if (statusCode == 200) {
                res.setFlag(true);
            } else if (statusCode == 301) {
                // 获取新的地址
                String newUrl = response.getFirstHeader("Location").getValue();
                // 使用新的地址重新发起请求
                HttpGet newHttpGet = new HttpGet(newUrl);
                newResponse = httpClient.execute(newHttpGet);
                // 处理新请求的响应...
                statusCode = newResponse.getStatusLine().getStatusCode();

                if (statusCode == 200) {
                    res.setFlag(true);
                } else if (statusCode == 404) {
                    // 处理正常的响应...
                    log.info("url {} 404 已被下架", url);
                    Thread.sleep(1 * 1000);
                    newResponse = httpClient.execute(newHttpGet);
                    // 处理新请求的响应...
                    statusCode = newResponse.getStatusLine().getStatusCode();
                    if (statusCode == 404) {
                        log.info("url {} 404*2 已被下架", url);
                        res.setFlag(false);
                    }
                }
            } else if (statusCode == 404) {
                // 处理正常的响应...
                log.info("url {} 404 已被下架", url);
                Thread.sleep(1 * 1000);
                response = httpClient.execute(httpGet);
                statusCode = response.getStatusLine().getStatusCode();
                if (statusCode == 404) {
                    log.info("url {} 404*2 已被下架", url);
                    res.setFlag(false);
                }
            }

        } catch (Exception e) {
            log.error("Request Er:", e);
        } finally {
            if (newResponse != null) {
                try {
                    newResponse.close();
                } catch (IOException e) {
                    log.error(" newResponse.close ", e);
                }
            }
            try {
                response.close();
            } catch (IOException e) {
                log.error(" response.close ", e);
            }
            try {
                httpClient.close();
            } catch (IOException e) {
                log.error(" httpClient.close ", e);
            }
        }
        return res;
    }

}




