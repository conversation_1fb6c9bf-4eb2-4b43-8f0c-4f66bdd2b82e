package com.shinet.core.safe.msql.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.safe.enums.IosProductStatus;
import com.shinet.core.safe.hsq.rsp.GdIpRsp;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.entity.IosProjectSwitch;
import com.shinet.core.safe.msql.mapper.IosProjectSwitchMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.directory.api.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-06-07
*/
@Service
@Slf4j
public class IosProjectSwitchService extends ServiceImpl<IosProjectSwitchMapper, IosProjectSwitch> {

    public IosProjectSwitch getByPname(String product){
        LambdaQueryWrapper<IosProjectSwitch> objectQueryWrapper = new QueryWrapper<IosProjectSwitch>().lambda();
        objectQueryWrapper.eq(IosProjectSwitch::getProduct, product);
        List<IosProjectSwitch> projectSwitchList = list(objectQueryWrapper);
        if(projectSwitchList.size()>0){
            return projectSwitchList.get(0);
        }
        return null;
    }
    @Autowired
    LockAreaConfigService lockAreaConfigService;

    public boolean isLock(CommonHeaderDTO commonHeaderDTO,String ip){
        String trans = UUID.randomUUID().toString();
        log.info(" ios锁区判断 "+commonHeaderDTO.getIp()+"@"+trans);
        String product = commonHeaderDTO.getProduct();
        //LockAreaConfigService
        IosProjectSwitch projectSwitch = getByPname(product);
        Integer appId = Integer.parseInt(commonHeaderDTO.getAppId());
        boolean isLocked = true;
        if(projectSwitch==null){
            log.info("ios锁区判断2 "+trans+" 全锁 无配置");
            return isLocked;
        }
        IosProductStatus iosProductStatus  = IosProductStatus.getType(projectSwitch.getFlagStatus());
        if(IosProductStatus.SHENHE.equals(iosProductStatus)){
            //审核面 全锁
            log.info("ios锁区判断3 "+trans+" 审核面 全锁");
        }else if (IosProductStatus.LOCKOPEN.equals(iosProductStatus)){
            //半开 对审核IP 海外锁定  ocpc用户开放
            //全开 对审核IP 以及海外IP 锁定
            String dsp = lockAreaConfigService.userIsOCPC(commonHeaderDTO, appId, product);
            if (!"nodsp".equals(dsp)) {
                //ocpc用户 不进入锁区
                isLocked = false;
            }else{
                //ip
                boolean isValIp = isValidIpCity(commonHeaderDTO,trans);
                if(isValIp){
                    isLocked =  true;
                    log.info("ios 锁区生效 "+ip+"  "+commonHeaderDTO.getIp());
                }
            }
        }
        return isLocked;
    }
    @Autowired
    IpService ipService;

    public boolean isValidIpCity(CommonHeaderDTO commonHeaderDTO,String trans){
        // 区域Check
        boolean isLockCty = false;
        try {
            String saveKey = ipService.getSaveKey(commonHeaderDTO);
            GdIpRsp gdIpRsp = ipService.getFormHBase(saveKey);
            if (gdIpRsp == null) {
                gdIpRsp = ipService.getIpLocation(commonHeaderDTO.getIp());
            }
            // 缓存
            if (StringUtils.isEmpty(gdIpRsp.getCity()) || gdIpRsp.getCity().contains("[")){
                // 使用ipPlus
                String city = ipService.getCityByIpPlus(commonHeaderDTO.getIp(),commonHeaderDTO.getGps());
                if (Strings.isNotEmpty(city)) {
                    gdIpRsp.setCity(city);
                    log.info("[{}] Get By GD Failed Succeed Use IpPlus Get city: {}",trans,city);
                    ipService.saveHBase(gdIpRsp,saveKey);
                }else {
                    log.info("[{}] Get By GD Failed IpPlus Also Failed",trans);
                }
            }else {
                ipService.saveHBase(gdIpRsp,saveKey);
            }
            // 成功则判断
            if (StringUtils.isNotBlank(gdIpRsp.getCity())){
                log.info("[{}] ReqIp: {} , User {} App {}, City {}",trans,commonHeaderDTO.getIp(),commonHeaderDTO.getUserId(),commonHeaderDTO.getAppId(),gdIpRsp.getCity());
                Set<String> checkCity = lockAreaConfigService.iosBlackCitySet;

                if ("ios".equalsIgnoreCase(commonHeaderDTO.getOs())){
                    checkCity.add("海外");
                }
                for (String city : checkCity){
                    if (city.contains(gdIpRsp.getCity()) || gdIpRsp.getCity().contains(city)) {
                        isLockCty = true;
                        break;
                    }
                }
            }else {
                log.warn("[{}] GET IP FAILED,Rs:{}",trans, JSON.toJSONString(gdIpRsp));
                if ("ios".equalsIgnoreCase(commonHeaderDTO.getOs())){
                    isLockCty = true;
                }
            }
        }catch (Exception e){
            log.info("获取Ip失败..",e);
            // IOS若判定的确异常 直接认为是锁区
            if ("ios".equalsIgnoreCase(commonHeaderDTO.getOs())){
                isLockCty = true;
            }
        }
        return  isLockCty;
    }
}
