package com.shinet.core.safe.msql.service;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shinet.core.safe.hsq.rsp.BdIpRsp;
import com.shinet.core.safe.hsq.rsp.GdIpRsp;
import com.shinet.core.safe.hsq.rsp.GdLocationRsp;
import com.shinet.core.safe.hsq.rsp.IpPlusRsp;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.util.DateUtils;
import com.shinet.core.safe.util.HBaseUtils;
import com.shinet.core.safe.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.directory.api.util.Strings;
import org.apache.hadoop.hbase.client.Connection;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class IpService {

    private static final Map<String,String> gdKeyMap = new LinkedHashMap<String,String>(){{
        put("GD_GS","7b14431ed16aa82f47127cfb143bcd10"); // 公司企业开发者
        put("GD_KL","0e82aef25c5b20c51eb07b9da6e768ca"); // 康乐
        put("GD_JT","5381406ce92a926239431c6ddd7ff20f"); // 继弢
        put("GD_BR","0f7184d5e89de28944ba5fc6f92473b7"); // 老毕
        put("GD_JF","f3083e8e68e378f5166e4acee587566d"); // 建峰
        put("GD_SS","2652618bfa90efc386455817c946bb55"); // 神锁
        put("GD_GQ","98974dfdb9182029af3d292673a0c244"); // 耕樵
        put("GD_CY","1a5a2ec6604959d8269147008658a29c"); // cy
    }};

    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate redisTemplate;

    @Value("${ip.use.baidu:false}")
    private Boolean useBaidu;
    @Resource
    private Connection hbaseConnection;

    private static final String usedKeyList = "used:key:list";

    private static final String TABLE_NAME = "user_lock_area";

    //    private static final String baiduMapUrl = "http://api.map.baidu.com/reverse_geocoding/v3/?ak=5PqXnkNcLoGbPCIKINDl3EYunhgSz5xf&output=json&location=";
    private static final String baiduMapUrl = "https://api.map.baidu.com/location/ip?ak=5PqXnkNcLoGbPCIKINDl3EYunhgSz5xf&ip=%s&coor=bd09ll?ak=5PqXnkNcLoGbPCIKINDl3EYunhgSz5xf&output=json&location=";

    public GdIpRsp getFormHBase(String device){
        GdIpRsp result = null;
        try {
            String queryKey = buildCacheKeyDevice(device);
            byte[] results = HBaseUtils.searchDataFromHadoop(hbaseConnection,TABLE_NAME,queryKey);
            result = results == null ? null : JSONObject.parseObject(results, GdIpRsp.class);
        }catch (Exception e){
            log.info("查询HBase异常..",e);
        }
        return result;
    }

    public String getSaveKey(CommonHeaderDTO commonHeaderDTO){
        return commonHeaderDTO.getIp();
//        String device = commonHeaderDTO.getDeviceId();
//        if ( "0".equals(device)
//                || "0000-0000-0000-0000".equals(device)
//                || "00000000-0000-0000-0000-000000000000".equals(commonHeaderDTO.getOaid())
//                || (StringUtils.isNotEmpty(device) && (device.startsWith("1111")|| device.startsWith("0000")))
//        ){
//            return commonHeaderDTO.getIp();
//        }
//        if (Strings.isEmpty(device)){
//            if (Strings.isNotEmpty(commonHeaderDTO.getAndroidId())){
//                device = commonHeaderDTO.getAndroidId();
//            }else if (Strings.isNotEmpty(commonHeaderDTO.getOaid())){
//                device = commonHeaderDTO.getOaid();
//            }else if (Strings.isNotEmpty(commonHeaderDTO.getAccessKey())){
//                device = String.valueOf(commonHeaderDTO.getUserId());
//            }else {
//                device = commonHeaderDTO.getIp();
//            }
//        }
//        return device;
    }

    public void saveHBase(GdIpRsp gdIpRsp,String device){
        String queryKey = buildCacheKeyDevice(device);
        HBaseUtils.saveToHadoop(hbaseConnection,TABLE_NAME,queryKey, JSON.toJSONBytes(gdIpRsp));
    }

    private String buildCacheKeyDevice(String device){
        return String.format("save_key:%s",device);
    }

    public static GdIpRsp getByGps(String gps) {
        try {
            String raw = HttpUtil.get(String.format(baiduMapUrl,gps));
//            System.out.println(raw);
            BdIpRsp bdIpRsp = JSONObject.parseObject(raw,BdIpRsp.class);
            return bdIpRsp.convert();
        } catch (Exception e) {
            log.error("百度逆地理编码失败, gps: {}", gps, e);
            return null;
        }
    }

    private static final String gdIpUrl = "https://restapi.amap.com/v3/ip?key=%s&ip=";
    private static final String gdLocationUrl = "https://restapi.amap.com/v3/geocode/regeo?key=7b14431ed16aa82f47127cfb143bcd10&location=%s";

    public GdIpRsp getIpLocation(String ipStr){
        if (useBaidu){
            return getByGps(ipStr);
        }
        String key = choseKeyV1();
        String result1= HttpUtil.get(String.format(gdIpUrl,gdKeyMap.get(key))+ipStr, 3000);
        GdIpRsp gdIpRsp = JSON.parseObject(result1,GdIpRsp.class);
        String cacheKey = buildCacheKey(key);
        redisTemplate.opsForValue().increment(cacheKey);
        if ("10003".equals(gdIpRsp.getInfocode()) || "10044".equals(gdIpRsp.getInfocode())){
            log.error("Key {} 已经达到额度",key);
            setUsedKeyList(key);
        }
        redisTemplate.expire(cacheKey,2, TimeUnit.DAYS);
        return gdIpRsp;
    }

    private static String buildCacheKey(String key){
        String today = DateUtils.formatDateForYMDSTR(new Date());
        return String.format("api:ip:get:%s:%s",today,key);
    }

    private String choseKey(){
        List<Object> results = getAllKeysCount();

        int i = 0;
        for (String key : gdKeyMap.keySet()){
            Object target = results.get(i);
            if (Objects.nonNull(target)) {
                if ( Integer.parseInt(String.valueOf(target)) < 300000) {
                    return key;
                }
                i++;
            }else {
                return key;
            }
        }
        return "GD_GS";
    }

    private void setUsedKeyList(String usedKey){
        String keyUsed = usedKeyList + ":" + DateUtils.formatDateForYMDSTR(new Date());
        String result = redisTemplate.opsForValue().get(keyUsed);
        List<String> usedArrayList = new ArrayList<>();
        if (Strings.isNotEmpty(result)){
            usedArrayList  = JSON.parseArray(result,String.class);
        }
        usedArrayList.add(usedKey);
        Set<String> usedSet = new HashSet<>(usedArrayList);
        redisTemplate.opsForValue().set(keyUsed,JSON.toJSONString(usedSet));
    }


    private String choseKeyV1(){
        List<String> usedKeyList = getAlreadyUsedKeys();
        for (String key : gdKeyMap.keySet()){
            if (!usedKeyList.contains(key)) {
                return key;
            }
        }
        return "GD_GS";
    }

    private List<String> getAlreadyUsedKeys(){
        String key = usedKeyList + ":" + DateUtils.formatDateForYMDSTR(new Date());
        String result = redisTemplate.opsForValue().get(key);
        if (Strings.isNotEmpty(result)){
            return JSON.parseArray(result,String.class);
        }
        return new ArrayList<>();
    }

    private List<Object> getAllKeysCount(){
        return  redisTemplate.executePipelined((RedisCallback<String>) redisConnection -> {
            StringRedisConnection stringRedisConnection = (StringRedisConnection) redisConnection;
            for (String key : gdKeyMap.keySet()) {
                String cacheKey = buildCacheKey(key);
                stringRedisConnection.get(cacheKey);
            }
            return null;
        });
    }

    private static final String dingTalkPush = "https://oapi.dingtalk.com/robot/send?access_token=2a63d7d1fd116a8f08037ac6540831997fade1e1c018f306cc930c4300633a28";

    @Autowired
    private RedisUtils redisUtils;

    // 统计高德账户消耗
    @Scheduled(cron = "0 0 * * * ?")
    public void checkGDCount(){
        redisUtils.processWithoutWait("check_gd_count_task",(success) -> {
            List<String> results = getAlreadyUsedKeys();
            if (results.size() > 4) {
                JSONObject jsonObject = new JSONObject();
                JSONObject content = new JSONObject();
                jsonObject.put("msgtype", "text");
                content.put("content",
                        "[Push] 高德API余额：\n\n" + String.join("", results)
                );
                jsonObject.put("text", content);

                // 发送钉钉通知
                HttpUtil.post(dingTalkPush, jsonObject.toJSONString());
                try {
                    TimeUnit.SECONDS.sleep(10L);
                } catch (InterruptedException e) {
                    log.error("Sleep Er:", e);
                }
            }
            return null;
        },(failed) -> {
            log.info("Get LockFailed.. JUMP");
            return null;
        });

    }

    public String getCityByIpCc(String ip){
        try {
            String url = String.format("http://www.cip.cc/%s", ip);
            // 200 MS 超时
            String result = HttpUtil.get(url,500);
            Document doc = Jsoup.parseBodyFragment(result);
            Element body = doc.body();
            Elements elements = body.getElementsByClass("data kq-well");
            if (elements.size() > 0) {
                Element element = elements.get(0);
                String desc = element.text();
                return desc.split(":")[4].split("[|]")[0].trim();
            }
        }catch (Exception e){
            log.warn("Query IpCC Ex:{}",e.getMessage());
        }
        return null;
    }

    /**
     * 使用地理位置获取城市
     * @param location 经纬度
     * @return 城市
     */
    public static GdLocationRsp.AddressComponent getCityByLocation(String location){
        try {
            String result = HttpUtil.get(String.format(gdLocationUrl,location), 3000);
            GdLocationRsp gdLocationRsp = JSON.parseObject(result,GdLocationRsp.class);
            if ("10000".equals(gdLocationRsp.getInfocode())){
                if (gdLocationRsp.getRegeocode() != null) {
                    if (gdLocationRsp.getRegeocode().getAddressComponent() != null) {
                        return gdLocationRsp.getRegeocode().getAddressComponent();
                    }
                }
            }
        }catch (Exception e){
            log.warn("QueryBy Gd Location:",e);
        }
        return null;
    }

    public String getCityByIpPlus(String ip,String gps){
        try {
            String key = "NIoDVoN2d2szHaoftxpHgX145aAwbwg1UOqT1YBOeKf54fbgc2SInjwcVgDagZnU";
            String url = String.format("https://api.ipplus360.com/ip/geo/v1/city/?key=%s&ip=%s&coordsys=WGS84&area=multi",key, ip);
            // 200 MS 超时
            String result = HttpUtil.get(url,500);
            IpPlusRsp ipPlusRsp = JSONObject.parseObject(result, IpPlusRsp.class);
            if ("Success".equals(ipPlusRsp.getCode())){
                if (StringUtils.isEmpty(ipPlusRsp.getData().getCity())){
                    log.info("==> City Null:{}",result);
                }
                if (!ipPlusRsp.getData().getCountry().contains("中国")){
                    log.info("Get city ==> {}",result);
                    //isp
                    if(StringUtils.contains(ipPlusRsp.getData().getIsp(),"苹果")){
                        return "海外-苹果";
                    }
                    return "海外";
                }
                String city = ipPlusRsp.getData().getCity();
//                if (StringUtils.isEmpty(city)){
//                    String location = ipPlusRsp.getData().getLng()+","+ipPlusRsp.getData().getLat();
//                    if (location.contains("null")){
//                        location = gps;
//                    }
//                    GdLocationRsp.AddressComponent addressComponent = getCityByLocation(location);
//                    if (addressComponent!=null) {
//                        city = addressComponent.getCity();
//                    }
//                    log.info("IpPlus Failed,Use Gd Location Fx {}",city);
//                }
                return city;
            }
        }catch (Exception e){
            log.warn("Query IpCC Ex:{}",e.getMessage());
        }
        return null;
    }
}
