package com.shinet.core.safe.msql.service;

import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.shinet.core.safe.common.HttpClientService;
import com.shinet.core.safe.dto.LockKeyResult;
import com.shinet.core.safe.hsq.rsp.GdIpRsp;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.service.ioslock.IosLockService;
import com.shinet.core.safe.msql.service.ioslock.LockIosConfigService;
import com.shinet.core.safe.util.HBaseUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.directory.api.util.Strings;
import org.apache.hadoop.hbase.client.Connection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @since 2022/1/8
 */
@Slf4j
@Service
public class LockAreaConfigMinService {

    @Autowired
    private IpService ipService;

    @ApolloJsonValue("${lock.area.min.black.city:[\"北京\"]}")
    public Set<String> minBlackCitySet;

    @ApolloJsonValue("${lock.area.min.white.user:[]}")
    private List<String> minWhiteUserSet;



    private Map<String,Set<String>> blackCityMap;

    private Map<String,Set<String>> whiteUserMap;

    private List<Integer> whiteDeviceListApp;


    @Resource
    private Connection hbaseConnection;

    @Autowired
    private LockedAreaRecordMinService lockedAreaRecordMinService;

    private static final String TABLE_NAME = "user_lock_area";
    private static final String TABLE_LOCKED = "user_locked";

    @PostConstruct
    public void initTable() throws IOException {
        HBaseUtils.initHadoopTable(hbaseConnection,TABLE_NAME,30 * 24 * 60 * 60);
        HBaseUtils.initHadoopTable(hbaseConnection,TABLE_LOCKED,90 * 24 * 60 * 60);
    }
    @Autowired
    IosLockService iosLockService;
    @Autowired
    LockIosConfigService lockIosConfigService;
    public LockKeyResult pushLockKey(CommonHeaderDTO commonHeaderDTO,Integer appId, String pkgNames,String trans){

        boolean ocpcFlag = false, localFlag = false;

        LockKeyResult lockKeyResult = new LockKeyResult();
        lockKeyResult.setLocked(Boolean.FALSE);
        lockKeyResult.setOcpc(Boolean.FALSE);
        if (appId == null){
            return lockKeyResult.build();
        }
        Long userId = CommonHeaderDTO.getUserIdNoEx(commonHeaderDTO);


        if (minWhiteUserSet != null && minWhiteUserSet.contains(String.valueOf(userId))) {
            lockKeyResult.setLocked(false);
            return lockKeyResult.build();
        }

        // OCPC用户识别
        String product = commonHeaderDTO.getProduct();
        String dsp = userIsMinOCPC(commonHeaderDTO, appId, product);
        ocpcFlag = !"nodsp".equals(dsp);
        lockKeyResult.setOcpc(ocpcFlag);

        String city = getCity(commonHeaderDTO, trans);
        lockKeyResult.setCity(city);
        //非ocpc 直接 true
        if (!ocpcFlag) {
            lockKeyResult.setLocked(true);
            return lockKeyResult.build();
        }

        if (StringUtils.isEmpty(city) || (StringUtils.isNotBlank(city) && minBlackCitySet.stream().anyMatch(r -> city.contains(r)))) {
            log.info("{} {} {} 城市命中 按照非OCPC处理", trans, commonHeaderDTO.getOpenId(), city);
            lockKeyResult.setLocked(true);
            return lockKeyResult.build();
        }

        lockKeyResult.setLocked(localFlag);

        return lockKeyResult.build();
    }


    private String getCity(CommonHeaderDTO commonHeaderDTO,String trans){
        String defalutQueryCity = "海外";

        try {

            String saveKey = ipService.getSaveKey(commonHeaderDTO);
            GdIpRsp gdIpRsp = ipService.getFormHBase(saveKey);
            if (gdIpRsp == null) {
                gdIpRsp = ipService.getIpLocation(commonHeaderDTO.getIp());
            }
            // 缓存
            if (StringUtils.isEmpty(gdIpRsp.getCity()) || gdIpRsp.getCity().contains("[")){
                // 使用ipPlus
                String city = ipService.getCityByIpPlus(commonHeaderDTO.getIp(),commonHeaderDTO.getGps());
                if (Strings.isNotEmpty(city)) {
                    gdIpRsp.setCity(city);
                    log.info("[{}] Get By GD Failed Succeed Use IpPlus Get city: {}",trans,city);
                    return city;
                }else {
                    log.info("[{}] Get By GD Failed IpPlus Also Failed",trans);
                }
            }else {
                return  gdIpRsp.getCity();
            }
            // 成功则判断
            if (StringUtils.isBlank(gdIpRsp.getCity())){
                log.warn("[{}] GET IP FAILED,Rs:{}",trans,JSON.toJSONString(gdIpRsp));
                if ("ios".equalsIgnoreCase(commonHeaderDTO.getOs())){
                    return defalutQueryCity;
                }
            }
            return defalutQueryCity;
        }catch (Exception e){
            log.info("获取Ip失败..",e);
            // IOS若判定的确异常 直接认为是锁区
            return defalutQueryCity;
        }
    }

    public static String userIsOCPC(CommonHeaderDTO commonHeaderDTO,Integer appId,String product){
        try {
            Map<String,Object> param = new HashMap<>();
            param.put("product",product);
            param.put("pkgChannel",commonHeaderDTO.getChannel());
            param.put("os",commonHeaderDTO.getOs().toLowerCase());
            param.put("ocpcDeviceId", MD5.create().digestHex(commonHeaderDTO.getDeviceId()));
            param.put("SourceDeviceId", commonHeaderDTO.getDeviceId());
            param.put("oaid",commonHeaderDTO.getOaid());
            param.put("androidId",commonHeaderDTO.getAndroidId());
            param.put("appId",appId);
            param.put("mac", commonHeaderDTO.getMac());
            param.put("model", commonHeaderDTO.getModel());
            param.put("ip", commonHeaderDTO.getIp());
            param.put("caid", commonHeaderDTO.getCaid());
            param.put("ua", commonHeaderDTO.getUa());
            param.put("openId", commonHeaderDTO.getOpenId());

            String result = HttpUtil.get("http://ocpc-api.shinet-inc.com/dispense/user/event/guiDsp",param);

            if (StringUtils.isNotBlank(result)){
                JSONObject jsonObject = JSONObject.parseObject(result);
                String dsp = jsonObject.getString("data");
                // 2022-12-26 非nosDsp 均为买量
                return dsp;
            }
        }catch (Exception e){
            log.error("Query Er:",e);
        }
        return "";
    }

    public static String userIsMinOCPC(CommonHeaderDTO commonHeaderDTO,Integer appId,String product){
        try {
            Map<String,Object> param = new HashMap<>();
            param.put("product",product);
            param.put("appId",appId);
            param.put("minOs",commonHeaderDTO.getOs().toLowerCase());
            param.put("userId",commonHeaderDTO.getUserId());
            param.put("openId", commonHeaderDTO.getOpenId());

            String result = HttpUtil.get("http://ocpc-api.shinet-inc.com/dispense/user/event/minDsp",param);

            if (StringUtils.isNotBlank(result)){
                JSONObject jsonObject = JSONObject.parseObject(result);
                String dsp = jsonObject.getString("data");
                // 2022-12-26 非nosDsp 均为买量
                return dsp;
            }
        }catch (Exception e){
            log.error("Query Er:",e);
        }
        return "";
    }


    @Autowired
    private HttpClientService httpClientService;


    private String buildLockKey(Integer appId,Long userId){
        return String.format("%s:%s:%s",appId,userId, UUID.randomUUID().toString().replaceAll("-",""));
    }

    private String buildKey(Integer appId,Integer os){
        return String.format("%s:%s",appId,os);
    }



    @Data
    private static class ConfigRemote{
        private Integer id;
        private Integer appId;
        private Integer os;
        private String product;
        private String channels;
        private String cities;
        private String pkgs;
        private String whitelist;
        private Integer whiteType;
        private String blockAllList;
    }

    private List<ConfigRemote> getRemoteConfig(){
        String result = HttpUtil.get("http://bp-api.shinet-inc.com/use-event/area/getConfig");
        if (Strings.isNotEmpty(result)){
            return JSON.parseArray(JSON.parseObject(result).getString("data"),ConfigRemote.class);
        }
        return new ArrayList<>();
    }


    public void refreshLockKeyResult(Integer appId,CommonHeaderDTO commonHeaderDTO ,LockKeyResult lockKeyResult){
        CompletableFuture.runAsync(()->{
            try {
                lockedAreaRecordMinService.saveIfNotExist(appId,commonHeaderDTO,lockKeyResult);
            }catch (Exception e){
                log.error("Save LockEx:",e);
            }
        });
    }
}
