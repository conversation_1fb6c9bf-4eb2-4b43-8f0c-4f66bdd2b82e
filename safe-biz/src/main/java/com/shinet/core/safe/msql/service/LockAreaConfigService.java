package com.shinet.core.safe.msql.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.coohua.bp.account.remote.api.AccountRPC;
import com.coohua.bp.data.retrieve.remote.api.UserChannelRPC;
import com.coohua.bp.data.retrieve.remote.dto.UserActiveRequest;
import com.coohua.bp.data.retrieve.remote.dto.UserActiveResponse;
import com.coohua.bp.user.remote.api.UserRPC;
import com.coohua.user.event.api.dto.UserActiveResp;
import com.coohua.user.event.api.remote.rpc.LockConfigRpc;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Sets;
import com.shinet.core.safe.common.HttpClientService;
import com.shinet.core.safe.dto.LockKeyResult;
import com.shinet.core.safe.enums.ActiveChannel;
import com.shinet.core.safe.hsq.rsp.GdIpRsp;
import com.shinet.core.safe.lock.service.LockHbaseService;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.entity.LockIosConfig;
import com.shinet.core.safe.msql.service.ioslock.IosLockService;
import com.shinet.core.safe.msql.service.ioslock.LockIosConfigService;
import com.shinet.core.safe.util.HBaseUtils;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.directory.api.util.Strings;
import org.apache.hadoop.hbase.client.Connection;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.shinet.core.safe.constant.BaseConstants.COMMA;

/**
 * <AUTHOR>
 * @since 2022/1/8
 */
@Slf4j
@Service
public class LockAreaConfigService {

    @Autowired
    private IpService ipService;

    private Map<String,String> lockNeedCheckOCPC = new ConcurrentHashMap<>();

    private Map<String,List<String>> joinChannelStartMap = new ConcurrentHashMap<>();

    @MotanReferer(basicReferer = "bp-data-retrieve")
    private UserChannelRPC userChannelRPC;

    @MotanReferer(basicReferer = "bp-user")
    private UserRPC userRPC;

    @MotanReferer(basicReferer = "bp-account")
    private AccountRPC accountRPC;

    @MotanReferer(basicReferer = "user-event")
    private LockConfigRpc lockConfigRpc;

    public List<String> getJoinChannelStartList(Integer appId,CommonHeaderDTO commonHeaderDTO){
        String queryKey = buildKey(appId,commonHeaderDTO.getIntOs()+1);
        return joinChannelStartMap.getOrDefault(queryKey,new ArrayList<>());
    }

    @ApolloJsonValue("${lock.area.black.city:[\"北京\",\"上海\",\"广州\",\"深圳\",\"佛山\",\"杭州\",\"南京\",\"成都\"]}")
    private Set<String> blackCitySet;

    @ApolloJsonValue("${lock.area.ios.black.city:[\"北京\"]}")
    public Set<String> iosBlackCitySet;

    @ApolloJsonValue("${query.register.channel.app:[642]}")
    private List<Integer> queryRegisterChannelApp;

    private Map<String,Set<String>> blackCityMap = new ConcurrentHashMap<>();

    private Map<String,Set<String>> whiteUserMap = new ConcurrentHashMap<>();

    private Map<String,Set<String>> lockAllChannelMap = new ConcurrentHashMap<>();

    private Map<String,Set<String>> lockPkgMap = new ConcurrentHashMap<>();

    private List<Integer> whiteDeviceListApp = new ArrayList<>();

    @ApolloJsonValue("${no.ocpc.join.app.list:[696,668]}")
    private List<Integer> noOcpcToJoinLockAreaAppList;

    @ApolloJsonValue("${gdt.no.ocpc.join.app.list:[668]}")
    private List<Integer> gdtNoOcpcToJoinLockAreaAppList;

    @ApolloJsonValue("${lock.no.cache.app:[810]}")
    private List<Integer> noCacheAppList;

    @ApolloJsonValue("${lock.ip.gray.list:[\"**************\",\"**************\"]}")
    private List<String> ipGrayList;

    @ApolloJsonValue("${lock.ip.white.map:{\"742:2\":[\"************\"]}}")
    private Map<String,List<String>> ipWhiteMap;

    @ApolloJsonValue("${lock.device.gray.list:[\"9ae546c3e4469d56\",\"3f1c8b848daa36b4\",\" 865407010000009\"]}")
    private List<String> deviceGrayList;

    @ApolloJsonValue("${lock.xian.config.map:{\"558\":[\"yydxny2vivo\"],\"579\":[\"swtyvivo\"]}}")
    private Map<String,Set<String>> addXianMapApps;

    @ApolloJsonValue("${skip.no.device.app:[754,775]}")
    private List<Integer> noDeviceSkipAppList;

    @ApolloJsonValue("${go.to.locked:[\"810_vivo_116\"]}")
    private List<String> lockKeyCrList;

    @ApolloJsonValue("${pass.lock.ios.app:[900,910,947]}")
    private List<Integer> passedIosList;

    @ApolloJsonValue("${single.lock.ios.app:[937,947]}")
    private List<Integer> singleBjAndLf;

    @ApolloJsonValue("${hwm.lock.ntf.app:[948]}")
    private List<Integer> hwxmAlLockNtfAppList;

    @ApolloJsonValue("${hw.ex.channel.pkg:[]}")
    private List<String> hwExChannelPkg;

    @ApolloJsonValue("${sprcial.channel.lock.city:{}}")
    private Map<String,Set<String>> specialChannelLockCity;


    @Resource
    private Connection hbaseConnection;

    @Autowired
    private LockHbaseService lockHbaseService;

    private static final String TABLE_NAME = "user_lock_area";
    private static final String TABLE_LOCKED = "user_locked";

    @PostConstruct
    public void initTable() throws IOException {
        HBaseUtils.initHadoopTable(hbaseConnection,TABLE_NAME,30 * 24 * 60 * 60);
        HBaseUtils.initHadoopTable(hbaseConnection,TABLE_LOCKED,90 * 24 * 60 * 60);
    }
    @Autowired
    IosLockService iosLockService;
    @Autowired
    LockIosConfigService lockIosConfigService;
    public LockKeyResult pushLockKey(CommonHeaderDTO commonHeaderDTO,Integer appId, String pkgNames,String trans, String wfName,String ivpn){

        if (appId == null){
            LockKeyResult lockKeyResult = new LockKeyResult();
            lockKeyResult.setLocked(Boolean.FALSE);
            lockKeyResult.setOcpc(Boolean.FALSE);

            return lockKeyResult.build();
        }

        String queryKey = buildKey(appId,commonHeaderDTO.getIntOs()+1);

        String product = lockNeedCheckOCPC.get(queryKey);
        if (StringUtils.isEmpty(product)){
            product = commonHeaderDTO.getProduct();
        }

        if(StringUtils.equalsIgnoreCase("ios",commonHeaderDTO.getOs().toLowerCase())){
            long ctime = System.currentTimeMillis();
            LockIosConfig lockIosConfig = null;
            log.info("iosnewvpn "+commonHeaderDTO.getProduct()+" "+commonHeaderDTO.getCaid()+" wfName = "+wfName+" ivpn="+ivpn+" pkgNames="+pkgNames);
            try{
                if(StringUtils.isBlank(commonHeaderDTO.getProduct())){
                    lockIosConfig = lockIosConfigService.getIosConfig(null,appId);
                    if(lockIosConfig!=null){
                        commonHeaderDTO.setProduct(lockIosConfig.getProduct());
                        log.info("填补product "+appId+" "+lockIosConfig.getProduct());
                    }
                }


            }catch (Exception e){
                log.error("",e);
            }

            if(lockIosConfig==null){
                lockIosConfig = lockIosConfigService.getIosConfig(commonHeaderDTO.getProduct(),appId);
            }

            if(lockIosConfig!=null && lockIosConfig.getLinkId()!=null && lockIosConfig.getLinkId()>0){
                LockIosConfig lockIosConfig2 = lockIosConfigService.getById(lockIosConfig.getLinkId());
                if(lockIosConfig2!=null){
                    log.info("产品AB替换锁区 "+commonHeaderDTO.getProduct()+" "+commonHeaderDTO.getAppId()+" 替换成 "+lockIosConfig.getProduct()+" "+lockIosConfig.getAppId());
                    commonHeaderDTO.setAppId(lockIosConfig2.getAppId()+"");
                    commonHeaderDTO.setProduct(lockIosConfig2.getProduct());
                }else{
                    log.error("产品AB替换锁区错误 "+lockIosConfig.getLinkId());
                }
            }
            if(lockIosConfig!=null){
                log.info("ios进入白名单测试 "+commonHeaderDTO.getProduct()+" "+commonHeaderDTO.getCaid()+" "+commonHeaderDTO.getIp());
                LockKeyResult lockKeyResult = iosLockService.iosLockCheck(commonHeaderDTO,appId,trans);

                queryKey = buildKey(appId,commonHeaderDTO.getIntOs()+1);
                if (ipWhiteMap.getOrDefault(queryKey,new ArrayList<>()).contains(commonHeaderDTO.getIp())){
                    log.info("{} 白名单ip命中...",trans);
                    lockKeyResult.setLocked(false);
                }
                long dtime = System.currentTimeMillis()-ctime;
                if(dtime>2000){
                    log.info("lockKeyResultcostthan2 "+commonHeaderDTO.getProduct()+" "+commonHeaderDTO.getProduct()+" "+commonHeaderDTO.getUserId()+" "+commonHeaderDTO.getCaid()+" cost "+(dtime));
                }
                if(lockKeyResult.getLocked()){
                    log.info("lockKeyResult lock is "+commonHeaderDTO.getProduct()+" "+JSON.toJSONString(lockKeyResult)+" cost "+(dtime));
                }else{
                    log.info("lockKeyResult unlock is "+commonHeaderDTO.getProduct()+" "+JSON.toJSONString(lockKeyResult)+" cost "+(dtime));
                }
                return lockKeyResult;
            }else{
                log.info("ios缺少配置信息 "+commonHeaderDTO.getProduct()+" "+commonHeaderDTO.getAppId());
            }
        }

        LockKeyResult lockKeyResult = new LockKeyResult();
        lockKeyResult.setLocked(Boolean.FALSE);

        lockKeyResult.setOcpc(Boolean.FALSE);

        boolean pkgLock = false;
        if ("hw".equalsIgnoreCase(commonHeaderDTO.getChannel())||
                "mi".equalsIgnoreCase(commonHeaderDTO.getChannel()) ||
                "vivo".equalsIgnoreCase(commonHeaderDTO.getChannel()) ||
                commonHeaderDTO.getChannel().contains("xiaomi")||
                commonHeaderDTO.getChannel().contains("vivo")||
                commonHeaderDTO.getChannel().contains("huawei")) {
            try {
                for (String dkPkg : hwExChannelPkg){
                    if (pkgNames.contains(dkPkg)){
                        log.info("{} {} {} {} {} {} oaid:{} android_id:{} 华米Vivo审核 包名拉黑 直接锁区 命中包名 {}",trans,commonHeaderDTO.getDeviceId(),
                                appId,
                                commonHeaderDTO.getChannel(),commonHeaderDTO.getAppVersion(),commonHeaderDTO.getModel()
                                ,commonHeaderDTO.getOaid(),commonHeaderDTO.getAndroidId(),dkPkg);
                        pkgLock = true;
                        break;
                    }
                }
                if (!pkgLock) {
                    log.info("{} {} {} {} {} {}  oaid:{} android_id:{} 华米Vivo审核 包名未命中 记录日志", trans, commonHeaderDTO.getDeviceId()
                            ,appId,commonHeaderDTO.getChannel(),commonHeaderDTO.getAppVersion(),commonHeaderDTO.getModel()
                            ,commonHeaderDTO.getOaid(),commonHeaderDTO.getAndroidId());
                }
            }catch (Exception e){
                log.warn("Match DkPkg Ex:",e);
            }
        }
        String dsp = userIsOCPC(commonHeaderDTO, appId, product);
        try {
            if ("android".equalsIgnoreCase(commonHeaderDTO.getOs())){
                // Android 设备id 和 oaid都没有 归因基本失败
                if (StringUtils.isEmpty(commonHeaderDTO.getOaid()) && StringUtils.isEmpty(commonHeaderDTO.getDeviceId())){
                    if (StringUtils.isNotEmpty(commonHeaderDTO.getAccessKey())){
                        // 对设备无法归因的用户进行 user_id 归因
                        UserActiveRequest request = new UserActiveRequest();
                        request.setProduct(product);
                        request.setUserId(commonHeaderDTO.getUserId());
                        UserActiveResponse userActiveResponse = userChannelRPC.searchActiveChannel(request);
                        List<Integer> activeChannel = new ArrayList<>();
                        // 内部渠道视为非投放
                        activeChannel.add(ActiveChannel.NONE.getType());
                        activeChannel.add(ActiveChannel.ZR.getType());
                        activeChannel.add(ActiveChannel.INNER_DRAINAGE.getType());
                        activeChannel.add(ActiveChannel.INNER_OLD.getType());
                        if (!activeChannel.contains(userActiveResponse.getActiveType())){
                            // 只修正为投放渠道
                            dsp = userActiveResponse.getActiveChannel();
                            log.info("[{}] Query By Ak {} {} get dsp {}",trans,product,commonHeaderDTO.getUserId(),userActiveResponse.getActiveChannel());
                        }
                    }
                }
            }
        }catch (Exception e){
            log.warn("Query By RPC Failed:",e);
        }
        lockKeyResult.setOcpc(!"nodsp".equals(dsp));
        if (queryRegisterChannelApp.contains(appId)){
            Long userId = CommonHeaderDTO.getUserIdNoEx(commonHeaderDTO);
            if (userId != 0L){
                commonHeaderDTO.setChannel(getChannel(commonHeaderDTO));
            }
        }

        try {

            if (ipGrayList.contains(commonHeaderDTO.getIp()) || deviceGrayList.contains(commonHeaderDTO.getDeviceId())){
                log.info("{} {} {} {} 黑名单命中 {}",trans,appId,commonHeaderDTO.getChannel(),commonHeaderDTO.getDeviceId(),pkgNames);
                lockKeyResult.setLocked(true);
                return lockKeyResult.build();
            }
            if (ipWhiteMap.getOrDefault(queryKey,new ArrayList<>()).contains(commonHeaderDTO.getIp())){
                log.info("{} 白名单ip命中...",trans);
                lockKeyResult.setLocked(false);
                return lockKeyResult.build();
            }
        }catch (Exception e){
            log.error("GrayList Check Er:",e);
        }


        String lockKey = "";
        Long userId = 0L;
        String mapKey = "";
        if (StringUtils.isNotEmpty(commonHeaderDTO.getAccessKey())) {
            userId = CommonHeaderDTO.getUserIdNoEx(commonHeaderDTO);
            lockKey = buildLockKey(appId, userId);
            mapKey = String.valueOf(userId);
        }

        if (0L == userId || whiteDeviceListApp.contains(appId)){
            mapKey = commonHeaderDTO.getDeviceId();
        }

        Set<String> whiteSet = whiteUserMap.get(queryKey);
        if (whiteSet != null && whiteSet.size() > 0){
            if (whiteSet.contains(mapKey)) {
                lockKeyResult.setLocked(false);
                lockKeyResult.setLockKey(lockKey);
                log.info("[{}] TARGET WITHE LIST", trans);
                return lockKeyResult.build();
            }
        }

        try {
            String lockKsy = appId+"_"+commonHeaderDTO.getChannel()+"_"+commonHeaderDTO.getAppVersion().replaceAll("[.]","");
            if (lockKeyCrList.contains(lockKsy)){
                log.info("{} {} {} {} 分版本全锁 {}",trans,appId,commonHeaderDTO.getChannel(),commonHeaderDTO.getDeviceId(),pkgNames);
                lockKeyResult.setLocked(Boolean.TRUE);
                return lockKeyResult.build();
            }
        }catch (Exception e){
            log.error("Solve r:",e);
        }


        boolean channelFlag = false, localFlag = false;
        List<String> joinList = joinChannelStartMap.get(queryKey);
        if (joinList!= null && joinList.size() > 0) {
            // 参与渠道Check
            for (String skipChannel : joinList) {
                if (commonHeaderDTO.getChannel().equals(skipChannel) ||
                        (skipChannel.equalsIgnoreCase("ks") && commonHeaderDTO.getChannel().startsWith(skipChannel)) ||
                        (skipChannel.equalsIgnoreCase("gdt") && commonHeaderDTO.getChannel().startsWith(skipChannel)) ||
                        (skipChannel.equalsIgnoreCase("bd") && commonHeaderDTO.getChannel().startsWith(skipChannel))
                ) {
                    log.info("[{}] App {} User {} Channel {} Need Check", trans, appId, userId, commonHeaderDTO.getChannel());
                    channelFlag = true;
                }
            }
        }
        if (!channelFlag){
            return lockKeyResult.build();
        }

        // 其他情况全锁区
        Set<String> allLockSet = lockAllChannelMap.get(queryKey);
        if (allLockSet != null && allLockSet.size() > 0){
            if (allLockSet.contains(commonHeaderDTO.getChannel())) {
                log.info("{} {} {} {} 全锁渠道 {}",trans,appId,commonHeaderDTO.getChannel(),commonHeaderDTO.getDeviceId(),pkgNames);
                lockKeyResult.setLocked(Boolean.TRUE);
                return lockKeyResult.build();
            }
        }

        if (pkgLock){
            log.info("[{}] PkgTarget {} Lock ,Return true", trans,commonHeaderDTO.getChannel());
            lockKeyResult.setLocked(Boolean.TRUE);
            return lockKeyResult.build();
        }

        // 若是OCPC用户
        if (!"nodsp".equals(dsp)) {
            log.info("[{}] USER {} IS OCPC TARGET {}",trans,commonHeaderDTO.getUserId(),dsp);
            // ios单独处理
            if ("ios".equalsIgnoreCase(commonHeaderDTO.getOs())){
                String city = getCity(commonHeaderDTO,trans);
                lockKeyResult.setCity(city);
                if (StringUtils.isEmpty(city) || (StringUtils.isNotBlank(city) && "海外".equals(city)) || StringUtils.contains("海外",city)){
                    log.info("{} {} {} 城市命中海外 按照非OCPC处理",trans,commonHeaderDTO.getCaid(),city);
                    lockKeyResult.setLocked(true);
                    return lockKeyResult.build();
                }
            }
            return lockKeyResult.build();
        }else {
            if ("ios".equalsIgnoreCase(commonHeaderDTO.getOs())){
                String city = getCity(commonHeaderDTO,trans);
                if (StringUtils.isEmpty(city) || (StringUtils.isNotBlank(city) && "海外".equals(city)) || StringUtils.contains("海外",city)) {
                    log.info("{} {} {} 城市命中海外 按照非OCPC处理", trans, commonHeaderDTO.getCaid(), city);
                    lockKeyResult.setLocked(true);
                    return lockKeyResult.build();
                }
                if (singleBjAndLf.contains(appId)) {
                    if (StringUtils.isNotEmpty(commonHeaderDTO.getCaid())) {
                        lockKeyResult.setCity(city);
                        if (StringUtils.isEmpty(city)) {
                            lockKeyResult.setLocked(true);
                            return lockKeyResult.build();
                        } else {
                            Boolean contains = Arrays.asList("海外", "北京", "廊坊").stream().anyMatch(r -> city.contains(r));
                            boolean isNew = isNewUser(product,commonHeaderDTO);
                            // 包含且是新用户
                            if (contains && isNew) {
                                log.info("{} {} {} 城市命中 按照非OCPC处理", trans, JSON.toJSONString(commonHeaderDTO), city);
                                lockKeyResult.setLocked(true);
                                return lockKeyResult.build();
                            } else {
                                log.info("{} {} {} 命中国内-非OCPC处理", trans, commonHeaderDTO.getCaid(), city);
                                lockKeyResult.setLocked(false);
                                return lockKeyResult.build();
                            }
                        }
                    } else {
                        lockKeyResult.setCity(city);
                        log.info("{} {} {} 命中国内-非OCPC处理", trans, commonHeaderDTO.getCaid(), city);
                        lockKeyResult.setLocked(false);
                        return lockKeyResult.build();
                    }
                }
                if (passedIosList.contains(appId)){
                    lockKeyResult.setCity(city);
                    log.info("{} {} {} 命中国内-非OCPC处理", trans, commonHeaderDTO.getCaid(), city);
                    lockKeyResult.setLocked(false);
                    return lockKeyResult.build();
                }

            }

            if ("android".equalsIgnoreCase(commonHeaderDTO.getOs())){
                if ("hw".equalsIgnoreCase(commonHeaderDTO.getChannel())||
                commonHeaderDTO.getChannel().contains("huawei")) {
                    log.info("华为小米vivo渠道-非ocpc自然量 {} {} {} {} {}",trans,product,
                            commonHeaderDTO.getDeviceId(),commonHeaderDTO.getOaid(),
                            commonHeaderDTO.getChannel());
                    if (hwxmAlLockNtfAppList.contains(appId) || appId > 950){
                        log.info("华为小米Vivo渠道-非ocpc执行全锁区 {} {} {}",trans,product,commonHeaderDTO.getDeviceId());
                        lockKeyResult.setLocked(true);
                        return lockKeyResult.build();
                    }
                }
            }
        }

        // 非ocpc直接锁区 ==> 20220923 mengyazhou
        if (noOcpcToJoinLockAreaAppList.contains(appId) && commonHeaderDTO.getChannel().startsWith("ks")){
            log.info("[{}] KS Not OCPC ,Return true",trans);
            lockKeyResult.setLocked(Boolean.TRUE);
            return lockKeyResult.build();
        }

        if (gdtNoOcpcToJoinLockAreaAppList.contains(appId) &&  commonHeaderDTO.getChannel().startsWith("gdt")){
            log.info("[{}] GDT Not OCPC ,Return true",trans);
            lockKeyResult.setLocked(Boolean.TRUE);
            return lockKeyResult.build();
        }

        // 包名Check
        Set<String> pkgSet = lockPkgMap.get(queryKey);
        if (StringUtils.isNotEmpty(pkgNames) && pkgSet!= null && pkgSet.size() > 0) {
            Set<String> reportPackages = Sets.newHashSet(StringUtils.splitByWholeSeparator(pkgNames, COMMA));
            if (reportPackages.size() > 0) {
                String causePackages = String.join(COMMA, Sets.intersection(pkgSet, reportPackages));
                if (StringUtils.isNotBlank(causePackages)) {
                    log.info("[{}] Pkg Target {} {}",trans, appId, userId);
                    lockKeyResult.setLocked(Boolean.TRUE);
                    return lockKeyResult.build();
                }
            }
        }

        // 区域Check
        try {
            String saveKey = ipService.getSaveKey(commonHeaderDTO);
            GdIpRsp gdIpRsp = ipService.getFormHBase(saveKey);
            if (gdIpRsp == null) {
                 gdIpRsp = ipService.getIpLocation(commonHeaderDTO.getIp());
            }
            // 缓存
            if (StringUtils.isEmpty(gdIpRsp.getCity()) || gdIpRsp.getCity().contains("[")){
                // 使用ipPlus
                String city = ipService.getCityByIpPlus(commonHeaderDTO.getIp(),commonHeaderDTO.getGps());
                if (Strings.isNotEmpty(city)) {
                    gdIpRsp.setCity(city);
                    log.info("[{}] Get By GD Failed Succeed Use IpPlus Get city: {}",trans,city);
                    ipService.saveHBase(gdIpRsp,saveKey);
                }else {
                    log.info("[{}] Get By GD Failed IpPlus Also Failed",trans);
                }
            }else {
                ipService.saveHBase(gdIpRsp,saveKey);
            }
            // 成功则判断
            if (StringUtils.isNotBlank(gdIpRsp.getCity())){
                log.info("[{}] ReqIp: {} , User {} App {}, City {}",trans,commonHeaderDTO.getIp(),userId,appId,gdIpRsp.getCity());
                lockKeyResult.setGdIpRsp(gdIpRsp);
                Set<String> checkCitySet = blackCitySet;
                if ("ios".equalsIgnoreCase(commonHeaderDTO.getOs())) {
                    checkCitySet = iosBlackCitySet;
                }
                Set<String> checkCity = blackCityMap.getOrDefault(queryKey,checkCitySet);
                Set<String> checkXian = addXianMapApps.getOrDefault(appId.toString(),new HashSet<>());
                if (checkXian.contains(commonHeaderDTO.getChannel())){
                    checkCity.add("西安");
                }
                if ("ios".equalsIgnoreCase(commonHeaderDTO.getOs())){
                    checkCity.add("海外");
                }

                // 控制部分产品、部分地区的的锁区地区
                String key = appId+"_"+commonHeaderDTO.getChannel();
                Set<String> tempLockCity = specialChannelLockCity.get(key);
                if (tempLockCity != null && tempLockCity.size() > 0){
                    checkCity = tempLockCity;
                    log.info("[{}] {} {} {} 特殊锁区渠道 使用以下城市 {}",trans,appId,commonHeaderDTO.getIp(),commonHeaderDTO.getChannel(),JSON.toJSONString(checkCity));
                }
                for (String city : checkCity){
                    if (city.contains(gdIpRsp.getCity()) || gdIpRsp.getCity().contains(city)) {
                        localFlag = true;
                        break;
                    }
                }
            }else {
                log.warn("[{}] GET IP FAILED,Rs:{}",trans,JSON.toJSONString(gdIpRsp));
                if ("ios".equalsIgnoreCase(commonHeaderDTO.getOs())){
                    localFlag = true;
                }
            }
            lockKeyResult.setCity(gdIpRsp.getCity());
        }catch (Exception e){
            log.info("获取Ip失败..",e);
            // IOS若判定的确异常 直接认为是锁区
            if ("ios".equalsIgnoreCase(commonHeaderDTO.getOs())){
                localFlag = true;
            }
        }

        if (localFlag){
            // 若触发锁区且无oaid、device_id同时出现
            if (StringUtils.isEmpty(commonHeaderDTO.getOaid()) && StringUtils.isEmpty(commonHeaderDTO.getDeviceId())){
                // 且在白名单
                if (noDeviceSkipAppList.contains(appId)){
                    log.info("==> {} 白名单设备oaid device为空, 不触发锁区",trans);
                    localFlag = false;
                }
            }
        }

        lockKeyResult.setLocked(localFlag);
        if (lockKeyResult.getLocked()){
            lockKeyResult.setLockKey(lockKey);
        }
        return lockKeyResult.build();
    }

    private boolean isNewUser(String product,CommonHeaderDTO commonHeaderDTO){
        try {
            // 没传就是新的
            if (StringUtils.isEmpty(commonHeaderDTO.getCaid())){
                return true;
            }
            UserActiveResp userActiveResp = lockConfigRpc.queryActiveByCaid(product,commonHeaderDTO.getCaid());
            boolean isNew = true;
            if (userActiveResp != null && userActiveResp.getCreateTime() != null){
                String dayStart = DateUtil.today() + " 00:00:00";
                if (DateUtil.parseDateTime(dayStart).getTime() > userActiveResp.getCreateTime()){
                    log.info("{} 注册时间 {} 鉴定为老用户",commonHeaderDTO.getCaid(),
                            DateUtil.formatDateTime(new Date(userActiveResp.getCreateTime())));
                    isNew = false;
                }
            }
            return isNew;
        }catch (Exception e){
            log.warn("Query By Caid Ex ",e);
            return true;
        }
    }

    public String getCity(CommonHeaderDTO commonHeaderDTO,String trans){
        String defalutQueryCity = "海外";
        if (commonHeaderDTO.getAppId()!=null && passedIosList.contains(Integer.valueOf(commonHeaderDTO.getAppId()))){
            defalutQueryCity = "未知";
        }
        try {

            String saveKey = ipService.getSaveKey(commonHeaderDTO);
            GdIpRsp gdIpRsp = ipService.getFormHBase(saveKey);
            if (gdIpRsp == null) {
                gdIpRsp = ipService.getIpLocation(commonHeaderDTO.getIp());
            }
            // 缓存
            if (StringUtils.isEmpty(gdIpRsp.getCity()) || gdIpRsp.getCity().contains("[")){
                // 使用ipPlus
                String city = ipService.getCityByIpPlus(commonHeaderDTO.getIp(),commonHeaderDTO.getGps());
                if (Strings.isNotEmpty(city)) {
                    if(StringUtils.contains("香港",city) ||StringUtils.contains("台湾",city) ||StringUtils.contains("澳门",city)){
                        city = "海外-"+city;
                    }
                    gdIpRsp.setCity(city);
                    log.info("[{}] Get By GD Failed Succeed Use IpPlus Get city: {}",trans,city);
                    return city;
                }else {
                    log.info("[{}] Get By GD Failed IpPlus Also Failed",trans);
                }
            }else {
                return  gdIpRsp.getCity();
            }
            // 成功则判断
            if (StringUtils.isBlank(gdIpRsp.getCity())){
                log.warn("[{}] GET IP FAILED,Rs:{}",trans,JSON.toJSONString(gdIpRsp));
                if ("ios".equalsIgnoreCase(commonHeaderDTO.getOs())){
                    return defalutQueryCity;
                }
            }
            return defalutQueryCity;
        }catch (Exception e){
            log.info("获取Ip失败..",e);
            // IOS若判定的确异常 直接认为是锁区
            return defalutQueryCity;
        }
    }

    public static String userIsOCPC(CommonHeaderDTO commonHeaderDTO,Integer appId,String product){
        try {
            Map<String,Object> param = new HashMap<>();
            param.put("product",product);
            param.put("pkgChannel",commonHeaderDTO.getChannel());
            param.put("os",commonHeaderDTO.getOs().toLowerCase());
            param.put("ocpcDeviceId", MD5.create().digestHex(commonHeaderDTO.getDeviceId()));
            param.put("SourceDeviceId", commonHeaderDTO.getDeviceId());
            param.put("oaid",commonHeaderDTO.getOaid());
            param.put("androidId",commonHeaderDTO.getAndroidId());
            param.put("appId",appId);
            param.put("mac", commonHeaderDTO.getMac());
            param.put("model", commonHeaderDTO.getModel());
            param.put("ip", commonHeaderDTO.getIp());
            param.put("openId", commonHeaderDTO.getOpenId());
            param.put("caid", commonHeaderDTO.getCaid());
            param.put("ua", commonHeaderDTO.getUa());
            param.put("userId", commonHeaderDTO.getUserId());

            String result = HttpUtil.get("http://ocpc-api.shinet-inc.com/dispense/user/event/guiDsp",param);

            if (StringUtils.isNotBlank(result)){
                JSONObject jsonObject = JSONObject.parseObject(result);
                String dsp = jsonObject.getString("data");
                // 2022-12-26 非nosDsp 均为买量
                return dsp;
            }
        }catch (Exception e){
            log.error("锁区ocpc出错 "+product+" :",e);
        }
        return "";
    }


    @Autowired
    private HttpClientService httpClientService;

    private String getChannel(CommonHeaderDTO commonHeaderDTO){
        try {
            String url ="https://bp-api.shinet.cn/bp/user/info";
            HashMap<String,String> param = new HashMap<>();

            String result = httpClientService.doPost(url,param,commonHeaderDTO.getMap());
            if (Strings.isNotEmpty(result)){
                JSONObject object = JSONObject.parseObject(result);
                if (object.getInteger("code") == 0){
                    String channel = object.getJSONObject("result").getString("channel");
                    log.info(">>> {} Source Channel {} ,change to {}",commonHeaderDTO.getAccessKey(),commonHeaderDTO.getChannel(),channel);
                    return channel;
                }
            }
        }catch (Exception e){
            log.warn("Request er:",e);
        }

        return commonHeaderDTO.getChannel();
    }

    private String buildLockKey(Integer appId,Long userId){
        return String.format("%s:%s:%s",appId,userId, UUID.randomUUID().toString().replaceAll("-",""));
    }

    private String buildKey(Integer appId,Integer os){
        return String.format("%s:%s",appId,os);
    }

    @PostConstruct
    @Scheduled(cron = "0 * * * * ?")
    public void refreshConfig(){
        log.info("===> REFRESH CONFIG...");
        List<ConfigRemote> configRemotes = getRemoteConfig();
        if (configRemotes.size() == 0){
            return;
        }

        // 产品埋点配置Load
        lockNeedCheckOCPC = configRemotes.stream()
                .collect(Collectors.toMap(configRemote -> buildKey(configRemote.getAppId(),configRemote.getOs()),
                        ConfigRemote::getProduct,(r1,r2) -> r1));
        // 参与渠道聚合
        joinChannelStartMap = configRemotes.stream()
                .collect(Collectors.toMap(configRemote -> buildKey(configRemote.getAppId(),configRemote.getOs()),
                        configRemote-> Arrays.stream(configRemote.getChannels().split(","))
                                .filter(Strings::isNotEmpty)
                                .collect(Collectors.toList()),(r1, r2) -> r1));
        log.info("===> joinChannelStartMap CONFIG AFTER REFRESH:{}",JSON.toJSONString(joinChannelStartMap));
        // 白名单
        whiteUserMap = configRemotes.stream()
                .collect(Collectors.toMap(configRemote -> buildKey(configRemote.getAppId(),configRemote.getOs()),
                        configRemote-> Arrays.stream(configRemote.getWhitelist().split(","))
                                .filter(Strings::isNotEmpty)
                                .collect(Collectors.toSet()),(r1, r2) -> r1));

        // Device白名单应用
        whiteDeviceListApp =  configRemotes.stream()
                .filter(configRemote -> Integer.valueOf(2).equals(configRemote.getWhiteType()))
                .map(ConfigRemote::getAppId)
                .collect(Collectors.toList());

        // 城市名单
        blackCityMap = configRemotes.stream()
                .collect(Collectors.toMap(configRemote -> buildKey(configRemote.getAppId(),configRemote.getOs()),
                        configRemote-> {
                            Set<String> cityList = Arrays.stream(configRemote.getCities().split(","))
                                    .filter(Strings::isNotEmpty)
                                    .collect(Collectors.toSet());
                            if (cityList.size() == 0){
                                return blackCitySet;
                            }
                            return cityList;
                        },(r1, r2) -> r1));

        // 包名
        lockPkgMap = configRemotes.stream()
                .collect(Collectors.toMap(configRemote -> buildKey(configRemote.getAppId(),configRemote.getOs()),
                        configRemote-> Arrays.stream(configRemote.getPkgs().split(","))
                                .filter(Strings::isNotEmpty)
                                .collect(Collectors.toSet()),(r1, r2) -> r1));

        // 该渠道全锁
        lockAllChannelMap = configRemotes.stream()
                .collect(Collectors.toMap(configRemote -> buildKey(configRemote.getAppId(),configRemote.getOs()),
                        configRemote-> Arrays.stream(configRemote.getBlockAllList().split(","))
                                .filter(Strings::isNotEmpty)
                                .collect(Collectors.toSet()),(r1, r2) -> r1));
        log.info("===> REFRESH CONFIG COMPLETE...");
    }

    @Data
    private static class ConfigRemote{
        private Integer id;
        private Integer appId;
        private Integer os;
        private String product;
        private String channels;
        private String cities;
        private String pkgs;
        private String whitelist;
        private Integer whiteType;
        private String blockAllList;
    }

    private List<ConfigRemote> getRemoteConfig(){
        try {
            String result = HttpUtil.get("http://bp-api.shinet-inc.com/use-event/area/getConfig");
            if (Strings.isNotEmpty(result)){
                return JSON.parseArray(JSON.parseObject(result).getString("data"),ConfigRemote.class);
            }
            return new ArrayList<>();
        } catch (Exception e) {
            log.error("getRemoteConfig error: {}", e.getMessage(), e);
        }
        return new ArrayList<>();
    }

    @Autowired
    private LockedAreaRecordService lockedAreaRecordService;
    @Autowired
    private LockedAreaRecordAuService lockedAreaRecordAuService;

    public void refreshLockKeyResult(Integer appId,CommonHeaderDTO commonHeaderDTO ,LockKeyResult lockKeyResult){
        CompletableFuture.runAsync(()->{
            try {
                // 命中锁区 记录信息
                if (lockKeyResult.getLocked()){
                    lockHbaseService.save(commonHeaderDTO,appId);
                    // 记录锁区用户Mysql
                    String queryKey = buildKey(appId,commonHeaderDTO.getIntOs()+1);
                    String product = lockNeedCheckOCPC.get(queryKey);
                    if (product == null || product.isEmpty()){
                        product = commonHeaderDTO.getProduct();
                    }

                    String city = lockKeyResult.getGdIpRsp() != null?lockKeyResult.getGdIpRsp().getCity() : null;
                    if (StringUtils.isEmpty(city)){
                        city = lockKeyResult.getCity();
                    }
                    if ("ios".equalsIgnoreCase(commonHeaderDTO.getOs())){
                        commonHeaderDTO.setOaid(commonHeaderDTO.getCaid());
                    }
                    lockedAreaRecordService.saveIfNotExist(product,appId,commonHeaderDTO,city,null);
                    lockedAreaRecordAuService.saveIfNotExist(product,appId,commonHeaderDTO,city);
                }else {
                    if (noCacheAppList.contains(appId)) {
                        // 查询一次 若锁区 释放
                        List<String> deviceIdList = new ArrayList<>();
                        deviceIdList.add(commonHeaderDTO.getDeviceId());
                        deviceIdList.add(commonHeaderDTO.getOaid());
                        deviceIdList.add(commonHeaderDTO.getImei());
                        boolean isLocked = lockHbaseService.isLocked(deviceIdList, appId, commonHeaderDTO.getUserId());
                        if (isLocked) {
                            lockHbaseService.delLocked(deviceIdList,appId,commonHeaderDTO.getUserId());
                        }
                    }
                }
            }catch (Exception e){
                log.error("Save LockEx:",e);
            }
        });
    }
}
