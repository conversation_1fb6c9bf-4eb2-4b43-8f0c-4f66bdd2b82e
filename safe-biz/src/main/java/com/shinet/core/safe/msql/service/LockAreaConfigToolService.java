package com.shinet.core.safe.msql.service;

import cn.hutool.core.lang.Pair;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.shinet.core.safe.dto.LockKeyResult;
import com.shinet.core.safe.msql.entity.AndroidLockRst;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.service.androidlock.AndroidLockRstService;
import com.shinet.core.safe.msql.service.androidlock.AndroidLokReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2022/1/8
 */
@Slf4j
@Service
public class LockAreaConfigToolService {

    @Autowired
    private AndroidLockRstService androidLockRstService;
    @Autowired
    private LockAreaConfigService lockAreaConfigService;

    @ApolloJsonValue("${lock.area.tool.black.city:[\"北京\",\"上海\",\"广州\",\"深圳\"]}")
    public Set<String> toolBlackCitySet;


    public Pair<LockKeyResult, AndroidLockRst> isToolLock(CommonHeaderDTO commonHeaderDTO, AndroidLokReq androidLokReq, String ip){
        String product = androidLokReq.getProduct();
        commonHeaderDTO.setIp(ip);
        commonHeaderDTO.setProduct(product);
        commonHeaderDTO.setOs("android");
        String  oaid = androidLokReq.getOaid();
        commonHeaderDTO.setOaid(oaid);
        commonHeaderDTO.setIp(ip);
        commonHeaderDTO.setDeviceId(androidLokReq.getImei());
        commonHeaderDTO.setAndroidId(androidLokReq.getAndroidId());
        commonHeaderDTO.setUa(androidLokReq.getUa());
        commonHeaderDTO.setModel(androidLokReq.getModel());
        LockKeyResult lockKeyResult = new LockKeyResult();
        lockKeyResult.setLocked(Boolean.FALSE);
        lockKeyResult.setOcpc(Boolean.FALSE);

        String channel = androidLokReq.getChannel();
        AndroidLockRst androidLockRst = new AndroidLockRst();
        androidLockRst.setIp(ip);

        AndroidLockRst androidLockRecord = androidLockRstService.queryByOaid(product, oaid, channel);
        if(androidLockRecord!=null){
            if("true".equalsIgnoreCase(androidLockRecord.getLockFlag())){
                lockKeyResult.setLocked(true);
            }else{
                lockKeyResult.setLocked(false);
            }
            androidLockRst.setRemark("获取到存储信息，以此判断" + lockKeyResult.getLocked());
            log.info(commonHeaderDTO.getProduct()+" "+commonHeaderDTO.getOaid()+" android获取到存储信息，以此判断 "+lockKeyResult.getLocked());
            return new Pair<>(lockKeyResult,androidLockRst);
        }

        String dsp = lockAreaConfigService.userIsOCPC(commonHeaderDTO, null, androidLokReq.getProduct());
        boolean isOcpc = !StringUtils.equalsIgnoreCase(dsp,"nodsp");
        if(isOcpc){
            androidLockRst.setIsOcpc(1);
        }else{
            androidLockRst.setIsOcpc(0);
        }
        lockKeyResult.setOcpc(isOcpc);

        String city = lockAreaConfigService.getCity(commonHeaderDTO,"android");
        androidLockRst.setCity(city);
        androidLockRst.setDsp(dsp);
        lockKeyResult.setCity(city);
        //渠道 锁ip 包  非ocpc 城市
        if(StringUtils.isNotBlank(dsp) && StringUtils.isNotBlank(city)){
            if(StringUtils.equalsIgnoreCase("nodsp",dsp) && toolBlackCitySet.stream().anyMatch(ct -> city.contains(ct))){
                String msg = " 非ocpc拉黑city直接锁区 "+city;
                log.info(msg);
                lockKeyResult.setLocked(true);
                androidLockRst.setRemark(msg);
                return new Pair<>(lockKeyResult,androidLockRst);
            }
        }

        //判断是否在锁区渠道
        return new Pair<>(lockKeyResult,androidLockRst);
    }
}
