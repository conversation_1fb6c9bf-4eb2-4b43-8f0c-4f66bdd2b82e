package com.shinet.core.safe.msql.service;

import cn.hutool.core.collection.ListUtil;
import com.shinet.core.safe.lock.enums.TargetType;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.entity.LockBlackList;
import com.shinet.core.safe.msql.mapper.LockBlackListMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* <p>
    * 锁区黑名单表 服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-09-19
*/
@Slf4j
@Service
public class LockBlackListService extends ServiceImpl<LockBlackListMapper, LockBlackList> {

    private Map<Integer, List<String>> blackMap;

    @PostConstruct
    public void refreshWhiteAndBlack(){
        log.info("===> Refresh Black List Start....");

        this.blackMap = list().stream()
                .collect(Collectors.groupingBy(LockBlackList::getTargetType,
                        Collectors.mapping(LockBlackList::getTargetInfo,Collectors.toList())));
        log.info("===> Refresh Black List End....");

    }

    public boolean isInBlackList(CommonHeaderDTO commonHeaderDTO){
        // ip
        List<String> ipList = blackMap.getOrDefault(TargetType.IP.getType(),ListUtil.empty());
        if (ipList.contains(commonHeaderDTO.getIp())){
            return true;
        }

        if ("ios".equalsIgnoreCase(commonHeaderDTO.getOs()) && StringUtils.isNotEmpty(commonHeaderDTO.getCaid())){
            List<String> caidList = blackMap.getOrDefault(TargetType.CA_ID.getType(),ListUtil.empty());
            return caidList.contains(commonHeaderDTO.getCaid());
        }else {
            // oaid
            List<String> deviceList = blackMap.getOrDefault(TargetType.OA_ID.getType(),ListUtil.empty());
            if (StringUtils.isNotEmpty(commonHeaderDTO.getDeviceId())){
                if (deviceList.contains(commonHeaderDTO.getDeviceId())){
                    return true;
                }
            }

            if (StringUtils.isNotEmpty(commonHeaderDTO.getImei())){
                return deviceList.contains(commonHeaderDTO.getImei());
            }

            if (StringUtils.isNotEmpty(commonHeaderDTO.getOaid())){
                return deviceList.contains(commonHeaderDTO.getOaid());
            }
        }

        return false;
    }

}
