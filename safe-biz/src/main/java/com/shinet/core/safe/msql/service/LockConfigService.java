package com.shinet.core.safe.msql.service;

import com.shinet.core.safe.msql.entity.LockConfig;
import com.shinet.core.safe.msql.mapper.LockConfigMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
* <p>
    * 产品锁区配置 服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-09-19
*/
@Service
public class LockConfigService extends ServiceImpl<LockConfigMapper, LockConfig> {

}
