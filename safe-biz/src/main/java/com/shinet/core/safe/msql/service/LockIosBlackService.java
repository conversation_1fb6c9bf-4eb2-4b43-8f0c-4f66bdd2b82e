package com.shinet.core.safe.msql.service;

import com.shinet.core.safe.msql.entity.LockIosBlack;
import com.shinet.core.safe.msql.mapper.LockIosBlackMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-11-06
*/
@Service
@Slf4j
public class LockIosBlackService extends ServiceImpl<LockIosBlackMapper, LockIosBlack> {
    @Scheduled(cron = "0 15 14 * * ?")
    public void saveLockIosBlack(){
        List<LockIosBlack>  dlist = this.baseMapper.queryLockCaid(8);
        int i = 0;
        int updNum = 0;
        for(LockIosBlack lockIosBlack : dlist){
            LockIosBlack dbIs = this.getById(lockIosBlack.getCaid());
            if(dbIs==null){
                lockIosBlack.setCreateTime(new Date());
                lockIosBlack.setUpdateTime(new Date());
                lockIosBlack.setLockA(1);
                lockIosBlack.setReason("自动任务");
                i++;
                save(lockIosBlack);
            }else{
                dbIs.setPnum(lockIosBlack.getPnum());
                updNum++;
                updateById(dbIs);
            }
        }

        log.info("ios锁区执行完成  共插入 "+i+" 更新 "+updNum);
    }

    public boolean isInBlack(String caid){
        try{
            if(StringUtils.isNotBlank(caid)){
                LockIosBlack lockIosBlack =  getById(caid);
                if(lockIosBlack!=null  && lockIosBlack.getLockA()!=null && lockIosBlack.getLockA().equals(1)){
                    log.info(caid+" 锁区黑名单 ");
                    return true;
                }
            }
        }catch (Exception e){
            log.error("",e);
        }
        return false;
    }
}
