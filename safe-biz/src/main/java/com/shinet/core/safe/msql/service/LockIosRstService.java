package com.shinet.core.safe.msql.service;

import cn.hutool.core.lang.Pair;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.safe.msql.entity.LockIosRst;
import com.shinet.core.safe.msql.mapper.LockIosRstMapper;
import com.shinet.core.safe.msql.service.ioslock.IpAliYunService;
import com.shinet.core.safe.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-10-07
*/
@Service
@Slf4j
public class LockIosRstService extends ServiceImpl<LockIosRstMapper, LockIosRst> {
    public String getIosId(String product,String caid){
        return product+"@"+caid;
    }

    public LockIosRst queryById(String product,String caid){
        if(StringUtils.isNotBlank(product) && StringUtils.isNotBlank(caid)){
            String id = getIosId(product,caid);
            LockIosRst lockIosRst = getById(id);
            return lockIosRst;
        }else {
            log.warn("IOS获取锁区结果错误 "+product+" caid:"+caid);
            return null;
        }
    }
    @Autowired
    private RedisUtils redisUtils;
    public void addIosRst(String dsp,String product,String caid,String deviceId,boolean isLock,String userId,String ip,String city,String reason,String appversion){
        // 异步去保存
        CompletableFuture.runAsync(() -> {
            try {
                if (StringUtils.isEmpty(caid)){
                    UUID uid = UUID.randomUUID();
                    saveIosRd(dsp,"canul@"+uid.toString(),product,caid,deviceId,isLock,userId,ip,city,reason,appversion);
                    return;
                }else{
                    LockIosRst dbRst = queryById(product,caid);
                    if(dbRst==null){
                        String id = getIosId(product,caid);
                        String identification = UUID.randomUUID().toString().replaceAll("-", "");
                        boolean isLocked = redisUtils.tryLock(id,identification,"100");
                        if(isLocked){
                            saveIosRd(dsp,id,product,caid,deviceId,isLock,userId,ip,city,reason,appversion);
                            log.info("保存rst结果成功 "+caid+"@"+isLock);
                        }else{
                            log.info("redis锁获取失败rst直接更新 "+caid+"@"+isLock);
                            updateWithRetry(product, caid);
                        }
                    }else{
                        if(!StringUtils.contains(dbRst.getRemark(),reason) || !StringUtils.equalsIgnoreCase(isLock+"",dbRst.getLockFlag())){
                            String rsyStr = reason+"!"+dbRst.getRemark();
                            if(rsyStr.length()>2048){
                                dbRst.setRemark(reason);
                                log.info("reson大于2045 "+caid);
                            }else{
                                dbRst.setRemark(reason+"!"+dbRst.getRemark());
                            }
                            dbRst.setDsp(dsp);
                            dbRst.setLockFlag(isLock+"");
                            dbRst.setUpdateTime(new Date());
                            updateById(dbRst);
                        }
                    }
                }
            }catch (Exception e){
                log.error("存储iOS信息错误",e);
            }
        });
    }

    private void updateWithRetry(String product, String caid) {
        LockIosRst dbRst = null;
        int maxRetries = 3;
        int retryCount = 0;
        while (retryCount < maxRetries) {
            dbRst = queryById(product, caid);
            log.info("ios锁区记录更新重试查询 {}，重试次数：{}", dbRst, retryCount);
            if (dbRst != null) {
                dbRst.setUpdateTime(new Date());
                updateById(dbRst);
                break;
            }
            retryCount++;
            try {
                Thread.sleep(100); // 等待一段时间后重试
            } catch (InterruptedException e) {
                log.error("重试查询时发生中断", e);
            }
        }
        if (retryCount == maxRetries) {
            log.error("ios锁区记录更新重试次数已用完，无法更新记录");
        }
    }

    public void saveIosRd(String dsp,String id,String product,String caid,String deviceId,boolean isLock,String userId,String ip,String city,String reason,String appversion){
        Date date = new Date();
        LockIosRst lockIosRst1 = new LockIosRst();
        lockIosRst1.setCaid(caid);
        lockIosRst1.setCreateTime(date);
        lockIosRst1.setDsp(dsp);
        lockIosRst1.setId(id);
        lockIosRst1.setIdfa(deviceId);
        lockIosRst1.setLockFlag(isLock+"");
        lockIosRst1.setIp(ip);
        lockIosRst1.setCity(city);
        lockIosRst1.setOs("ios");
        lockIosRst1.setProduct(product);
        lockIosRst1.setUpdateTime(date);
        lockIosRst1.setUserId(userId+"");
        lockIosRst1.setRemark(reason);
        lockIosRst1.setAppVersion(appversion);
        save(lockIosRst1);
        log.info("保存rst结果成功 "+caid+"@"+isLock);
    }

    public void shua(){
        LambdaQueryChainWrapper<LockIosRst> lab = lambdaQuery();
        lab.eq(LockIosRst::getCity,"海外");

        List<LockIosRst> dlist = lab.list();
        for(int i=0;i<dlist.size();i++){
            LockIosRst lockIosRst = dlist.get(i);
            Pair<String,String> cityd = IpAliYunService.ipAdress(lockIosRst.getProduct(),lockIosRst.getCaid(),lockIosRst.getIp());

            String cnm = cityd.getValue();
            if(StringUtils.contains(cnm,"苹果")){
                lockIosRst.setCity(cnm);
                lockIosRst.setUpdateTime(new Date());
                updateById(lockIosRst);
            }

            log.info("剩余 "+(dlist.size()-i));
        }
    }

}
