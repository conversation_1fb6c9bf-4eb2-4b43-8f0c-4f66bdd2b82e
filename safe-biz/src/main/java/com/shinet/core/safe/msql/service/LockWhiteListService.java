package com.shinet.core.safe.msql.service;

import cn.hutool.core.collection.ListUtil;
import com.shinet.core.safe.lock.enums.TargetType;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.entity.LockWhiteList;
import com.shinet.core.safe.msql.mapper.LockWhiteListMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* <p>
    * 锁区白名单表 服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-09-19
*/
@Slf4j
@Service
public class LockWhiteListService extends ServiceImpl<LockWhiteListMapper, LockWhiteList> {

    private Map<Integer, List<String>> whiteMap;

    @PostConstruct
    public void refreshWhiteAndBlack(){
        log.info("===> Refresh White List Start....");

        this.whiteMap = list().stream()
                .collect(Collectors.groupingBy(LockWhiteList::getTargetType,
                        Collectors.mapping(LockWhiteList::getTargetInfo,Collectors.toList())));
        log.info("===> Refresh White List End....");

    }

    public boolean isInWhiteList(CommonHeaderDTO commonHeaderDTO){
        // ip
        List<String> ipList = whiteMap.getOrDefault(TargetType.IP.getType(), ListUtil.empty());
        if (ipList.contains(commonHeaderDTO.getIp())){
            return true;
        }

        if ("ios".equalsIgnoreCase(commonHeaderDTO.getOs()) && StringUtils.isNotEmpty(commonHeaderDTO.getCaid())){
            List<String> caidList = whiteMap.getOrDefault(TargetType.CA_ID.getType(),ListUtil.empty());
            return caidList.contains(commonHeaderDTO.getCaid());
        }else {
            // oaid
            List<String> deviceList = whiteMap.getOrDefault(TargetType.OA_ID.getType(),ListUtil.empty());
            if (StringUtils.isNotEmpty(commonHeaderDTO.getDeviceId())){
                if (deviceList.contains(commonHeaderDTO.getDeviceId())){
                    return true;
                }
            }

            if (StringUtils.isNotEmpty(commonHeaderDTO.getImei())){
                return deviceList.contains(commonHeaderDTO.getImei());
            }

            if (StringUtils.isNotEmpty(commonHeaderDTO.getOaid())){
                return deviceList.contains(commonHeaderDTO.getOaid());
            }
        }

        return false;
    }
}
