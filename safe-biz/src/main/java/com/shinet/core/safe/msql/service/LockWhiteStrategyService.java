package com.shinet.core.safe.msql.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.shinet.core.safe.dto.StrategyResult;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.entity.LockWhiteStrategy;
import com.shinet.core.safe.msql.enums.WhiteStrategyEnums;
import com.shinet.core.safe.msql.mapper.LockWhiteStrategyMapper;
import com.shinet.core.safe.msql.service.white.strategy.HighQualityDeviceReleaseRecordService;
import com.shinet.core.safe.msql.service.white.strategy.StrategyInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
@Slf4j
public class LockWhiteStrategyService extends ServiceImpl<LockWhiteStrategyMapper, LockWhiteStrategy> {

    @Autowired
    private Map<String, StrategyInterface> strategyMap;
    @ApolloJsonValue("${lock.device.id.white.list}")
    public List<String> whiteDeviceIdList;
    @Autowired
    private HighQualityDeviceReleaseRecordService highQualityDeviceReleaseRecordService;

    //key=product_strategy
    private Map<String, LockWhiteStrategy> VALID_STRATEGY = new HashMap<>();

    @PostConstruct
    @Scheduled(cron = "12 0/3 * * * ? ")
    public void initProductConfig() {
        //未删除
        Map<String, LockWhiteStrategy> map = lambdaQuery().eq(LockWhiteStrategy::getDelFlag, 0).list().stream().collect(Collectors.toMap(o -> o.getProduct() + "_" + o.getStrategy(), o -> o, (o1, o2) -> o1));
        VALID_STRATEGY = map;
        /*CommonHeaderDTO dto = new CommonHeaderDTO();
        dto.setOaid("CF157C96FDEC48C7A258BD6E18F7442Afb909844f98de93b4671104bc2137803");
        System.out.println(JSONObject.toJSONString(getStrategyResult("qzfkdd", dto)));*/
    }

    public LockWhiteStrategy getByProductAndStrategy(String product, int strategy) {
        return VALID_STRATEGY.get(product + "_" + strategy);
    }

    /**
     * 获取策略白, 满足一个条件，就白了
     * @param product
     * @param commonHeaderDTO
     * @return
     */
    public StrategyResult getStrategyResult(String product, CommonHeaderDTO commonHeaderDTO) {
        log.info("用户deviceId {}", JSONObject.toJSONString(commonHeaderDTO));
        //如果在白名单内则直接返回null，测试白名单
        if (whiteDeviceIdList.contains(commonHeaderDTO.getRealIp())) {
            return null;
        }
        for(WhiteStrategyEnums whiteStrategyEnums : WhiteStrategyEnums.values()) {
            try {
                StrategyResult result = strategyMap.get(whiteStrategyEnums.getServiceName()).checkStrategy(product, commonHeaderDTO);
                if (null != result && result.isWhiteFlag()) {
                    highQualityDeviceReleaseRecordService.insertRecord(commonHeaderDTO, result);
                    log.info("满足白策略校验, product:{}, header:{}, res:{}", product, JSONObject.toJSONString(commonHeaderDTO), JSONObject.toJSONString(result));
                    return result;
                }
            }catch (Exception ex) {
                log.error("校验是否满足白策略, enum:{}, product:{}, header:{}", whiteStrategyEnums, product, JSONObject.toJSONString(commonHeaderDTO), ex);
            }
        }
        return null;
    }
}
