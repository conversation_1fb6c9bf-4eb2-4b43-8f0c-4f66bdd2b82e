package com.shinet.core.safe.msql.service;

import com.shinet.core.safe.hsq.rsp.GdIpRsp;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.entity.LockedAreaRecord;
import com.shinet.core.safe.msql.entity.LockedAreaRecordAu;
import com.shinet.core.safe.msql.mapper.LockedAreaRecordAuMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.safe.util.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.UUID;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2023-01-09
*/
@Service
public class LockedAreaRecordAuService extends ServiceImpl<LockedAreaRecordAuMapper, LockedAreaRecordAu> {
    private Boolean isExist(String product,String oaId,Date logday,String ip){
        logday = DateUtils.getDayBeginDate(logday);
        if (StringUtils.isEmpty(oaId)){
            return lambdaQuery()
                    .eq(LockedAreaRecordAu::getProduct,product)
                    .eq(LockedAreaRecordAu::getIp,ip)
                    .eq(LockedAreaRecordAu::getLogday,logday)
                    .count() > 0;
        }
        return lambdaQuery()
                .eq(LockedAreaRecordAu::getProduct,product)
                .eq(LockedAreaRecordAu::getOaId,oaId)
                .eq(LockedAreaRecordAu::getLogday,logday)
                .count() > 0;
    }

    public void saveIfNotExist(String product, Integer appId, CommonHeaderDTO commonHeaderDTO,String city){
        Date now = new Date();
        if (!isExist(product,commonHeaderDTO.getOaid(),now,commonHeaderDTO.getIp())){
            LockedAreaRecordAu lockedAreaRecord = new LockedAreaRecordAu();
            lockedAreaRecord.setLogday(now);
            lockedAreaRecord.setProduct(product);
            lockedAreaRecord.setAppId(appId);
            lockedAreaRecord.setOs(commonHeaderDTO.getOs());
            lockedAreaRecord.setAccessKey(commonHeaderDTO.getAccessKey());
            lockedAreaRecord.setDeviceId(commonHeaderDTO.getDeviceId());
            lockedAreaRecord.setOaId(commonHeaderDTO.getOaid());
            if ("ios".equalsIgnoreCase(commonHeaderDTO.getOs())){
                lockedAreaRecord.setOaId(commonHeaderDTO.getCaid());
            }
            lockedAreaRecord.setImei(commonHeaderDTO.getImei());
            lockedAreaRecord.setIp(commonHeaderDTO.getIp());
            lockedAreaRecord.setAppVersion(commonHeaderDTO.getAppVersion());
            lockedAreaRecord.setChannel(commonHeaderDTO.getChannel());
            lockedAreaRecord.setLocked(1);
            lockedAreaRecord.setCity(city);
            lockedAreaRecord.setCreateTime(now);
            lockedAreaRecord.setUpdateTime(now);
            save(lockedAreaRecord);
        }
    }
}
