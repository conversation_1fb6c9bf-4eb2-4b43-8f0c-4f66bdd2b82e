package com.shinet.core.safe.msql.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.safe.dto.LockKeyResult;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.entity.LockedAreaRecord;
import com.shinet.core.safe.msql.entity.LockedAreaRecordMin;
import com.shinet.core.safe.msql.mapper.LockedAreaRecordMapper;
import com.shinet.core.safe.msql.mapper.LockedAreaRecordMinMapper;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2022-08-31
*/
@Service
public class LockedAreaRecordMinService extends ServiceImpl<LockedAreaRecordMinMapper, LockedAreaRecordMin> {

    private Boolean isExist(String product, Long userId){
        Integer count = lambdaQuery().eq(LockedAreaRecordMin::getProduct, product)
                .eq(LockedAreaRecordMin::getUserId, userId)
                .count();
        return count > 0;
    }

    public void saveIfNotExist( Integer appId, CommonHeaderDTO commonHeaderDTO, LockKeyResult result){
        String product = commonHeaderDTO.getProduct();
        Long userId = CommonHeaderDTO.getUserIdNoEx(commonHeaderDTO);
        if (!isExist(product,userId)){
            LockedAreaRecordMin lockedAreaRecord = new LockedAreaRecordMin();
            lockedAreaRecord.setProduct(product);
            lockedAreaRecord.setAppId(appId);
            lockedAreaRecord.setOs(commonHeaderDTO.getOs());
            lockedAreaRecord.setAccessKey(commonHeaderDTO.getAccessKey());
            lockedAreaRecord.setOpenId(commonHeaderDTO.getOpenId());

            lockedAreaRecord.setIp(commonHeaderDTO.getIp());
            lockedAreaRecord.setLocked(result.getLocked() ? 1 : 0);
            lockedAreaRecord.setCity(result.getCity());
            Date now = new Date();
            lockedAreaRecord.setCreateTime(now);
            lockedAreaRecord.setUpdateTime(now);
            lockedAreaRecord.setUserId(userId);
            lockedAreaRecord.setIsOcpc(result.getOcpc() ? 1 : 0);
            save(lockedAreaRecord);
        }
    }
}
