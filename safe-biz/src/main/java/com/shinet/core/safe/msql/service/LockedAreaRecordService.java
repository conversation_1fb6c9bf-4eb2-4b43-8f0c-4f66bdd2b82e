package com.shinet.core.safe.msql.service;

import com.shinet.core.safe.hsq.rsp.GdIpRsp;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.entity.LockedAreaRecord;
import com.shinet.core.safe.msql.mapper.LockedAreaRecordMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2022-08-31
*/
@Service
public class LockedAreaRecordService extends ServiceImpl<LockedAreaRecordMapper, LockedAreaRecord> {

    private Boolean isExist(String product,String oaId){
        return lambdaQuery().eq(LockedAreaRecord::getProduct,product)
                .eq(LockedAreaRecord::getOaId,oaId)
                .count() > 0;
    }

    public void saveIfNotExist(String product,Integer appId,CommonHeaderDTO commonHeaderDTO,String city,String reason){
        if (!isExist(product,commonHeaderDTO.getOaid())){
            LockedAreaRecord lockedAreaRecord = new LockedAreaRecord();
            lockedAreaRecord.setProduct(product);
            lockedAreaRecord.setAppId(appId);
            lockedAreaRecord.setOs(commonHeaderDTO.getOs());
            lockedAreaRecord.setAccessKey(commonHeaderDTO.getAccessKey());
            lockedAreaRecord.setDeviceId(commonHeaderDTO.getDeviceId());
            lockedAreaRecord.setOaId(commonHeaderDTO.getOaid());
            if ("ios".equalsIgnoreCase(commonHeaderDTO.getOs())){
                lockedAreaRecord.setOaId(commonHeaderDTO.getCaid());
            }
            lockedAreaRecord.setImei(commonHeaderDTO.getImei());
            lockedAreaRecord.setIp(commonHeaderDTO.getIp());
            lockedAreaRecord.setAppVersion(commonHeaderDTO.getAppVersion());
            lockedAreaRecord.setChannel(commonHeaderDTO.getChannel());
            lockedAreaRecord.setLocked(1);
            lockedAreaRecord.setCity(city);
            Date now = new Date();
            lockedAreaRecord.setCreateTime(now);
            lockedAreaRecord.setUpdateTime(now);
            lockedAreaRecord.setLockedReason(reason);
            save(lockedAreaRecord);
        }
    }
}
