package com.shinet.core.safe.msql.service;

import com.shinet.core.safe.msql.entity.Tables;
import com.shinet.core.safe.msql.mapper.InfomationSchemaMapper;
import com.shinet.core.safe.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class MysqlTableService {
    public void createIfNot(String tablePre) {
        try {
            infomationSchemaMapper.createOrderUnionTable(tablePre);
        } catch (Exception e) {
            log.warn("",e);
        }
    }
    @Autowired
    InfomationSchemaMapper infomationSchemaMapper;
    /**
     *
     * @param tablePre gpt_chat_info
     */
    public void renameTable(String tablePre,int days) {
        String dateStr = DateUtils.formatDateYMD(new Date());
        List<Tables> tbs = infomationSchemaMapper.queryTablesByEnd(tablePre,dateStr);
        if(tbs.size()==0){
            try {
                infomationSchemaMapper.renameTable(tablePre,dateStr);
                log.info("infomat rename " + tablePre+dateStr + " 成功");
            } catch (Exception e) {
                log.warn("",e);
            }
            try {
                infomationSchemaMapper.createOrderUnionTable(tablePre);
                log.info("infomat create " + tablePre+dateStr + " 成功");
            } catch (Exception e) {
                log.warn("",e);
            }
        }
        try {
            String delDateStr = DateUtils.formatDateYMD(new Date(System.currentTimeMillis() - TimeUnit.DAYS.toMillis(60)));
            List<Tables>  delTabs = infomationSchemaMapper.queryTablesByEnd(tablePre,delDateStr);
            if(delTabs.size()>0){
                infomationSchemaMapper.truncateOrderUnionTable(tablePre,delDateStr);
                infomationSchemaMapper.dropOrderUnionTable(tablePre,delDateStr);
                log.info("infomat 删除表 " + delDateStr + " 成功");
            }
        } catch (Exception e) {
            log.warn("",e);
        }
    }
}
