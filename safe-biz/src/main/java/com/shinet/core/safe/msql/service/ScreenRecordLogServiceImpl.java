package com.shinet.core.safe.msql.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.safe.enums.StoreNameEnums;
import com.shinet.core.safe.msql.entity.ScreenRecordLog;
import com.shinet.core.safe.msql.entity.ScreenRecordReq;
import com.shinet.core.safe.msql.mapper.ScreenRecordLogMapper;
import com.shinet.core.safe.msql.service.androidlock.AndroidLockService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @description 针对表【screen_record_log】的数据库操作Service实现
 * @createDate 2024-11-14 15:52:34
 */
@Slf4j
@Service
public class ScreenRecordLogServiceImpl extends ServiceImpl<ScreenRecordLogMapper, ScreenRecordLog> {
    public static String recordLogTableName = "screen_record_log";

    @Autowired
    MysqlTableService mysqlTableService;

    /**
     * 异步保存
     *
     * @param recordLog
     * @param screenRecordReq
     */
    public void saveAsync(ScreenRecordLog recordLog, ScreenRecordReq screenRecordReq) {
        mysqlTableService.createIfNot(recordLogTableName);
        CompletableFuture.runAsync(() -> {
            try {
                Date date = new Date();
                recordLog.setProduct(screenRecordReq.getProduct());
                recordLog.setOs(screenRecordReq.getOs());
                recordLog.setOaid(screenRecordReq.getOaid());
                recordLog.setImei(screenRecordReq.getImei());
                recordLog.setDeviceId(screenRecordReq.getDeviceId());
                recordLog.setChannel(screenRecordReq.getChannel());
                recordLog.setUserId(String.valueOf(screenRecordReq.getUserId()));
                recordLog.setAppVersion(screenRecordReq.getAppVersion());
                recordLog.setCreateTime(date);
                recordLog.setUpdateTime(date);
                recordLog.setAppId(screenRecordReq.getAppId());

                if (StringUtils.isBlank(recordLog.getStoreName())) {
                    StoreNameEnums storeNameEnums = AndroidLockService.getStoreName(screenRecordReq.getProduct(), screenRecordReq.getChannel());
                    recordLog.setStoreName(storeNameEnums.val);
                }
                save(recordLog);
            } catch (Exception e) {
                log.error("录屏检测日志存储失败 {}", recordLog, e);
            }
        });
    }
}




