package com.shinet.core.safe.msql.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableList;
import com.shinet.core.safe.common.HttpClientService;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.entity.ShumeiRiskInfo;
import com.shinet.core.safe.msql.entity.ShumeiRiskReq;
import com.shinet.core.safe.msql.mapper.ShumeiRiskInfoMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.safe.msql.mapper.TableExtMap;
import com.shinet.core.safe.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2022-05-24
*/
@Slf4j
@Service
public class ShumeiRiskInfoService extends ServiceImpl<ShumeiRiskInfoMapper, ShumeiRiskInfo> {

    @Autowired
    TableExtMap tableExtMap;

    // 设备风险信息url
    private static final String URL = "http://api-tianxiang-bj.fengkongcloud.com/tianxiang/v4";
    // 事件接口
    private static final String EVENT_URL = "http://api-skynet-bj.fengkongcloud.com/v4/event";

    private static final String accessKey = "S5GqEHlqAy6XJ76sEkng";
    private static final String tableName = "shumei_risk_info";

    public Boolean riskInfo(ShumeiRiskReq req, CommonHeaderDTO headerDTO) {
        // 校验设备id
        if (StringUtils.isBlank(headerDTO.getDeviceId())) {
            log.warn("用户: "+headerDTO.getUserId()+" 设备信息错误 "+ JSON.toJSONString(headerDTO));
            return false;
        }
        if (StringUtils.isBlank(req.getShuMeiDeviceId())) {
            log.warn("用户: "+headerDTO.getUserId()+" 数美设备信息错误 "+ JSON.toJSONString(req));
            return false;
        }
        // 校验 eventId
        if(StringUtils.isBlank(req.getEventId())){
            log.warn("用户: "+headerDTO.getUserId() + " 事件id为空 "+JSON.toJSONString(req));
            return false;
        }

        // 校验 tokenId
        if(StringUtils.isBlank(req.getTokenId())){
            log.warn("用户: "+headerDTO.getUserId() + " tokenId为空 "+JSON.toJSONString(req));
            return false;
        }


        /**
         * 事件请求接口
         */
        JSONObject eventResultObj = null;
        HashMap<String,Object> eventRequestParam = new HashMap<>();
        eventRequestParam.put("accessKey",accessKey);
        eventRequestParam.put("appId",headerDTO.getAppId());
        eventRequestParam.put("eventId",req.getEventId());

        HashMap<String,Object> eventReqData = new HashMap<>();
        // 如果是注册、登录事件
        if ("register".equals(req.getEventId()) || "login".equals(req.getEventId())){
            if (StringUtils.isBlank(req.getType())){
                log.warn("用户: "+headerDTO.getUserId() + " type参数为空 "+JSON.toJSONString(req));
                return false;
            }else {
                eventReqData.put("type",req.getType());
            }
        }

        // 如果是提现事件
        if ("withdraw".equals(req.getEventId())){
            if (StringUtils.isBlank(req.getWithdrawAmount().toString()) || StringUtils.isBlank(req.getWithdrawAccountId())){
                log.warn("用户: "+headerDTO.getUserId() + " 提现金额/提现账户 参数为空 "+JSON.toJSONString(req));
                return false;
            }else {
                eventReqData.put("withdrawAmount",req.getWithdrawAmount());
                eventReqData.put("withdrawAccountId",req.getWithdrawAccountId());
            }
        }

        // 激活事件
        if ("activation".equals(req.getEventId())){
            if (StringUtils.isBlank(req.getChannel())){
                log.warn("用户: "+headerDTO.getUserId() + " 激活渠道 参数为空 "+JSON.toJSONString(req));
                return false;
            }else {
                String imei = headerDTO.getImei();
                eventReqData.put("advertisingId",StringUtils.isBlank(imei)?headerDTO.getDeviceId():imei);
                eventReqData.put("apputm",req.getChannel());
            }
        }

        // 广告相关事件
        List<String> ads = ImmutableList.of("adGet","adShow","adReward","adFinish","adClick");
        if (ads.contains(req.getEventId())){
            if (StringUtils.isBlank(req.getAdType()) || StringUtils.isBlank(req.getPosId())){
                log.warn("用户: "+headerDTO.getUserId() + " 广告类型/广告代码位id 参数为空 "+JSON.toJSONString(req));
                return false;
            }else {
                eventReqData.put("adType",req.getAdType());
                eventReqData.put("codeId",req.getPosId());
            }
            // 发放奖励事件
            if ("adReward".equals(req.getEventId())){
                if (StringUtils.isBlank(req.getRewardAmount().toString()) || StringUtils.isBlank(req.getTaskId())){
                    log.warn("用户: "+headerDTO.getUserId() + " 激励金额/活动id 参数为空 "+JSON.toJSONString(req));
                    return false;
                }else {
                    eventReqData.put("rewardAmount",req.getRewardAmount());
                    eventReqData.put("taskId",req.getTaskId());
                }
            }
            // 广告消失事件
            if ("adFinish".equals(req.getEventId())){
                if(StringUtils.isBlank(req.getAdFinishType())){
                    log.warn("用户: "+headerDTO.getUserId() + " 广告消失方式 参数为空 "+JSON.toJSONString(req));
                    return false;
                }else {
                    eventReqData.put("adFinishType",req.getAdFinishType());
                }
            }
        }

        // 接受邀请事件
        if ("fission".equals(req.getEventId())){
            if (StringUtils.isBlank(req.getInviteUserId())){
                log.warn("用户: "+headerDTO.getUserId() + " 邀请人id 参数为空 "+JSON.toJSONString(req));
                return false;
            }else {
                eventReqData.put("inviteTokenId",req.getInviteUserId());
            }
        }

        // app 版本处理 三点四段数 例：0.0.0.0
        String std = ".0.0.0.0";
        String appVersion = headerDTO.getAppVersion();
        if (!StringUtils.isBlank(appVersion)){
            int len = appVersion.length();
            if (len>7){
                appVersion = appVersion.substring(0,7);
            }else if(len<7){
                appVersion = appVersion + std.substring(0,7-len);
            }
        }

        // tokenId  激活事件不需要传tokenId
        if (!"activation".equals(req.getEventId())){
            eventReqData.put("tokenId",req.getTokenId());
        }

        eventReqData.put("deviceId",req.getShuMeiDeviceId());
        // todo 测试使用手动参数 上线提交请求IP
        eventReqData.put("ip",headerDTO.getIp());
//        eventReqData.put("ip","*********");
        // 区分账号体系（不同app）  0-不区分   1-区分
        eventReqData.put("isTokenSeperate",1);
        eventReqData.put("timestamp",System.currentTimeMillis());
        eventReqData.put("os",headerDTO.getOs());
        eventReqData.put("appVersion",appVersion);

        eventRequestParam.put("data",JSONObject.parseObject(JSON.toJSONString(eventReqData)));

        log.info("调用数美事件接口-请求参数 eventRequestParam:{} ",JSON.toJSON(eventRequestParam));
        String eventResult = HttpClientService.doPostJson(EVENT_URL, JSON.toJSONString(eventRequestParam), null);
        log.info("调用数美事件接口-返回 result:{}",eventResult);
        log.info("result:{}", JSONObject.parseObject(eventResult));
        if (StringUtils.isNotBlank(eventResult)) {
            JSONObject jsonObject = JSONObject.parseObject(eventResult);
            if (1100==jsonObject.getIntValue("code")){
                eventResultObj = jsonObject;
            }
        }

        /**
         * 风险信息接口
         */
        JSONObject riskInfoObj = null;
        // 请求参数
        HashMap<String,Object> requestParam = new HashMap<>();
        requestParam.put("accessKey",accessKey);

        HashMap<String,String> data = new HashMap<>();
        data.put("deviceId",req.getShuMeiDeviceId());
        data.put("ip",headerDTO.getIp());
        requestParam.put("data",JSONObject.parseObject(JSON.toJSONString(data)));

        // 调用接口
        log.info("调用数美风险信息接口-请求参数 requestParam:{} ",JSON.toJSON(requestParam));
        String result = HttpClientService.doPostJson(URL, JSON.toJSONString(requestParam), null);
        log.info("调用数美风险信息接口-返回 result:{}",result);

        if (StringUtils.isNotBlank(result)) {
            JSONObject jsonObject = JSONObject.parseObject(result);
            if (1100==jsonObject.getIntValue("code")){
                riskInfoObj = jsonObject;
            }
        }
        log.info("--------> eventResult: {}",eventResult);
        log.info("--------> riskInfoObj: {}",riskInfoObj);
        if (null!=eventResultObj && null!=riskInfoObj) {
            // 存储风险识别信息
            saveRiskInfo(req,headerDTO,riskInfoObj,eventResultObj);
        }
        return true;
    }

    /**
     * 保存风险识别信息
     * @param headerDTO
     * @param riskObject
     *
     *
     * jsonObject 示例
     *
     * {
     *     "code": 1100,
     *     "message": "成功",
     *     "profileExist": 1,
     *     "requestId": "7a5445716f0581c2ab1d381a6af4d1b8",
     *     "ipLabels": {
     *         "risk_ip": {
     *             "risk_ip": 1,
     *             "risk_ip_last_ts": *************
     *         },
     *         "ip_continent": {
     *             "ip_continent": "亚洲"
     *         },
     *         "ip_country": {
     *             "ip_country": "中国"
     *         },
     *         "ip_province": {
     *             "ip_province": "上海"
     *         },
     *         "ip_city": {
     *             "ip_city": "浦东区"
     *         },
     *         "ip_owner": {
     *             "ip_owner": "移动"
     *         },
     *         "ip_longitude": {
     *             "ip_longitude": 39.90279
     *         },
     *         "ip_latitude": {
     *             "ip_latitude": 39.90279
     *         },
     *         "b_cmwap": {
     *             "b_cmwap": 0,
     *             "b_cgn": {
     *                 "b_cgn": 0
     *             }
     *         }
     *     },
     *     "phonePrimaryInfo": {
     *         "phone_city": "鞍山",
     *         "phone_operator": "移动",
     *         "phone_province": "辽宁"
     *     },
     *     "devicePrimaryInfo": {
     *         "abi": "arm64-v8a",
     *         "acc": {
     *             "enable": "0",
     *             "service": [],
     *             "suc": "1"
     *         },
     *         "adbEnabled": null,
     *         "adid": "065e22a51d96f259",
     *         "appname": "com.sabac.hy",
     *         "appver": "2.1.0-15722",
     *         "availableSpace": 13781127168,
     *         "band": "953_GEN_PACK-1.179545.1.181534.1",
     *         "battery": 10,
     *         "batteryState": 3,
     *         "board": "msm8953",
     *         "boot": 1648950564946,
     *         "bootId": "0|none_or_error",
     *         "bootTime": null,
     *         "brand": "vivo",
     *         "brightness": 34,
     *         "bssid": "020000000000",
     *         "countryIso": null,
     *         "cpuCount": 8,
     *         "cpuFreq": 2208000,
     *         "cpuModel": "",
     *         "devicet": 1649210574145,
     *         "files": "/data/user/0/com.sabac.hy/files",
     *         "fingerprint": "vivo/PD1730E/PD1730E:8.1.0/OPM1.171019.011/compil12031549:user/release-keys",
     *         "freeSpace": 13781127168,
     *         "input": [
     *             "InputMethodInfo{com.sohu.inputmethod.sogou.vivo/.SogouIME, settings com.sohu.inputmethod.sogou.SogouIMESettingsLauncher}"
     *         ],
     *         "installTime": null,
     *         "manufacturer": "vivo",
     *         "memory": 3736973312,
     *         "mockLoc": 0,
     *         "model": "V1730EA",
     *         "network": "wifi",
     *         "oaid": "56877d82c6dbc089e2e8f8143a55826c9b9d194ef680b03252864c67846914bd",
     *         "operator": "46001",
     *         "os": "android",
     *         "osver": "8.1.0",
     *         "osverStr": null,
     *         "scaledDensity": null,
     *         "screen": "1080,2154,480",
     *         "screenOn": 1,
     *         "sdkver": "3.0.6",
     *         "signdn": "CN=Zaiya@Sabac, OU=Sabac, O=Sabac, L=GuangZhou, ST=GuangDong, C=86",
     *         "simstate": "READY,ABSENT",
     *         "ssid": "<unknown ssid>",
     *         "targetSdk": 29,
     *         "totalSpace": ***********,
     *         "ubiquityIdentityToken": null,
     *         "updateTimes": null,
     *         "usbstate": "charging",
     *         "userInterfaceIdiom": null,
     *         "wifiip": "*************"
     *     },
     *     "tokenLabels": {
     *         "machine_account_risk": {
     *             "b_machine_control_tokenid": 1,
     *             "b_machine_control_tokenid_last_ts": *************,
     *             "b_offer_wall_tokenid": 1,
     *             "b_offer_wall_tokenid_last_ts": *************
     *         },
     *         "UGC_account_risk": {
     *             "b_politics_risk_tokenid": 1,
     *             "b_politics_risk_tokenid_last_ts": *************,
     *             "b_sexy_risk_tokenid": 1,
     *             "b_sexy_risk_tokenid_last_ts": *************,
     *             "b_advertise_risk_tokenid": 1,
     *             "b_advertise_risk_tokenid_last_ts": *************
     *         },
     *         "scene_account_risk": {
     *             "i_tout_risk_tokenid": 1,
     *             "i_tout_risk_tokenid_last_ts": *************
     *         },
     *         "account_active_info": {
     *             "i_tokenid_first_active_timestamp": *************,
     *             "i_tokenid_active_days_7d": 5,
     *             "i_tokenid_active_days_4w": 5
     *         },
     *         "account_freq_info": {
     *             "i_tokenid_login_cnt_1d": 5,
     *             "i_tokenid_login_cnt_7d": 5
     *         },
     *         "account_relate_info": {
     *             "i_tokenid_relate_smid_cnt_1d": 5,
     *             "i_tokenid_relate_smid_cnt_7d": 5,
     *             "i_tokenid_relate_ip_city_cnt_1d": 5,
     *             "i_tokenid_relate_ip_city_cnt_7d": 5
     *         },
     *         "account_common_info": {
     *             "s_tokenid_relate_smid_info_map_4w": [
     *                 {
     *                     "smid": "xxxx1",
     *                     "days": "3"
     *                 },
     *                 {
     *                     "smid": "xxxx2",
     *                     "days": "5"
     *                 },
     *                 {
     *                     "smid": "xxxx3",
     *                     "days": "10"
     *                 }
     *             ],
     *             "s_tokenid_relate_ip_city_info_map_4w": [
     *                 {
     *                     "city": "北京",
     *                     "days": "3"
     *                 },
     *                 {
     *                     "city": "长沙",
     *                     "days": "5"
     *                 },
     *                 {
     *                     "city": "武汉",
     *                     "days": "10"
     *                 }
     *             ]
     *         }
     *     },
     *     "deviceLabels": {
     *         "id": "33333",
     *         "fake_device": {
     *             "b_pc_emulator": 1,
     *             "b_pc_emulator_last_ts": *************,
     *             "b_cloud_device": 1,
     *             "b_cloud_device_last_ts": *************,
     *             "b_altered": 1,
     *             "b_altered_last_ts": *************,
     *             "b_multi_boxing": 1,
     *             "b_multi_boxing_last_ts": *************,
     *             "b_multi_boxing_by_os": 1,
     *             "b_multi_boxing_by_os_last_ts": *************,
     *             "b_multi_boxing_by_app": 1,
     *             "b_multi_boxing_by_app_last_ts": *************,
     *             "b_faker": 1,
     *             "b_faker_last_ts": *************,
     *             "b_farmer": 1,
     *             "b_farmer_last_ts": *************,
     *             "b_offerwall": 1,
     *             "b_offerwall_last_ts": *************,
     *             "other": {
     *                 "b_mismatch": 1,
     *                 "b_mismatch_last_ts": *************
     *             }
     *         },
     *         "device_suspicious_labels": {
     *             "b_root": 1,
     *             "b_root_last_ts": *************,
     *             "b_sim": 1,
     *             "b_sim_last_ts": *************,
     *             "b_debuggable": 1,
     *             "b_debuggable_last_ts": *************,
     *             "b_vpn": 1,
     *             "b_vpn_last_ts": *************,
     *             "b_monkey_apps": 1,
     *             "b_monkey_apps_last_ts": *************,
     *             "b_acc": 1,
     *             "b_acc_last_ts": *************,
     *             "b_multi_boxing_apps": 1,
     *             "b_multi_boxing_apps_last_ts": *************,
     *             "b_hook": 1,
     *             "b_hook_last_ts": *************,
     *             "b_vpn_apps": 1,
     *             "b_vpn_apps_last_ts": *************,
     *             "b_manufacture": 1,
     *             "b_manufacture_last_ts": *************,
     *             "b_icloud": 1,
     *             "b_icloud_last_ts": *************,
     *             "b_wx_code": 1,
     *             "b_wx_code_last_ts": *************,
     *             "b_sms_code": 1,
     *             "b_ sms_code _last_ts": *************,
     *             "b_low_osver": 1,
     *             "b_ low_osver_last_ts": *************,
     *             "b_remote_control_apps": 1,
     *             "b_remote_control_apps_last_ts": *************,
     *             "b_repackage": 1,
     *             "b_repackage_last_ts": *************,
     *             "b_reset": 1,
     *             "b_reset_last_ts": *************,
     *             "b_console": 1,
     *             "b_console_last_ts": *************,
     *             "b_alter_apps": 1,
     *             "b_alter_apps_last_ts": *************,
     *             "b_game_cheat_apps": 1,
     *             "b_game_cheat_apps_last_ts": *************,
     *             "b_headless": 1,
     *             "b_headless_last_ts": *************
     *         },
     *         "device_active_info": {
     *             "i_smid_boot_timestamp": *************
     *         },
     *         "monkey_device": {
     *             "common": {
     *                 "b_monkey_apps": 0,
     *                 "b_monkey_apps_last_ts": *************,
     *                 "b_webdriver": 0,
     *                 "b_webdriver_last_ts": *************
     *             },
     *             "monkey_game": {
     *                 "b_monkey_game_apps": 0,
     *                 "b_monkey_game_apps_last_ts": *************
     *             },
     *             "monkey_read": {
     *                 "b_monkey_read_apps": 0,
     *                 "b_monkey_read_apps_last_ts": *************
     *             }
     *         }
     *     },
     *     "tokenProfileLabels": [
     *         {
     *             "label1": "age_gender",
     *             "label2": "token_age",
     *             "label3": "minor_token",
     *             "description": "年龄性别:年龄:未成年人",
     *             "timestamp": 1634732525000,
     *             "detail": {}
     *         }
     *     ],
     *     "tokenRiskLabels": [
     *         {
     *             "label1": "risk_device_token",
     *             "label2": "b_cloud_token_device",
     *             "label3": "b_cloud_token_device",
     *             "description": "风险设备账号:云手机账号:云手机账号",
     *             "timestamp": 1634732525000,
     *             "detail": {}
     *         },
     *         {
     *             "label1": "risk_device_token",
     *             "label2": "b_hook_token",
     *             "label3": "b_hook_token",
     *             "description": "风险设备账号-hook 设备-hook 设备",
     *             "timestamp": 1634732525000,
     *             "detail": {}
     *         }
     *     ],
     *     "deviceRiskLabels": [
     *         {
     *             "label1": "monkey_device",
     *             "label2": "monkey_game",
     *             "label3": "b_monkey_game_apps",
     *             "description": "机器操控设备:游戏操控:安装",
     *             "timestamp": 1634732525000,
     *             "detail": {}
     *         }
     *     ],
     *     "phoneRiskLabels": [
     *         {
     *             "description": "接码平台手机号:接码平台手机号:接码平台手机号",
     *             "label1": "sms_platform_phone",
     *             "label2": "sms_platform_phone",
     *             "label3": "sms_platform_phone",
     *             "timestamp": null
     *         },
     *         {
     *             "description": "物联网卡手机号:物联网卡手机号:物联网卡手机号",
     *             "label1": "iot_simcard_phone",
     *             "label2": "iot_simcard_phone",
     *             "label3": "iot_simcard_phone",
     *             "timestamp": null
     *         },
     *         {
     *             "description": "虚拟运营商手机号:虚拟运营商手机号:虚拟运营商手机号",
     *             "label1": "mvno_simcard_phone",
     *             "label2": "mvno_simcard_phone",
     *             "label3": "mvno_simcard_phone",
     *             "timestamp": null
     *         },
     *         {
     *             "description": "黑产手机号:黑产手机号:黑产手机号",
     *             "label1": "black_record_phone",
     *             "label2": "black_record_phone",
     *             "label3": "black_record_phone",
     *             "timestamp": null
     *         }
     *     ]
     * }
     */
    private void saveRiskInfo(ShumeiRiskReq req,CommonHeaderDTO headerDTO, JSONObject riskObject, JSONObject eventObject) {
        ShumeiRiskInfo shumeiRiskInfo = new ShumeiRiskInfo();
        // 请求id
        shumeiRiskInfo.setRequestId(riskObject.getString("requestId"));
        shumeiRiskInfo.setEventRequestId(eventObject.getString("requestId"));
        shumeiRiskInfo.setDeviceId(headerDTO.getDeviceId());
        shumeiRiskInfo.setShumeiDeviceId(req.getShuMeiDeviceId());
        shumeiRiskInfo.setUserId(headerDTO.getUserId());
        shumeiRiskInfo.setAppId(Integer.valueOf(headerDTO.getAppId()));
        shumeiRiskInfo.setPkgId(Long.valueOf(headerDTO.getPkgId()));
        shumeiRiskInfo.setProduct(req.getProduct());
        shumeiRiskInfo.setOs(headerDTO.getOs());
        shumeiRiskInfo.setOsVersion(headerDTO.getOsVersion());
        shumeiRiskInfo.setAppVersion(headerDTO.getAppVersion());
        shumeiRiskInfo.setImei(headerDTO.getImei());
        shumeiRiskInfo.setOaid(headerDTO.getOaid());
        shumeiRiskInfo.setMac(headerDTO.getMac());
        shumeiRiskInfo.setAndroidId(headerDTO.getAndroidId());
        shumeiRiskInfo.setBrand(headerDTO.getBrand());
        shumeiRiskInfo.setRiskLevel(eventObject.getString("riskLevel"));
        shumeiRiskInfo.setEventType(req.getEventId());
        shumeiRiskInfo.setChannel(req.getChannel());

        // 事件详情
        if (null!=eventObject.getJSONObject("detail")){
            JSONObject object = eventObject.getJSONObject("detail");
            shumeiRiskInfo.setEventRiskDesc(object.getString("description"));
            shumeiRiskInfo.setModelHigh(object.getString("model"));
            shumeiRiskInfo.setHits(object.getJSONArray("hits").toJSONString());
        }

        /**
         * ip信息
         */
        shumeiRiskInfo.setIp(headerDTO.getIp());
        // ip信息 0-非  1-是
        if (riskObject.getJSONObject("ipLabels")!=null){
            JSONObject ipLabels = riskObject.getJSONObject("ipLabels");
            if (ipLabels.getJSONObject("risk_ip")!=null){
                JSONObject ipInfo = ipLabels.getJSONObject("risk_ip");
                shumeiRiskInfo.setRiskIp(ipInfo.getIntValue("risk_ip"));
            }
        }

        /**
         * 设备信息
         */
        if (riskObject.getJSONObject("deviceLabels")!=null){
            JSONObject deviceLabels = riskObject.getJSONObject("deviceLabels");
            if (deviceLabels.getJSONObject("fake_device")!=null) {
                JSONObject deviceInfo = deviceLabels.getJSONObject("fake_device");
                // 是否模拟设备  1-是 0-不是
                int pcEmulator = deviceInfo.getIntValue("b_pc_emulator");
                shumeiRiskInfo.setPcEmulator(pcEmulator);
                if (pcEmulator==1){
                    shumeiRiskInfo.setPcEmulatorLastTs(deviceInfo.getBigInteger("b_pc_emulator_last_ts"));
                }

                // 是否云端设备 0-非 1-是
                int cloudDevice = deviceInfo.getIntValue("b_cloud_device");
                shumeiRiskInfo.setCloudDevice(cloudDevice);
                if (cloudDevice==1){
                    shumeiRiskInfo.setCloudDeviceLastTs(deviceInfo.getBigInteger("b_cloud_device_last_ts"));
                }

                // 是否伪造设备 0-非 1-是
                int fakerDevice = deviceInfo.getIntValue("b_faker");
                shumeiRiskInfo.setFakerDevice(fakerDevice);
                if (fakerDevice==1){
                    shumeiRiskInfo.setFakerDeviceLastTs(deviceInfo.getBigInteger("b_faker_last_ts"));
                }

                // 是否篡改设备 0-非 1-是
                int alteredDevice = deviceInfo.getIntValue("b_altered");
                shumeiRiskInfo.setAlteredDevice(alteredDevice);
                if (alteredDevice==1){
                    shumeiRiskInfo.setAlteredDeviceLastTs(deviceInfo.getBigInteger("b_altered_last_ts"));
                }

                // 是否多开环境  0-非 1-是
                int multiBoxDevice = deviceInfo.getIntValue("b_multi_boxing");
                shumeiRiskInfo.setMultiBoxing(multiBoxDevice);
                if (multiBoxDevice==1){
                    shumeiRiskInfo.setMultiBoxingLastTs(deviceInfo.getBigInteger("b_multi_boxing_last_ts"));
                }

                // 是否农场设备  0-非 1-是
                int farmerDevice = deviceInfo.getIntValue("b_farmer");
                shumeiRiskInfo.setFarmerDevice(farmerDevice);
                if (farmerDevice==1){
                    shumeiRiskInfo.setFarmerDeviceLastTs(deviceInfo.getBigInteger("b_farmer_last_ts"));
                }

                // 是否积分墙设备  0-非 1-是
                int offerWallDevice = deviceInfo.getIntValue("b_offerwall");
                shumeiRiskInfo.setOfferwallDevice(offerWallDevice);
                if (offerWallDevice==1){
                    shumeiRiskInfo.setOfferwallDeviceLastTs(deviceInfo.getBigInteger("b_offerwall_last_ts"));
                }

                // 是否手机模拟器
                int phoneEmulator = deviceInfo.getIntValue("b_phone_emulator");
                shumeiRiskInfo.setPhoneEmulator(phoneEmulator);
                if (phoneEmulator==1){
                    shumeiRiskInfo.setPhoneEmulatorLastTs(deviceInfo.getBigInteger("b_phone_emulator_last_ts"));
                }
            }
        }

        /**
         * 风险标签信息
         */
        if (riskObject.getJSONObject("deviceRiskLabels")!=null) {
            JSONObject deviceRiskLabels = riskObject.getJSONObject("deviceRiskLabels");
            shumeiRiskInfo.setLabel1(deviceRiskLabels.getString("label1"));
            shumeiRiskInfo.setLabel2(deviceRiskLabels.getString("label2"));
            shumeiRiskInfo.setLabel3(deviceRiskLabels.getString("label3"));
            shumeiRiskInfo.setDescription(deviceRiskLabels.getString("description"));
            shumeiRiskInfo.setHitTime(deviceRiskLabels.getIntValue("timestamp"));
            shumeiRiskInfo.setHitDetail(deviceRiskLabels.getJSONObject("detail").toJSONString());

        }
        shumeiRiskInfo.setCreateTime(new Date());
        shumeiRiskInfo.setUpdateTime(new Date());
        log.info("shumeiRiskInfoDB result:{}",JSON.toJSONString(shumeiRiskInfo));
        // 表是否存在
        String dateStr = DateUtils.formatDateForYMDSTR((new Date()));
        isExits(tableName,dateStr);
        String table = tableName+"_"+dateStr;
        // 存入数据
        this.baseMapper.saveRiskInfo(shumeiRiskInfo,table);
    }




    /**
     * 按天分表 判断表是否存在
     */
    private static Map<String,Boolean> isEMap = new HashMap<>();
    private void isExits(String tableName, String dateStr){
        String tableByDay = tableName+"_"+dateStr;
        Boolean isE = isEMap.get(tableByDay);
        if(isE==null || !isE){
            isE = tableExtMap.existShuMeiTable(tableName,dateStr);
            if(!isE){
                tableExtMap.createShuMeiRiskInfoTable(tableName,dateStr);
            }
            isEMap.put(tableByDay,isE);
        }
    }


//    public static void main(String[] args) {
//
//
//        HashMap<String,Object> requestParam = new HashMap<>();
//        requestParam.put("accessKey",accessKey);
//
//        Map<String,String> map = new HashMap<>();
//        map.put("deviceId","33333");
//        map.put("ip","************");
//        String s = JSON.toJSONString(map);
//        JSONObject jsonObject = JSONObject.parseObject(s);
//        requestParam.put("data",jsonObject);
//        System.out.println(JSON.toJSONString(requestParam));
//
//
//        // 创建Httpclient对象
//        CloseableHttpClient httpClient = HttpClients.createDefault();
//        CloseableHttpResponse response = null;
//        String resultString = "";
//        try {
//            // 创建Http Post请求
//            HttpPost httpPost = new HttpPost(URL);
//            // 创建请求内容
//            StringEntity entity = new StringEntity(JSON.toJSONString(requestParam), ContentType.APPLICATION_JSON);
//            httpPost.setEntity(entity);
//
////            if (headers != null) {
////                headers.forEach(httpPost::setHeader);
////            }
//
//            // 执行http请求
//            response = httpClient.execute(httpPost);
//            resultString = EntityUtils.toString(response.getEntity(), "utf-8");
//        } catch (Exception e) {
//            log.error("post请求失败", e);
//        } finally {
//            if (response != null) {
//                try {
//                    response.close();
//                } catch (IOException e) {
//                    log.error("post连接关闭异常", e);
//                }
//            }
//        }
//
//        System.out.println(resultString);
//        JSONObject object = JSONObject.parseObject(resultString);
//
//        ShumeiRiskInfo shumeiRiskInfo = new ShumeiRiskInfo();
//        /**
//         * ip信息
//         */
//        shumeiRiskInfo.setIp("************");
//        // ip信息 0-非  1-是
//        if (!object.getJSONObject("ipLabels").isEmpty()){
//            JSONObject ipLabels = object.getJSONObject("ipLabels");
//            if (!ipLabels.getJSONObject("risk_ip").isEmpty()){
//                JSONObject ipInfo = ipLabels.getJSONObject("risk_ip");
//                shumeiRiskInfo.setRiskIp(ipInfo.getIntValue("risk_ip"));
//            }
//        }
//
//        /**
//         * 设备信息
//         */
//        if (!object.getJSONObject("deviceLabels").isEmpty()){
//            JSONObject deviceLabels = object.getJSONObject("deviceLabels");
//            if (!deviceLabels.getJSONObject("fake_device").isEmpty()) {
//                JSONObject deviceInfo = deviceLabels.getJSONObject("fake_device");
//                // 是否模拟设备  1-是 0-不是
//                int pcEmulator = deviceInfo.getIntValue("b_pc_emulator");
//                shumeiRiskInfo.setPcEmulator(pcEmulator);
//                if (pcEmulator==1){
//                    shumeiRiskInfo.setPcEmulatorLastTs(deviceInfo.getBigInteger("b_pc_emulator_last_ts"));
//                }
//
//                // 是否云端设备 0-非 1-是
//                int cloudDevice = deviceInfo.getIntValue("b_cloud_device");
//                shumeiRiskInfo.setCloudDevice(cloudDevice);
//                if (cloudDevice==1){
//                    shumeiRiskInfo.setCloudDeviceLastTs(deviceInfo.getBigInteger("b_cloud_device_last_ts"));
//                }
//
//                // 是否伪造设备 0-非 1-是
//                int fakerDevice = deviceInfo.getIntValue("b_faker");
//                shumeiRiskInfo.setFakerDevice(fakerDevice);
//                if (fakerDevice==1){
//                    shumeiRiskInfo.setFakerDeviceLastTs(deviceInfo.getBigInteger("b_faker_last_ts"));
//                }
//
//                // 是否篡改设备 0-非 1-是
//                int alteredDevice = deviceInfo.getIntValue("b_altered");
//                shumeiRiskInfo.setAlteredDevice(alteredDevice);
//                if (alteredDevice==1){
//                    shumeiRiskInfo.setAlteredDeviceLastTs(deviceInfo.getBigInteger("b_altered_last_ts"));
//                }
//
//                // 是否多开环境  0-非 1-是
//                int multiBoxDevice = deviceInfo.getIntValue("b_multi_boxing");
//                shumeiRiskInfo.setMultiBoxing(multiBoxDevice);
//                if (multiBoxDevice==1){
//                    shumeiRiskInfo.setMultiBoxingLastTs(deviceInfo.getBigInteger("b_multi_boxing_last_ts"));
//                }
//
//                // 是否农场设备  0-非 1-是
//                int farmerDevice = deviceInfo.getIntValue("b_farmer");
//                shumeiRiskInfo.setFarmerDevice(farmerDevice);
//                if (farmerDevice==1){
//                    shumeiRiskInfo.setFarmerDeviceLastTs(deviceInfo.getBigInteger("b_farmer_last_ts"));
//                }
//
//                // 是否积分墙设备  0-非 1-是
//                int offerWallDevice = deviceInfo.getIntValue("b_offerwall");
//                shumeiRiskInfo.setOfferwallDevice(offerWallDevice);
//                if (offerWallDevice==1){
//                    shumeiRiskInfo.setOfferwallDeviceLastTs(deviceInfo.getBigInteger("b_offerwall_last_ts"));
//                }
//
//                // 是否手机模拟器
//                int phoneEmulator = deviceInfo.getIntValue("b_phone_emulator");
//                shumeiRiskInfo.setPhoneEmulator(phoneEmulator);
//                if (phoneEmulator==1){
//                    shumeiRiskInfo.setPhoneEmulatorLastTs(deviceInfo.getBigInteger("b_phone_emulator_last_ts"));
//                }
//            }
//        }
//
//        /**
//         * 风险标签信息
//         */
//        if (object.getJSONObject("deviceRiskLabels")!=null) {
//            JSONObject deviceRiskLabels = jsonObject.getJSONObject("deviceRiskLabels");
//            shumeiRiskInfo.setLabel1(deviceRiskLabels.getString("label1"));
//            shumeiRiskInfo.setLabel2(deviceRiskLabels.getString("label2"));
//            shumeiRiskInfo.setLabel3(deviceRiskLabels.getString("label3"));
//            shumeiRiskInfo.setDescription(deviceRiskLabels.getString("description"));
//            shumeiRiskInfo.setHitTime(deviceRiskLabels.getIntValue("timestamp"));
//            shumeiRiskInfo.setHitDetail(deviceRiskLabels.getJSONObject("detail").toJSONString());
//
//        }
//        shumeiRiskInfo.setCreateTime(new Date());
//        shumeiRiskInfo.setUpdateTime(new Date());
//
//        System.out.println(JSON.toJSONString(shumeiRiskInfo));
//
//
//    }

//    public static void main(String[] args) {
//        String URL = "http://api-skynet-bj.fengkongcloud.com/v4/event";
//        // 请求参数
//        HashMap<String,Object> requestParam = new HashMap<>();
//        requestParam.put("accessKey",accessKey);
//        requestParam.put("appId","630");
//        requestParam.put("eventId","register");
//        requestParam.put("type","phoneOnePass");
//        requestParam.put("phone", Base64Encoder.encode("13166557755"));
//
//
//
//        HashMap<String,Object> data = new HashMap<>();
//        data.put("deviceId","20170930221706714c5fa240376ecf84abaaa359e1110ceea57164ede3db54");
//        data.put("ip","************");
//        data.put("tokenId","d64e481f93b158a7862a2c5571be9976");
//        data.put("isTokenSeperate",1);
//        data.put("timestamp",System.currentTimeMillis());
//        data.put("os","android");
//        data.put("appVersion","1.0.0");
//        requestParam.put("data",JSONObject.parseObject(JSON.toJSONString(data)));
//
//        String result = HttpClientService.doPostJson(URL, JSON.toJSONString(requestParam), null);
//        log.info("调用数美接口-返回 result:{}",result);
//        log.info("result:{}", JSONObject.parseObject(result));
//
//    }

}


