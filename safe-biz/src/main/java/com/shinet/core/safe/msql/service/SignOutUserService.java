package com.shinet.core.safe.msql.service;

import com.shinet.core.safe.msql.entity.AliUserDevice;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.enums.DeviceStatus;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@Slf4j
public class SignOutUserService {
    @Autowired
    UserAuthService userAuthService;
    @Autowired
    AliUserDeviceService aliUserDeviceService;
    public void startCleanUser(List<String> productList){
        List<AliUserDevice>  aliUserDeviceList = aliUserDeviceService.queryRiskDeivce(1000,productList);
        while (aliUserDeviceList.size()>0){
            for(AliUserDevice aliUserDevice : aliUserDeviceList){
                try {
                    CommonHeaderDTO commonHeaderDTO = CommonHeaderDTO.getCommHead(aliUserDevice);
                    boolean isDel = userAuthService.signOutUser(commonHeaderDTO);
                    if(isDel){
                        aliUserDevice.setDeviceStatus(DeviceStatus.DISABLE.value);
                        aliUserDeviceService.updateById(aliUserDevice);
                        String linfo = "拉黑用户 "+aliUserDevice.getUserId()+" id:"+aliUserDevice.getId()+" 成功";
                        log.info(linfo);
                        XxlJobLogger.log(linfo);
                    }else{
                        log.warn("失败拉黑用户 "+aliUserDevice.getUserId()+" id:"+aliUserDevice.getId()+" 失败");
                        XxlJobLogger.log("失败拉黑用户 "+aliUserDevice.getUserId()+" id:"+aliUserDevice.getId()+" 失败");
                    }
                }catch (Exception e){
                    log.error("",e);
                }
            }
            aliUserDeviceList = aliUserDeviceService.queryRiskDeivce(1000,productList);
        }
    }
}
