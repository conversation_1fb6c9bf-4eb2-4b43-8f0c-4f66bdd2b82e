package com.shinet.core.safe.msql.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shinet.core.safe.common.HttpClientService;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.util.CustomStringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.HeaderElement;
import org.apache.http.ParseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class UserAuthService {
    @Autowired
    HttpClientService httpClientService;

    // https://bp-api.shinet.cn/common/user/checkAuth
    private String authUrl = "http://bp-api.shinet.cn/bp/user/checkAuth";
    private String signOutUrl = "http://bp-api.shinet.cn/bp/user/cancelUser";
    public boolean checkAuth(CommonHeaderDTO commonHeaderDTO) {

        String[] pieces = StringUtils.splitByWholeSeparator(commonHeaderDTO.getAccessKey(), "_");

        if (pieces == null || pieces.length != 2) {
            return false;
        }

        if (CustomStringUtils.isNotPureNumber(pieces[1])) {
            return false;
        }

        String rspStr = httpClientService.doPost(authUrl,commonHeaderDTO.getMap(),commonHeaderDTO.getMap());
        JSONObject jsonObject = JSON.parseObject(rspStr);
        boolean isAck = jsonObject.getBoolean("result");
        log.info("rspStr  "+rspStr);
        return isAck;
    }

    public boolean signOutUser(CommonHeaderDTO commonHeaderDTO){
        String rspStr = httpClientService.doPost(signOutUrl,commonHeaderDTO.getMap(),commonHeaderDTO.getMap());
        JSONObject jsonObject = JSON.parseObject(rspStr);
        log.info("sirspStr  "+rspStr);
        return jsonObject.getString("message").equalsIgnoreCase("注销成功");
    }
}
