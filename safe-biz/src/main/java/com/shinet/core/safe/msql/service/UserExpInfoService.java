package com.shinet.core.safe.msql.service;

import com.shinet.core.safe.core.constants.DeviceRetryConstants;
import com.shinet.core.safe.msql.entity.UserExpInfo;
import com.shinet.core.safe.msql.entity.UserPkgs;
import com.shinet.core.safe.msql.mapper.UserExpInfoMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2024-07-10
*/
@Slf4j
@Service
public class UserExpInfoService extends ServiceImpl<UserExpInfoMapper, UserExpInfo> {
    @Autowired
    MysqlTableService mysqlTableService;

    public static String ustname = "user_exp_info";

    @Async(DeviceRetryConstants.DATA_PERSISTENCE_EXECUTOR)
    public void saveUseExps(UserExpInfo userExpInfo) {
        try {
            mysqlTableService.createIfNot(ustname);
            userExpInfo.setCreateTime(new Date());
            userExpInfo.setUpdateTime(new Date());
            save(userExpInfo);
        } catch (Exception e) {
            log.error("Error saving user exp info", e);
        }
    }
}
