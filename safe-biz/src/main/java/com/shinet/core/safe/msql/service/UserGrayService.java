package com.shinet.core.safe.msql.service;

import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.shinet.core.safe.common.HttpClientService;
import com.shinet.core.safe.constant.BaseConstants;
import com.shinet.core.safe.enums.ActiveChannel;
import com.shinet.core.safe.msql.entity.ByteUserDevice;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.util.HBaseUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.hadoop.hbase.client.Connection;
import org.apache.hadoop.hbase.util.Bytes;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2021/10/10
 */
@Slf4j
@Service
public class UserGrayService {


    @Autowired
    private HttpClientService httpClientService;
    /**
     * OCPC HBASE连接
     */
    @Resource
    private Connection ocpcHadoopConnection;

    /**
     * 用户HBase连接
     */
    @Resource
    private Connection hbaseConnection;


    @Value("#{${app.score.config}}")
    private Map<String,Integer> appScoreMap;

    @Value("${app.check.switch}")
    private boolean checkSwitch;

    @ApolloJsonValue("${hs.direct.gray.app.list:[662,637,696]}")
    private List<Integer> directGrayAppList;

    @Autowired
    private HBaseHsService hBaseHsService;

    @ApolloJsonValue("${hs.skip.gray.device.list:[\"0683d1291573b8c0\",\"4DDECCD9-4C74-46B4-9C70-36433A255978\"]}")
    private List<String> skipGrayDevice;


    /**
     * 归因key
     */
    private static final String ATTRIBUTE_KEY_FORMATTER ="ck:did:%s:%s:%s";

    @Getter
    @Setter
    private static class OcpcClick {
        /**
         * 渠道来源
         */
        private String dsp;
    }

    public void check(CommonHeaderDTO commonHeaderDTO, ByteUserDevice byteUserDevice){
        try {
            if (!checkSwitch || skipGrayDevice.contains(commonHeaderDTO.getDeviceId())){
                return;
            }
            Integer limitScore = appScoreMap.get(byteUserDevice.getAppId().toString());
            if (commonHeaderDTO.getIntOs() == 1 && limitScore == null){
                limitScore = 600;
            }
            if (limitScore == null){
                limitScore = 900;
            }

//            if (byteUserDevice.getScore() != null && byteUserDevice.getScore() >= 900){
//                if (commonHeaderDTO.getChannel().startsWith("tt")){
//                    log.info(">>> TT {} {} 900分异常用户 封禁处理...", byteUserDevice.getAppId(), byteUserDevice.getUserId());
//                    sendSignOut(byteUserDevice.getAppId().toString(), byteUserDevice.getUserId().toString(), commonHeaderDTO);
//                }
//                // 直接拉黑
//                if (directGrayAppList.contains(byteUserDevice.getAppId())){
//                    if (commonHeaderDTO.getChannel().contains("ttdr") || commonHeaderDTO.getChannel().contains("ksdr")) {
//                        log.info(">>> {} {} 900分异常用户 封禁处理...", byteUserDevice.getAppId(), byteUserDevice.getUserId());
//                        sendSignOut(byteUserDevice.getAppId().toString(), byteUserDevice.getUserId().toString(), commonHeaderDTO);
//                    }
//                }
//            }

            if (byteUserDevice.getScore() != null && byteUserDevice.getScore() >= limitScore) {
                log.info(">>>> 检测到用户[{}-{}]分值大于{},进行检测...",byteUserDevice.getAppId(),byteUserDevice.getUserId(),limitScore);
                if (!isTfUser(byteUserDevice.getProduct(),byteUserDevice.getUserId())) {
                    log.info(">>> 确认[{}-{}]为非投放用户 封禁处理...",byteUserDevice.getAppId(),byteUserDevice.getUserId());
                    sendSignOut(byteUserDevice.getAppId().toString(),byteUserDevice.getUserId().toString(),commonHeaderDTO);
                }
            }
            if (byteUserDevice.getScore() != null && byteUserDevice.getScore() >= 300){
//                log.info(">>> {} 用户 {} ,记录",byteUserDevice.getAppId(),byteUserDevice.getUserId());
                hBaseHsService.saveHsExUser(byteUserDevice);
            }
        }catch (Exception e){
            log.error("GrayUser Error:",e);
        }

    }

    private void sendSignOut(String appId, String userId, CommonHeaderDTO commonHeaderDTO){
        String url ="https://bp-api.shinet.cn/bp/user/signOutUser";
        HashMap<String,String> param = new HashMap<>();
        param.put("accessKey",commonHeaderDTO.getAccessKey());
        param.put("appId",appId);
        param.put("reason","VolcanicScoreAnomaly");
        String result = httpClientService.doPost(url,param,commonHeaderDTO.getMap());
        if (Strings.isNotBlank(result)){
            JSONObject object = JSONObject.parseObject(result);
            if (!Integer.valueOf(0).equals(object.getInteger("code"))){
                log.error(">>>> 拉黑[{}-{}] 异常...",appId,userId);
            }else {
                log.info(">>>> 拉黑[{}-{}] 成功...",appId,userId);
            }
        }
    }

    private void grayUser(String appId,String userId,CommonHeaderDTO commonHeaderDTO){
        String url ="https://bp-api.shinet.cn/bp/user/grayUser";
        HashMap<String,String> param = new HashMap<>();
        param.put("accessKey",userId);
        param.put("appId",appId);
        String result = httpClientService.doPost(url,param,commonHeaderDTO.getMap());
        if (Strings.isNotBlank(result)){
            JSONObject object = JSONObject.parseObject(result);
            if (!Integer.valueOf(0).equals(object.getInteger("code"))){
                log.error(">>>> 拉灰[{}-{}] 异常...",appId,userId);
            }else {
                log.info(">>>> 拉灰[{}-{}] 成功...",appId,userId);
            }
        }
    }

    /**
     * click事件表名
     */
    private static final String CLICK_TABLE_NAME = "ToutiaoClick";

    private static final String UA_KEY = "%s@%s";

    private static final String USER_ACTIVE = "userAcitveByUser";


    public boolean isTfUser(String product,Long realUserId){
        try {
            String key = String.format(UA_KEY,realUserId,product);
            byte[] result = HBaseUtils.searchDataFromHadoop(ocpcHadoopConnection, USER_ACTIVE, key);
            if (result != null) {
                UserActive userActive = JSONObject.parseObject(result, UserActive.class);
                ActiveChannel activeChannel = ActiveChannel.getByDesc(userActive.getSource());
                return ActiveChannel.isOCPCChanel(activeChannel);
            }
        }catch (Exception e){
            log.error("查询异常:",e);
        }
        log.info("UerActive未查询到..跳过");
        return true;
    }

    @Getter
    @Setter
    private static class UserActive{
        private String os;
        private String source;
    }



    /**
     * 用户检查投放来源
     * @param name productEnName
     * @param os 系统
     * @param deviceId 设备id
     * @param appId appId
     * @param realUserId 真实的用户id
     * @return 是否投放用户 false-投放用户 true-非投放用户
     */
    public boolean shouldShieldUser(String name, String os, String deviceId, Integer appId, Long realUserId) {
        try {

            if (!isOcpcByUserId(appId, realUserId)) {
                return true;
            }

            if (name == null){
                return false;
            }
            if (org.apache.commons.lang3.StringUtils.isBlank(deviceId) || "0".equals(deviceId) || "0000-0000-0000-0000".equals(deviceId)) {
                return false;
            }
            String hbaseKey =  String.format(ATTRIBUTE_KEY_FORMATTER, name, os, deviceId);
            Pair<Boolean, byte[]> result = HBaseUtils.searchDataFromHadoopWithoutHash(ocpcHadoopConnection, CLICK_TABLE_NAME, hbaseKey);

            if (result.getRight() == null) {
                return false;
            }

            OcpcClick click = JSONObject.parseObject(result.getRight(), OcpcClick.class);
            String dsp = click.dsp;
            boolean isOcpc = "toutiao".equals(dsp) || "kuaishou".equals(dsp) || "guangdiantong".equals(dsp);
            return !isOcpc;
        }catch (Exception e){
            log.error("查询用户投放/非投放检查异常...", e);
            return false;
        }
    }

    /**
     * 用户渠道信息表前缀
     */
    private static final String USER_CHANNEL_TABLE_PREFIX = "user_channel_";

    private boolean isOcpcByUserId(Integer appId, Long realUserId) {

        if (realUserId == null) {
            return true;
        }

        String activeChannelTableName = USER_CHANNEL_TABLE_PREFIX + appId;
        byte[] result = HBaseUtils.searchDataFromHadoop(hbaseConnection, activeChannelTableName, realUserId.toString());
        if (result == null) {
            return true;
        }

        String content = Bytes.toString(result);
        String channel = org.apache.commons.lang3.StringUtils.substringBefore(content, BaseConstants.COMMA);
        return "toutiao".equals(channel) || "kuaishou".equals(channel) || "guangdiantong".equals(channel);
    }
}
