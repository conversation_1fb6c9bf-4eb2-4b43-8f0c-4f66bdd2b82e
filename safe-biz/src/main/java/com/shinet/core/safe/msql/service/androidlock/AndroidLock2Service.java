package com.shinet.core.safe.msql.service.androidlock;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.net.NetUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Sets;
import com.shinet.core.safe.container.AppBuilder;
import com.shinet.core.safe.dto.BasicApp;
import com.shinet.core.safe.dto.LockKeyResult;
import com.shinet.core.safe.enums.StoreNameEnums;
import com.shinet.core.safe.lock.service.LockHbaseService;
import com.shinet.core.safe.msql.config.SafeSwitcher;
import com.shinet.core.safe.msql.entity.AndroidLockRst;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.service.LockAreaConfigService;
import com.shinet.core.safe.msql.service.DeviceBlackService;
import com.shinet.core.safe.core.vo.CommonHeaderVo;
import com.shinet.core.safe.msql.service.ioslock.IpAgtService;
import com.shinet.core.safe.msql.service.ioslock.IpAliYunService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
public class AndroidLock2Service {
    @Autowired
    LockAreaConfigService lockAreaConfigService;
    @Autowired
    AndroidLockConfService androidLockConfService;
    @Autowired
    AndroidLockRstService androidLockRstService;
    @Autowired
    AndroidLockBlacksService androidLockBlacksServicel;
    @Autowired
    LockHbaseService lockHbaseService;
    @Autowired
    SafeSwitcher safeSwitcher;
    @Resource(name = "stringRedisTemplate2")
    private StringRedisTemplate stringRedisTemplate2;
    @Resource
    private OcpcLockService ocpcLockService;
    @ApolloJsonValue("${lock.ip.white.map:{\"742:2\":[\"************\"]}}")
    private Map<String, List<String>> ipWhiteMap;
    @Autowired
    IpAgtService ipAgtService;
    @Autowired
    DeviceBlackService deviceBlackService;
    @ApolloJsonValue("${lock.store.product.map}")
    private Map<String, List<String>> lockStoreProductMap;

    @Value("${lock.store.product.log.flag:false}")
    public boolean lockStoreProductLogFlag;

    @ApolloJsonValue("${lock.store.offloading.product.map}")
    private Map<String, List<String>> storeOffLoadingProductMap;

    @ApolloJsonValue("${lock.ip.list}")
    private List<String> lockIpList;
    @ApolloJsonValue("${lock.use.ocpc2.product.list}")
    private List<String> useOcpc2ProductList;
    @ApolloJsonValue("${lock.white.oaid.list}")
    private List<String> whiteOaidList;
    @ApolloJsonValue("${lock.white.ip.list}")
    private List<String> lockWhiteIpList;
    @ApolloJsonValue("${lock.oppo.noLock2.list}")
    private List<String> oppoNoLock2List;
    @ApolloJsonValue("${lock.channel.city.black.map}")
    private Map<String, Map<String, String>> channelCityBlackMap;
    @ApolloJsonValue("${lock.ziran.noLock2.product.list:[]}")
    private List<String> ziranNoLock2ProductList;
    @ApolloJsonValue("${lock.ziran.noLock2.group.list:[]}")
    private List<String> ziranNoLock2GroupList;
    @ApolloJsonValue("${lock.no.ziran.product.list:[]}")
    private List<String> noLockZiranProductList;
    @Value("${enable.tf.skip.lock2:false}")
    private boolean enableSkipLock2;

    private static final Pattern IPV4_PATTERN = Pattern.compile(
            "^([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\." +
                    "([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\." +
                    "([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\." +
                    "([01]?\\d\\d?|2[0-4]\\d|25[0-5])$"
    );

    private void setCheader(CommonHeaderDTO commonHeaderDTO, AndroidLokReq androidLokReq, String ip){
        String product = androidLokReq.getProduct();
        commonHeaderDTO.setIp(ip);
        commonHeaderDTO.setProduct(product);
        commonHeaderDTO.setOs("android");
        String  oaid = androidLokReq.getOaid();
        commonHeaderDTO.setOaid(oaid);
        commonHeaderDTO.setIp(ip);
        commonHeaderDTO.setDeviceId(androidLokReq.getImei());
        commonHeaderDTO.setAndroidId(androidLokReq.getAndroidId());
        commonHeaderDTO.setUa(androidLokReq.getUa());
        commonHeaderDTO.setModel(androidLokReq.getModel());
    }
    public Pair<LockKeyResult, AndroidLockRst> isAdLock(CommonHeaderDTO commonHeaderDTO, AndroidLokReq androidLokReq, String ip){
        String product = androidLokReq.getProduct();
        setCheader(commonHeaderDTO,androidLokReq,ip);
        LockKeyResult lockKeyResult = new LockKeyResult();
        lockKeyResult.setLocked(Boolean.FALSE);
        lockKeyResult.setOcpc(Boolean.FALSE);
        String  oaid = androidLokReq.getOaid();

        String channel = androidLokReq.getChannel();
        StoreNameEnums storeNameEnums = getStoreName(product,channel);
        AndroidLockRst androidLockRst = getAndroidLockRst(storeNameEnums);
        androidLockRst.setIp(ip);
        androidLockRst.setStoreName(storeNameEnums.val);


        boolean isOcpc = false;
        boolean isNewUser = false;
        // 为空或者配置的产品走ocpc2接口
        boolean useOcpc2ApiFlag = CollUtil.isEmpty(useOcpc2ProductList) || useOcpc2ProductList.contains(product);
        Pair<OcpcRsp,Boolean>  isOcpcPair = ocpcLockService.isOcpcUser(commonHeaderDTO,androidLokReq,storeNameEnums, useOcpc2ApiFlag);

        isOcpc = isOcpcPair.getValue();
        OcpcRsp ocpcRsp = isOcpcPair.getKey();
        lockKeyResult.setOcpcRsp(ocpcRsp);
        if(isOcpc){
            androidLockRst.setIsOcpc(1);
        }else{
            androidLockRst.setIsOcpc(0);
        }
        lockKeyResult.setOcpc(isOcpc);
        if (ObjectUtil.isNotEmpty(ocpcRsp.isNewUser())) {
            isNewUser = ocpcRsp.isNewUser();
        }

        if (lockWhiteIpList.contains(ip)) {
            return checkLock(product, ip, androidLokReq, storeNameEnums, lockKeyResult, androidLockRst);
        }

        if (whiteOaidList.contains(oaid)) {
            log.info("oaid白名单,不锁定 {} {}", storeNameEnums.val, oaid);
            lockKeyResult.setLocked(false);
            androidLockRst.setRemark("oaid白名单,不锁定");
            return new Pair<>(lockKeyResult, androidLockRst);
        }

        if (isStoreOffLoadingProduct(product, storeNameEnums.val)) {
            log.info("归属商店下架产品名单,不锁定 {} {}", storeNameEnums.val, JSON.toJSONString(androidLockRst));
            lockKeyResult.setLocked(false);
            androidLockRst.setRemark("归属商店下架产品名单,不锁定");
            return new Pair<>(lockKeyResult, androidLockRst);
        }

        String queryKey = buildKey(commonHeaderDTO.getAppId(), commonHeaderDTO.getIntOs() + 1);
        if (ipWhiteMap.getOrDefault(queryKey, new ArrayList<>()).contains(commonHeaderDTO.getIp()) && !safeSwitcher.lockPrjs.contains(product)) {
            log.info("{} 白名单ip命中...", JSON.toJSONString(commonHeaderDTO));
            lockKeyResult.setLocked(false);
            androidLockRst.setRemark("白名单ip命中 " + ip);
            return new Pair<>(lockKeyResult, androidLockRst);
        }

        Pair<Boolean,String> isLockByChannelPair = androidLockConfService.isLockByChannelVersion(
                product,androidLokReq.getChannel(),androidLokReq.getAppVersion(),isOcpc,storeNameEnums);
        if(isLockByChannelPair.getKey()){
            lockKeyResult.setLocked(true);
            androidLockRst.setRemark(isLockByChannelPair.getValue());
            return new Pair<>(lockKeyResult,androidLockRst);
        }

        if (enableSkipLock2 && ocpcLockService.isTfChannel(storeNameEnums.channel)) {
            if (safeSwitcher.logFlag) {
                log.info("投放包跳过二道锁规则 header：{} param: {}", JSON.toJSONString(commonHeaderDTO), JSON.toJSONString(androidLokReq));
            }
            lockKeyResult.setLocked(false);
            androidLockRst.setRemark("投放包跳过二道锁规则");
            return new Pair<>(lockKeyResult, androidLockRst);
        }

        // 设备拉黑统一检测
        try {
            CommonHeaderVo headerVo = new CommonHeaderVo()
                    .setBasicInfo(commonHeaderDTO.getOs(), ip, product);
            headerVo.setOaid(commonHeaderDTO.getOaid());

            if (deviceBlackService.isDeviceBlocked(headerVo)) {
                lockKeyResult.setLocked(true);
                androidLockRst.setRemark("设备拉黑检测命中");
                log.info("Android设备拉黑检测命中：product={}, ip={}, oaid={}",
                        product, commonHeaderDTO.getIp(), commonHeaderDTO.getOaid());
                return new Pair<>(lockKeyResult, androidLockRst);
            }
        } catch (Exception e) {
            log.warn("Android设备拉黑检测异常，继续后续流程：product={}, error={}",
                    product, e.getMessage(), e);
        }


//        if ((StringUtils.isNotBlank(commonHeaderDTO.getDeviceId()) && StringUtils.isNotBlank(stringRedisTemplate2.opsForValue().get(SCREEN_VIDEO_BLACKLIST + commonHeaderDTO.getDeviceId())))
//                || (StringUtils.isNotBlank(oaid) && StringUtils.isNotBlank(stringRedisTemplate2.opsForValue().get(SCREEN_VIDEO_BLACKLIST + oaid)))) {
//            log.info("禁止录屏黑名单的device_id:{} ", commonHeaderDTO.getDeviceId());
//            log.info("禁止录屏黑名单的oaId:{} ", oaid);
//            lockKeyResult.setLocked(true);
//            androidLockRst.setRemark("禁止录屏的黑名单命中的device_id:" + commonHeaderDTO.getDeviceId());
//            return new Pair<>(lockKeyResult, androidLockRst);
//        }


        // 非ocpc自然量用户 拉黑
        if (isUserBlackByStoreProductAll(isNewUser, isOcpc, product, storeNameEnums)) {

//            if (specialHandleOppo(product, storeNameEnums, androidLokReq)) {
//                log.info("{} oppo特殊处理自然量拉黑,未开全锁，二道锁放行", JSON.toJSONString(commonHeaderDTO));
//                lockKeyResult.setLocked(false);
//                androidLockRst.setRemark("oppo特殊处理自然量拉黑,未开全锁,二道锁放行");
//                return new Pair<>(lockKeyResult, androidLockRst);
//            }

            lockKeyResult.setLocked(true);
            androidLockRst.setRemark("命中非ocpc自然量用户规则 " + product + " " + storeNameEnums.val + " " + isOcpc);
            log.info("命中非ocpc自然量用户规则 {} {} {}", product, storeNameEnums.val, isOcpc);
            return new Pair<>(lockKeyResult,androidLockRst);
        }

        Integer scRecord = androidLokReq.getScRecord();
        if(scRecord != null && scRecord > 0){
            log.info(" 开启录屏软件 {}", JSON.toJSONString(androidLockRst));
        }

        if(androidLokReq.getIsVp()
                && (
                storeNameEnums.equals(StoreNameEnums.honor)
                        || storeNameEnums.equals(StoreNameEnums.huawei)
                        || storeNameEnums.equals(StoreNameEnums.vivo)
                        || storeNameEnums.equals(StoreNameEnums.xiaomi)
                )
        ){
            log.info(" 开启vpn {}", JSON.toJSONString(androidLockRst));
            lockKeyResult.setLocked(true);
            androidLockRst.setRemark("开启vpn" );
            return new Pair<>(lockKeyResult,androidLockRst);
        }

        if(pkgLc(androidLokReq,storeNameEnums)){
            log.info(" 严格渠道无获取pkgs {}", JSON.toJSONString(androidLockRst));
            lockKeyResult.setLocked(true);
            androidLockRst.setRemark("严格渠道无获取pkgs" );
            return new Pair<>(lockKeyResult,androidLockRst);
        }

        AndroidLockRst androidLockRecord = androidLockRstService.queryByOaid(product, oaid, channel);
        if(androidLockRecord!=null){
            if("true".equalsIgnoreCase(androidLockRecord.getLockFlag())){
                lockKeyResult.setLocked(true);
            }else{
                lockKeyResult.setLocked(false);
            }
            androidLockRst.setRemark("获取到存储信息，以此判断" + lockKeyResult.getLocked());
            log.info(commonHeaderDTO.getProduct()+" "+commonHeaderDTO.getOaid()+" android获取到存储信息，以此判断 "+lockKeyResult.getLocked());
            return new Pair<>(lockKeyResult,androidLockRst);
        }

        String city = "";

        String dsp = "nodta";
        try{
            Pair<String,String>  cityd = IpAliYunService.getIpAddress(
                    commonHeaderDTO.getProduct(),commonHeaderDTO.getOaid(),commonHeaderDTO.getIp(),channel);
            androidLockRst.setCity(cityd.getValue());

            if(StringUtils.isNotBlank( ocpcRsp.getDsp())){
                dsp = ocpcRsp.getDsp();
            }

            if(StringUtils.contains(cityd.getKey(),"华为")
                    && StoreNameEnums.huawei.equals(storeNameEnums)){
                log.info("华为运营商 "+product+" "+ip+" "+channel);
                lockKeyResult.setLocked(true);
                androidLockRst.setRemark("华为运营商 "+product+" "+ip+" "+channel);
                return new Pair<>(lockKeyResult,androidLockRst);
            }
            boolean isYange =  ocpcRsp.isNewUser() &&
                             (!ocpcRsp.isOcpc() ||  (ocpcRsp.isOcpc() && ocpcRsp.getCurMins()>10));
            if(StringUtils.containsAny(cityd.getValue(),"南京","西安") &&
                    StoreNameEnums.honor.equals(storeNameEnums) &&
                    isYange
            ){
                log.info("荣耀南京西安10min "+product+" "+ip+" "+channel +" "+ocpcRsp.getCurMins());
                lockKeyResult.setLocked(true);
                androidLockRst.setRemark("V2 荣耀南京西安10min "+product+" "+ip+" "+channel+" "+ocpcRsp.getCurHour()+" "+ocpcRsp.getCurMins());
                return new Pair<>(lockKeyResult,androidLockRst);
            }


            if(StringUtils.containsAny(cityd.getValue(),"南京","杭州","深圳") &&
                    StoreNameEnums.vivo.equals(storeNameEnums)&&
                    isYange
            ){
                log.info("vivo南京杭州深圳10min "+product+" "+ip+" "+channel);
                lockKeyResult.setLocked(true);
                androidLockRst.setRemark("V2 vivo南京杭州深圳10min "+product+" "+ip+" "+channel+" "+ocpcRsp.getCurMins());
                return new Pair<>(lockKeyResult,androidLockRst);
            }

            //北京,南京,杭州,深圳,成都
            /**
             * boolean isYange =  ocpcRsp.isNewUser() &&  (!ocpcRsp.isOcpc() ||  (ocpcRsp.isOcpc() && ocpcRsp.getCurMins()>10));
             */
            boolean isHwLoc =  ocpcRsp.isNewUser() &&  !ocpcRsp.isOcpc();
            if(StringUtils.containsAny(cityd.getValue(),"北京","南京","杭州","深圳","成都") &&
                    StoreNameEnums.huawei.equals(storeNameEnums)&&
                    isYange
            ){
                log.info("华为北京,南京,杭州,深圳,成都10min "+product+" "+ip+" "+channel);
                lockKeyResult.setLocked(true);
                androidLockRst.setRemark("V2 华为北京,南京,杭州,深圳,成都10min "+product+" "+ip+" "+channel+" "+ocpcRsp.getCurMins());
                return new Pair<>(lockKeyResult,androidLockRst);
            }
            if(city ==null || StringUtils.isBlank(cityd.getValue())){
                city = lockAreaConfigService.getCity(commonHeaderDTO,"android");
            }else{
                city = cityd.getValue();
            }

        }catch (Exception e){
            log.warn("获取城市错误 ",e);
            city = lockAreaConfigService.getCity(commonHeaderDTO,"android");
        }

        //某渠道下某城市产品全部拉黑
        if (Objects.nonNull(channelCityBlackMap.get(channel)) && channelCityBlackMap.get(channel).containsKey(city)) {
            log.info("产品{}匹配的渠道和城市组合需要被拉黑：{} + {}", product, storeNameEnums.val, city);
            lockKeyResult.setLocked(true);
            androidLockRst.setRemark("命中渠道和城市组合规则");
            return new Pair<>(lockKeyResult, androidLockRst);
        }


        //商店锁
        Pair<Boolean,String> isLockByStorePair = androidLockConfService.isLockByStore(
                product, androidLokReq.getChannel(),androidLokReq.getAppVersion(),
                isOcpc,storeNameEnums,oaid);

        if(isLockByStorePair.getKey()){
            lockKeyResult.setLocked(true);
            androidLockRst.setRemark(isLockByStorePair.getValue());
            return new Pair<>(lockKeyResult,androidLockRst);
        }

        androidLockRst.setCity(city);
        androidLockRst.setDsp(dsp);
        lockKeyResult.setCity(city);


        //渠道 锁ip 包  非ocpc 城市
        Pair<Boolean,String> isLockPair = androidLockBlacksServicel.isLockByBlck(
                androidLokReq.getProduct(),
                storeNameEnums,
                ip,
                oaid,
                androidLokReq.getPkgs(),
                androidLokReq.getLcPkgs(),
                isOcpc,
                city,
                androidLokReq.getUserId());


        if(isLockPair.getKey()){
            lockKeyResult.setLocked(true);
            androidLockRst.setRemark(isLockPair.getValue());
            return new Pair<>(lockKeyResult,androidLockRst);
        }

        if (isLockIp(ip)) {
            lockKeyResult.setLocked(true);
            androidLockRst.setRemark("配置ip锁");
            return new Pair<>(lockKeyResult,androidLockRst);
        }

        //判断是否在锁区渠道
        return new Pair<>(lockKeyResult,androidLockRst);
    }

    /**
     * 白名单ip下-检测是否在锁区渠道
     *
     * @param lockRst
     * @param product
     * @param ip
     * @param androidLokReq
     * @param storeNameEnums
     * @param lockKeyResult
     */
    private Pair<LockKeyResult, AndroidLockRst> checkLock(String product, String ip, AndroidLokReq androidLokReq, StoreNameEnums storeNameEnums, LockKeyResult lockKeyResult, AndroidLockRst androidLockRst) {
        // 一道锁判断
        Pair<Boolean, String> isLockByChannelPair = new Pair<Boolean, String>(false, "命中白名单ip");
        if (StoreNameEnums.huawei.equals(storeNameEnums) ||
                StoreNameEnums.xiaomi.equals(storeNameEnums) ||
                StoreNameEnums.vivo.equals(storeNameEnums) ||
                StoreNameEnums.oppo.equals(storeNameEnums) ||
                StoreNameEnums.honor.equals(storeNameEnums)
        ) {
            isLockByChannelPair = androidLockConfService.isLockByChannelVersion(
                    product, androidLokReq.getChannel(), androidLokReq.getAppVersion(), false, storeNameEnums);
        }
        if (isLockByChannelPair.getKey()) {
            lockKeyResult.setLocked(true);
            androidLockRst.setRemark("ip白名单命中,一道锁锁住，继续锁定 " + ip);
            return new Pair<>(lockKeyResult, androidLockRst);
        }

        String oaid = androidLokReq.getOaid();
        String channel = androidLokReq.getChannel();

        if (StringUtils.isNotBlank(oaid) && StringUtils.isNotBlank(channel)) {
            AndroidLockRst androidLockRecord = androidLockRstService.queryByOaid(product, oaid, channel);
            if (androidLockRecord != null) {
                lockKeyResult.setLocked("true".equalsIgnoreCase(androidLockRecord.getLockFlag()));
                androidLockRst.setRemark("ip白名单命中-获取到存储信息，以此判断" + lockKeyResult.getLocked());
                return new Pair<>(lockKeyResult, androidLockRst);
            }
        }


        lockKeyResult.setLocked(false);
        androidLockRst.setRemark("白名单ip命中-未获取到存储信息,不锁定 " + ip);

        return new Pair<>(lockKeyResult, androidLockRst);

    }

    /**
     * 特殊处理oppo
     * 一道锁不锁的情况下，
     * 如果该产品在配置中,则该产品走二道锁逻辑,如果不在，则不走二道锁
     * 如果配置的产品为空时，则所有产品不走二道锁逻辑，返回非锁状态
     * @param product
     * @param storeNameEnums
     * @param androidLokReq
     * @return
     */
    private boolean specialHandleOppo(String product, StoreNameEnums storeNameEnums, AndroidLokReq androidLokReq) {
        // 只针对oppo
        if (!StoreNameEnums.oppo.equals(storeNameEnums)) {
            return false;
        }
        Pair<Boolean, String> isLockByChannelPair = androidLockConfService.isLockByChannelVersion(
                product, androidLokReq.getChannel(), androidLokReq.getAppVersion(), false, storeNameEnums);
        if (!isLockByChannelPair.getKey()) {

            if (CollUtil.isEmpty(oppoNoLock2List)) return true;

            if (oppoNoLock2List.contains(product)) return false;
//            return CollUtil.isEmpty(oppoNoLock2List) || oppoNoLock2List.contains(product);
        }
        return false;
    }

    /**
     * 判断是否是配置的商店下架应用
     * @param product
     * @param storeName
     * @return
     */
    private boolean isStoreOffLoadingProduct(String product, String storeName) {
        if (CollUtil.isEmpty(storeOffLoadingProductMap)) return false;

        return storeOffLoadingProductMap.getOrDefault(storeName, new ArrayList<>()).contains(product);
    }

    private boolean isLockIp(String ip) {
        try {
            if (CollUtil.isEmpty(lockIpList)) return false;

            for (String pattern : lockIpList) {
                if (isIpMatched(ip, pattern)) {
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("lock2 ip match error", e);
        }

        return false;
    }

    private static boolean isIpMatched(String inputIp, String pattern) {
        if (isIPv4(pattern)) {
            // 精确匹配
            return inputIp.equals(pattern);
        } else if (pattern.contains("*")) {
            // 通配符匹配
            String[] patternParts = pattern.split("\\.");
            String[] inputParts = inputIp.split("\\.");

            for (int i = 0; i < 4; i++) {
                if (!"*".equals(patternParts[i]) && !patternParts[i].equals(inputParts[i])) {
                    return false;
                }
            }

            return true;
        } else {
            // CIDR匹配
            return NetUtil.isInRange(inputIp, pattern);
        }
    }

    private static boolean isIPv4(String ip) {
        Matcher matcher = IPV4_PATTERN.matcher(ip);
        return matcher.matches();
    }

    /**
     * 判断是否在拉黑名单 渠道-产品 非ocpc
     * @param isOcpc
     * @param storeNameEnums
     * @param product
     * @return
     */
    private boolean isUserBlackByStoreProductConfig(boolean isNewUser, boolean isOcpc, StoreNameEnums storeNameEnums, String product) {

        if (lockStoreProductLogFlag) {
            log.info("拉黑渠道-产品-非ocpc {} {} {} {}", isOcpc, storeNameEnums.val, product, lockStoreProductMap);
        }
        if (!isNewUser) return false;

        if (isOcpc) return false;

        List<String> configProductList = lockStoreProductMap.getOrDefault(storeNameEnums.val, Collections.emptyList());

        return configProductList.contains(product);
    }


    /**
     * 判断是否在拉黑名单 非ocpc 自然量全部拉黑
     * @param isOcpc
     * @param isNewUser
     * @return
     */
    private boolean isUserBlackByStoreProductAll(boolean isNewUser, boolean isOcpc, String product, StoreNameEnums storeNameEnums) {

        BasicApp app = AppBuilder.getByProduct(product);

        if (StoreNameEnums.oppo.equals(storeNameEnums)
                && (ziranNoLock2ProductList.contains(product) || ziranNoLock2GroupList.contains(app.getProductGroup()))) {
            return false;
        }

        if (noLockZiranProductList.contains(product)) return false;

        if (!isNewUser || isOcpc) return false;

        return true;
    }

    /**
     * 严格渠道需要拉到包
     * @param androidLokReq
     * @param storeNameEnums
     * @return
     */
    public boolean pkgLc(AndroidLokReq androidLokReq,StoreNameEnums storeNameEnums){
        if(StoreNameEnums.vivo.equals(storeNameEnums) ||
                StoreNameEnums.huawei.equals(storeNameEnums) ||
                StoreNameEnums.honor.equals(storeNameEnums)){
            if(androidLokReq.isPkgSw){
                if(StringUtils.isBlank(androidLokReq.getPkgs())){
                    return true;
                }
            }
        }
        return false;
    }


    public boolean calIsShowNotify(LockKeyResult lockKeyResult,AndroidLockRst androidLockRst,CommonHeaderDTO commonHeaderDTO){
        try {
            if(safeSwitcher.notyDeviceSet.contains(commonHeaderDTO.getOaid()) ||
                    safeSwitcher.notyDeviceSet.contains(commonHeaderDTO.getAndroidId()) ||
                    safeSwitcher.notyDeviceSet.contains(commonHeaderDTO.getImei()) ){
                androidLockRst.setRemark2("白名单设备");
                return true;
            }
            if(lockKeyResult.getLocked()){
                androidLockRst.setRemark2("锁区不展示");
                return false;
            }else{
                boolean isC =  DeviceLcService.imeiset.contains(commonHeaderDTO.getImei()) || DeviceLcService.lset.contains(commonHeaderDTO.getOaid());
                if(isC){
                    log.info("用户直接锁命中 icC "+commonHeaderDTO.getUserId()+" "+commonHeaderDTO.getProduct()+" "+lockKeyResult.getCity()+ " "+lockKeyResult.getOcpc());
                  //  lockKeyResult.setLocked(true);
                    androidLockRst.setRemark2("用户直接锁命中 "+commonHeaderDTO.getUserId()+" "+commonHeaderDTO.getProduct()+
                            " "+lockKeyResult.getOcpc() +
                            " oaid-"+DeviceLcService.lset.contains(commonHeaderDTO.getOaid())+
                            " imei-"+DeviceLcService.imeiset.contains(commonHeaderDTO.getImei()));
                    return false;
                }else if (!lockKeyResult.getOcpc() && Sets.newHashSet(new String[]{"北京市"}).contains(lockKeyResult.getCity())){
                    androidLockRst.setRemark2("非ocpc 锁 "+lockKeyResult.getCity());
                    return false;
                }
            }
        }catch (Exception e){
            log.error("计算通知出错 ",e);
            return false;
        }
        return true;
    }

    private AndroidLockRst getAndroidLockRst(StoreNameEnums storeNameEnums){
        AndroidLockRst androidLockRst = new AndroidLockRst();
        androidLockRst.setStoreName(storeNameEnums.val);
        return androidLockRst;
    }

    public static boolean isStoreChannel(StoreNameEnums source) {
        return StoreNameEnums.vivo.equals(source)
                || StoreNameEnums.oppo.equals(source)
                || StoreNameEnums.xiaomi.equals(source)
                || StoreNameEnums.huawei.equals(source)
                || StoreNameEnums.honor.equals(source);
    }


    public static StoreNameEnums getStoreName(String product,String channelName){
        if(StringUtils.isNotBlank(product)){
            if(StringUtils.isNotBlank(channelName)&&StringUtils.contains(channelName,product)){
                String rpold = channelName;
                channelName = channelName.replace(product,"");
                log.info("替换渠道 "+rpold+"->"+channelName);
            }
        }
        if(StringUtils.contains(channelName,StoreNameEnums.vivo.val)){
            return StoreNameEnums.vivo;
        }else if(StringUtils.contains(channelName,StoreNameEnums.oppo.val)){
            return StoreNameEnums.oppo;
        }else if(StringUtils.contains(channelName,StoreNameEnums.xiaomi.val)){
            return StoreNameEnums.xiaomi;
        }else if(StringUtils.contains(channelName,StoreNameEnums.huawei.val)){
            return StoreNameEnums.huawei;
        }else if(StringUtils.contains(channelName,StoreNameEnums.baidu.val)){
            return StoreNameEnums.baidu;
        }else if(StringUtils.contains(channelName,StoreNameEnums.ks.val)){
            return StoreNameEnums.ks;
        }else if(StringUtils.contains(channelName,StoreNameEnums.gdt.val)){
            return StoreNameEnums.gdt;
        }else if(StringUtils.contains(channelName,StoreNameEnums.tt.val)){
            return StoreNameEnums.tt;
        }else if(StringUtils.contains(channelName,StoreNameEnums.csj.val)){
            return StoreNameEnums.csj;
        }else if(StringUtils.contains(channelName,StoreNameEnums.dy.val)){
            return StoreNameEnums.dy;
        }else if(StringUtils.contains(channelName,StoreNameEnums.neilaxin.val)){
            return StoreNameEnums.neilaxin;
        }else if(StringUtils.contains(channelName,StoreNameEnums.update1.val)){
            return StoreNameEnums.update1;
        }else if(StringUtils.contains(channelName,StoreNameEnums.yingyongbao.val)){
            return StoreNameEnums.yingyongbao;
        }else if(StringUtils.contains(channelName,StoreNameEnums.honor.val)){
            return StoreNameEnums.honor;
        }
        log.warn("拉取到自然包名为 "+channelName);

        return StoreNameEnums.ziran;
    }


    public void refreshHbase(CommonHeaderDTO commonHeaderDTO, AndroidLockRst androidLockRst) {
        CompletableFuture.runAsync(()->{
            try {
                if(commonHeaderDTO.getAppId()!=null){
                    Integer appId = Integer.valueOf(commonHeaderDTO.getAppId());
                    if (Objects.equals("true", androidLockRst.getLockFlag())) {
                        lockHbaseService.save(commonHeaderDTO, appId);
                    } else {
                        // 查询一次 若锁区 释放
                        List<String> deviceIdList = new ArrayList<>();
                        deviceIdList.add(commonHeaderDTO.getDeviceId());
                        deviceIdList.add(commonHeaderDTO.getOaid());
                        deviceIdList.add(commonHeaderDTO.getImei());
                        boolean isLocked = lockHbaseService.isLocked(deviceIdList, appId, commonHeaderDTO.getUserId());
                        if (isLocked) {
                            lockHbaseService.delLocked(deviceIdList, appId, commonHeaderDTO.getUserId());
                        }
                    }
                }
            }catch (Exception e){
                log.error("Save LockEx:"+JSON.toJSONString(commonHeaderDTO),e);
            }
        });
    }

    private String buildKey(String appId,Integer os){
        return String.format("%s:%s", appId, os);
    }
    public static void main(String[] args){
        String dst = "oppo0205,ttdlsttdd,vivo0308,ksdrkldd01,update1,oppo,mi,bdwydfd12c01,oppo0305,csjyskxsk12d,ksdrywrs01,nnyyoppo,ttzxnnyy,nnyyvivo,kssbwwdwmd12dsm,cbdqmyzc12b,kssbwmyjyg12d,ttzxkxsk12d,qzlgdxgoppo,ttzx2kljyds12b,csjjlkxsk12d,neilaxin,gdtttbzbnt12d,csjjlbzbnt12d,bdqzqqdfw12b02,kssbw5ddnc12dsm,klddoppo,kssbwjcss12d,qzkxxzkxiaomi,nnyyxiaomi,xiaomi0308,ttzx2kxctjsb12b,kssbwkxxzk12d,xiaomi0305,kssbwncddp12d,qzncddpoppo,ttzxmyjyg12b,cbdwdwmd12b,kssbwjllmcjtcc12d,csjysywrs12b,gdtwxxxds12b,snbctxiaomi,csjysncddp12b,qzncddpxiaomi,qzlgdxgxiaomi,klddvivo,cbdqzssl212b02,vivo0306,kslmbzbnt12b,csjjlwdwmd12d,gdtttkxsk12d,bdwsxy12b,ksdrjysgd01,kssxhsnbct,ttzxkxxzk12b,cbdjygs12b,bdcjzcw12c01,ttzx2jcxx12d,mmqjwoppo,csjjlywrs12b,gdtttjysgd12d,csjjlncddp12b,cjzcwoppo,gdtttkxxzk12d,ksjlkxxzk12d,csjyswdwmd12d,xiaomi0220,gdtylhhjdd12d,ksdrcjzcw01,ttzxwydfd12d_e,kssxhcjtcc12d,ttzxncddp12b,oppo1116,vivo0227,gdtylhtcds12d,ttzxwdwmd12d,ksdrlgdxg01,kssbwbzbnt12bsm,gdtwxgzh4wdnc12d,qzzlychgdoppo,ksdrtyxfsh01,gdtwxgzhmyjyg12d,ksdrxxhd01,ksdrxxqsc01,ksdrwdwmd01,ksdrjygs01,kssbwlbsgc12bsj,ksdrzlychgd01,ttzxhlaxx2,cbdxxqsc12b,gdtwxjygs12b,kssxhkxxzk12d,gdtzdbw2xfrs12d,ksdrzxdfd01,gdtwxjcdls12b,ttzxkxpyp12b,ksdrwydfd01,gdtttmyjyg12d,ttzxsnbct,xlsjoppo,ksjlbzbnt12b,gdtwxgzhqmjyg12d,ttzxmmqjw,csjjlwydfd12d,ksdr2mhct01,ttzx2tnxny12d,bdqzqqdfw12b04,kssbwkxpyp12d,kssbwsxhjljysgd12d,zzkpoppo,bdttzhj12c01,bdxcyxsh12c01,ttzxjysgd12b,csjjlkxxzk12b,ksdrkxsk01,ksdrlbsgc01,ksdrbzbnt01,bdzxdfd12b,kssbwsxhlmqmjyg12d,wydfdxiaomi,bd2kxhy12c02,ttzx2ywrs12b,gdtttqsdzz12b,ksdrfydxfsh01,csjysqsdzz12d,kssbwhlxb12dsm,ksdrlnjx01,kssxhjysgd12d,kssxhkxsk12b,kssbw2hlnc12dsm,gdtwxllazc12b,csjysjysgd2b,kssbw4qmssp12d,gdtwxwdwmd12b,kssbwsxhjlcjtcc12d,csjyswdjc12d,gdtzdbwfydxfsh12d,csjysbzbnt12d,ksdrnnyy01,csjjljysgd12b,ksdr2xfcs01,gdtwx2xfnc12c,gdtwxgzhlgdxg12d,bdxtdksc12c01,kssbwsxhjlsnbct,bdakdj12c02,mmqjwxiaomi,kslmkxxzk12d,kslmmyjyg12d,kssbwsxhlmsnbct,gdtzdbwkxsk12d,cbdjwqdc12b,ttzxbzbnt12d,ksdrzzjyg01,qzxfnc2oppo,kssbwjllmjysgd12d,gdtylhddhj12d,ttznmyjyg12b,csjyswydfd12d,kssxhwdwmd12d,ksdrxtdksc01,gdtwxgzh2wdjy19d,kssbwsxhlmbsdj1,cbdqzhl204812b02,csjjllbsgc12d,gdtwxgzhkxpyp12d,kssbwqsdzz12dsj,kslmxxqsc12d,gdtwxgzhxlsj12d,xyclboppo,csjys2xfcs12d,ksjlmyjyg12d,gdtzdbwcbnt12d1,bd2wdbcy12c02,ksdrdslzc01,tgcoppo,bdyyct12c01,kssbwsxhlmwdcy12d_e,bdqzqqdfw12b01,bdhjdd12c01,kssxhqmjyg12d,gdtzdbwqmyzc12d,kssbw3cthy12d,kssbwjllmlgdxg12d,ttzxtyxfsh12d,hkdjoppo,ksdrmmqjw01,snbcyvivo,xlsjvivo,kssbwsxhjlcjzcw,csjjlxxqsc12b,qzfkpypoppo,cbdqzqmssp401,csjjlqsdzz12d,kssbwsxhlmlgdxg12d,kssbwjcdls12dsj,gdtzdbwywrs12d,csjysttzhj12d_e,kssbwzlychgd12d,kssbwkxppc12d,kssbw2kxctjsb12bsm,gdtttyqnls12d,wdjy2oppo,csjyscjtcc2b,kssbwkxsk12bjm,ksdrxmct01,csjysxxqsc12b,bdjnxz12c01,kssbwwdwmd12dsj,kssbwsxhjlqmjyg12d,xnlftoppo,ttzxwdjc12d,kssbw2hlnc12djm,kslmlbsgc12b,yyaccoppo,cbdqzqgdxfsh12b02,cbdqzcxhj01,ttzxqsdzz12d,gdtzdbwwasdj12d,kssbwxxds12bjm,qzwjmsoppo,ksdrkljyds01,vivo,ttzxxcyxsh,ttzxhjtd,kslmkljyds12d,csjjlywrs12dcs,ksdr2kxppl01,ksdrllazc01,ttzncyksy12b,ksdrzzkp01,bdhkdj12c01,kssbw2xfxz12dsm,ttznlbsgc12ddy,bdjsth12c02,kssbwdzqpd12bsm,gdtttcjtcc12d,csjysywrs12dcs,kssbwcyksy12d,ksdrxrsgd01,gdtzdbwkxssp12d,ttznxtdksc,ttznkxpyp12b,ksdrqgdxfsh01,bdygct12c01,ksdr5ddnc01,kssbwbzbnt12bsj,bd2wsncz12c02,ttznkxxzk12b,jsthoppo,kssbwcbnt12bjm,ksjlxxqsc12d,kssbwttxzk12d,ksdrmyjyg01,xiaomi1117,ttzxmbdcg,kssxhxtdksc,ksdr2wyxy01,csjystyxfsh12d,kssbwsxhlmkldd,ttznlgdxg12b,ksdr2wsncz01,kssbwsxhjlmmqjw,bdqztyrj12b01,csjyslbsgc12d,gdtwxgzhcjtcc12d,cbdqzlgdxg01,qzzlychgdxiaomi,kssbwsxhlmxtdksc,kssbw2tnxny12bjm,kssxhwazj,kssbw2kxaxx12djm,csjyskxxzk12b,kssbwqsdzz12dsm,bd2zzsj12b,kssbwzcdzs12d,csjjlwdjc12d,kslmkxsk12b,csjyswsxy12b,gdtttxxqsc12b,kssbwywrs12dsmkt,gdtzdbw2xfnc12c,csjjlcbnt12d,kssbwjcdls12dsm,csjyssnbct,csjjlcjtcc12b,hggsoppo,kssbwjllmmmqjw,ksdrwjms01,bdqmjyg12c01,xiaomi1229,gdtwxgzhcjzcw,ttzxddhj12d,ksdrlxsj01,csjjlwazj,csjysxxds12b,gdtylhlnjx12b,bdybaxx12c01,ksdr2xfxz01,kssxhcyclb,ksdr2kxhy01,gdtylhkxsk12d,kssbw2tnxny12bsj,kssbwxxds12bsj,ksdrkxxzk01,kssbwjllmkldd,ttzxxxds12b,ksdrdzqpd01,kssbwxxqsc12dsm,ksdrncddp01,gdtttlgdxg12d,bdqzqqdfw12b03,csjyswdcy12d,ttznlbsgc12d,ttzxqmjyg12b,bdtnxny12b,ksdrbpdzz01,wdnc3oppo,oppo1229,csjjlzxdfd12b,gdtwxgzhnnyy,cbdzzjyg12b,gdtsphjygs12b,csjys2zzsj12b,ttzxwazj,ksdr2zzzy01,gdtzdbwtnxny12d,csjjlxtdksc,qzxfkdsoppo,bdydtcc12c01,kssbw2xgjc12d,ttznkldd,csjysxtdksc,csjysmbdcg,ttzxcjtcc12b,kssbwsxhlmqmxyx12d_e,csjjl5ddnc12b,qmzsgoppo,csjjlxcyxsh,csjyscbnt12d,ttzxlgdxg12b,kslmyyacc12d,kssbwsxhlmxnlft,wdjy2neilaxin,ttzxzxdfd12b,csjjlxxds12b,kssbwsxhlmcjtcc12d,gdtwxgzhncddp12d,ttzxlbsgc12d,kssbwywrs12dsjkt,ksdr2jcxx01,kssxhmmqjw,ttzx4xxqsc12d,ksdrwdcy01,cbdlnjx12b,kssbw2ywdh12dsm,mmqjwvivo,csjys2tnxny12d,cbdqzlgdxg02,bdxrsgd12c01,gdtwxzxdfd19d,kssbwyzdh12djm,kssxhkxssp12d,cbdqzxxzy12b02,csjjljcdls12d,bdqzcthy212b01,kssxhcjzcw,csjysnnyy,ksdrxcyxsh01,csjysqgdxfsh12bxpt,ksdrfealx01,ttzxlnjx12d,bdttkhj12c01,csjysxcyxsh,kssbwsxhjlxxhd12d,cbdqzqmssp412b02,kssbw2ssl12d,csjjlzzkp12d,gdtttcbnt12d,ksdrqmjyg01,kssbwwsxy12dsj,csjjldzqpd12d,gdtttsnbct,kssbwjygs12dsj,kssbwjllmyyacc12d_e,ksdrtgc01,csjyswazj,csjjlsnbct,bdhkjc12c01,kssbwxxqsc12dsj,kssxhbzbnt12b,ttzxxxqsc12b,cbdqzfl204812b02,bdqzzysj12b01,ttznwdwmd12d,kssbwlnjx12bsj,ttzxcjzcw,kssbwsxhjlxtdksc,kssbwjllmgodfarm12d,gdtzdbwttzhj12d,ksdrqsdzz01,ttzx2xgjcdt01,kssbwsxhlmxlsj12d,kssbwjllmqmxyx12d,ksdrjwqdc01,csjjltyxfsh12d,kssbwsxhlmwydfd12d_e,csjyszxdfd12b,cbdtcds12b,ksjlspsnbct,bdcjtcc12c01,ksdr2qmsdj01,gdtwxgzh4wdnc13d,ttzxxnlft,kssbwsxhjlfkds12d_e,xrsgdoppo,csjysmmqjw,bdzq204812c02,ksjlspyyacc12d_e,csjjlnnyy,ddhjneilaxin,ksdrbjdxx01,kssbwqmyzc12dsm,ksdrddhj01,ksdrfeaxx01,kssbwcxhj12d,gdtwx2xfnc12d,csjjltgc12b,gdtzdbwwsxy12d,kssbwsxhjlyyacc12d_e,ksdrxyclb01,cbdlxsj12b,cbdqzkxpyp01,csjjlhggs12b,ksdryyacc01,ttzxwydfd12d,cbdqzkxppc12b02,kssbwsxhlmbpdzz,gdtzdbwgodfarm12dr,kssxhyyacc12d,kssbwjllm2mxhy12d,ksdryqnls01,kssxhlbsgc12b,ttznsnbcy12e_e,qztyrjyingyongbao,cbdwdwmd12b3,ttzxfealx12c,gdtwxqsdzz12b,kslmsnbct,kssbw2jcxx12dsm,kssbwsxhjlyqnls12d,ksdrsddzz01,ksdrklxxx01,gdtwxgzhhggs12d,gdtttkljyds12d,kssbwsxhlmlsxxx,csjyshlxb12b,kssbwwjms12d,kssbwsxhlmjysgd12d,kssbwlxsj12dsm,kslmcbnt12b,kssbwsxhlmmmqjw,csjyshjtd,kssbwjllmbpdzz,ksdrqmxyx01,ksdrrsxmb01,bdfkds12c01,csjysfeaxx12b,kssbwsddzz12djm,kssbwwsxy12dsm,ttzxkldd,csjysdzqpd12d,ttznzzkp12b_e,csjjl2hlnc12b,gdtttzlychgd12d,bdqcdh12c02,ksdrwazj01,ksdrtcds01,csjyswdcy12b,ksdrjcdls01,kslmwdwmd12d,ksdryyty01,gdtzdbw2qmdsp12dr,ttzn4xxqsc12d,gdtylhcbnt12d,ttzxhjtddt01,ttzxzzkp12d,ksdrqmzsg01,csjjlhlxb12b,kssxhkxpyp12d";
        dst = "ttdlsttdd";

        System.out.println(StringUtils.contains(dst,"ttds"));
        for(String ss : dst.split(",")){
            System.out.println(getStoreName("ttdls",ss));
        }

        System.out.println(StringUtils.containsAny("上海","北京","南京","杭州","深圳","成都"));
    }

    public Set<String> getBlackPkgs(String product, String channel) {
        StoreNameEnums storeNameEnums = getStoreName(product,channel);
        return androidLockBlacksServicel.getBlackPkgs(storeNameEnums);
    }
}
