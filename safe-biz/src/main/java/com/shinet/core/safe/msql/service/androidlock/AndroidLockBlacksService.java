package com.shinet.core.safe.msql.service.androidlock;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.net.Ipv4Util;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.inject.internal.cglib.proxy.$MethodProxy;
import com.shinet.core.safe.enums.StoreNameEnums;
import com.shinet.core.safe.msql.config.SafeSwitcher;
import com.shinet.core.safe.msql.entity.AndroidLockBlacks;
import com.shinet.core.safe.msql.entity.AndroidLockRst;
import com.shinet.core.safe.msql.mapper.AndroidLockBlacksMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2024-03-16
*/
@Service
@Slf4j
public class AndroidLockBlacksService extends ServiceImpl<AndroidLockBlacksMapper, AndroidLockBlacks> {

    @Value("${lock.refresh.blcks.log.switch:false}")
    private boolean refreshLogSwitch;

    private Map<String, AdBlackMes> mesBlcks = Maps.newHashMap();
    @PostConstruct
    @Scheduled(cron = "0 0/3 * * * ? ")
    public void initAccountActionConfig() {
        try {
            Map<String, AdBlackMes> mtBlckMap = Maps.newHashMap();
            List<AndroidLockBlacks> list = lambdaQuery().list();
            for(AndroidLockBlacks androidLockBlacks : list){
                AdBlackMes adBlackMes = mtBlckMap.get(androidLockBlacks.getSoreName());
                if(adBlackMes==null){
                    adBlackMes = new AdBlackMes();
                }
                addLocks(adBlackMes,androidLockBlacks);
                mtBlckMap.put(androidLockBlacks.getSoreName(),adBlackMes);
            }
            mesBlcks = mtBlckMap;
            if (refreshLogSwitch) {
                log.info("成功刷新渠道黑名单数据 "+ JSON.toJSONString(mesBlcks));
            }
        }catch (Exception e){
            log.error("刷新渠道黑名单数据失败",e);
        }
    }

    private void addLocks(AdBlackMes adBlackMes,AndroidLockBlacks androidLockBlacks){
        if(StringUtils.isNotBlank(androidLockBlacks.getLockIps())){
            adBlackMes.getIpBlacks().addAll(Lists.newArrayList(androidLockBlacks.getLockIps().split(",")));
        }
        if(StringUtils.isNotBlank(androidLockBlacks.getLockOaids())){
            adBlackMes.getOaidBlacks().addAll(Lists.newArrayList(androidLockBlacks.getLockOaids().split(",")));
        }
        if(StringUtils.isNotBlank(androidLockBlacks.getLockPkgs())){
            adBlackMes.getPkgBlacks().addAll(Lists.newArrayList(androidLockBlacks.getLockPkgs().split(",")));
        }
        if(StringUtils.isNotBlank(androidLockBlacks.getLockCitys())){
            adBlackMes.getLockCitys().addAll(Lists.newArrayList(androidLockBlacks.getLockCitys().split(",")));
        }
        if(StringUtils.isNotBlank(androidLockBlacks.getLockUnionids())){
            adBlackMes.getLockUnionids().addAll(Lists.newArrayList(androidLockBlacks.getLockUnionids().split(",")));
        }
    }
    @Autowired
    SafeSwitcher safeSwitcher;

    public Pair<Boolean,String> isLockByBlck(String product,
            StoreNameEnums storeNameEnums, String ip, String oaid,
            String pkgs,
            String lcPkgs,
            boolean isOcpc,
            String city,
            String unionId){

        // 先走default 配置拉黑
        Pair<Boolean,String> defaultLockRes = isLockByStoreBlackConf(StoreNameEnums.DEFAULT_STORE, product, ip, oaid, pkgs, lcPkgs, isOcpc, city, unionId);
        if (defaultLockRes.getKey()) {
            return defaultLockRes;
        }

        // default配置没拉黑的话，走其他渠道配置拉黑
        Pair<Boolean,String> otherLockRes = isLockByStoreBlackConf(storeNameEnums, product, ip, oaid, pkgs, lcPkgs, isOcpc, city, unionId);
        if (otherLockRes.getKey()) {
            return otherLockRes;
        }

        return new Pair<>(false,"");
    }


    /**
     * 判断是否被配置的Store黑名单命中
     * @param storeNameEnums
     * @param ip
     * @param oaid
     * @param pkgs
     * @param lcPkgs
     * @param isOcpc
     * @param city
     * @param unionId
     * @return
     */
    private Pair<Boolean, String> isLockByStoreBlackConf(StoreNameEnums storeNameEnums, String product, String ip, String oaid, String pkgs, String lcPkgs, boolean isOcpc, String city, String unionId) {
        AdBlackMes adBlackMes = mesBlcks.get(storeNameEnums.val);
        //判断是否在锁区渠道
        if (adBlackMes != null) {
            if (StringUtils.isNotBlank(ip) && adBlackMes.getIpBlacks().contains(ip)) {
                String msg = "" + storeNameEnums.val + " 拉黑ip直接锁区 " + ip;
                log.info(msg);

                return new Pair<>(true, msg);
            }
            if (StringUtils.isNotBlank(oaid) && adBlackMes.getOaidBlacks().contains(oaid)) {
                String msg = "" + storeNameEnums.val + " 拉黑oaid直接锁区 " + ip;
                log.info(msg);
                return new Pair<>(true, msg);
            }

            Set<String> pkgSet = new HashSet<>();
            if (StringUtils.isNotBlank(lcPkgs)) {
                pkgSet.addAll(Sets.newHashSet(lcPkgs.split(",")));
            }
            if (StringUtils.isNotBlank(pkgs)) {
                pkgSet.addAll(Sets.newHashSet(pkgs.split(",")));
            }
            if (pkgSet.size() > 0) {
                Set<String> dbSet = Sets.newHashSet(adBlackMes.getPkgBlacks());
                dbSet.retainAll(pkgSet);

                if (dbSet.size() > 0) {
                    String msg = "" + storeNameEnums.val + " 拉黑pkg直接锁区 " + JSON.toJSONString(dbSet);
                    log.info(msg);
                    return new Pair<>(true, msg);
                }
            }
            if (!isOcpc && StringUtils.isNotBlank(city)) {
                if ((StoreNameEnums.vivo.equals(storeNameEnums) || StoreNameEnums.honor.equals(storeNameEnums) || StoreNameEnums.huawei.equals(storeNameEnums)) && safeSwitcher.adNewLockPros.contains(product)) {
                    log.info("白名单产品 略过城市锁 " + product + " " + storeNameEnums + " " + city + " " + isOcpc);
                } else {
                    if (!isOcpc && adBlackMes.getLockCitys().stream().anyMatch(ct -> city.contains(ct))) {
                        String msg = "" + storeNameEnums.val + " 拉黑city直接锁区 " + city;
                        log.info(msg);
                        return new Pair<>(true, msg);
                    }
                }
            }

            if (StringUtils.isNotBlank(unionId)) {
                Set<String> dbSet = Sets.newHashSet(adBlackMes.getLockUnionids());
                if (dbSet.contains(unionId)) {
                    String msg = "" + storeNameEnums.val + " 拉黑unionid直接锁区 " + JSON.toJSONString(dbSet);
                    log.info(msg);
                    return new Pair<>(true, msg);
                }
            }
        }

        return new Pair<>(false, "");
    }

    public void insertAndroidLockByBlck(AndroidLockRst androidLockRst){
        if (!"true".equals(androidLockRst.getLockFlag())) {
            return;
        }
        String storeName = androidLockRst.getStoreName();
        if (StringUtils.isBlank(storeName) || "ziran".equals(storeName)) {
            return;
        }
        AndroidLockBlacks androidLockBlacks = this.lambdaQuery().eq(AndroidLockBlacks::getSoreName, storeName).last("limit 1").one();

        try {
            if (Objects.isNull(androidLockBlacks)) {
                androidLockBlacks = new AndroidLockBlacks();
                androidLockBlacks.setSoreName(androidLockRst.getStoreName());
                androidLockBlacks.setLockCitys(androidLockRst.getCity());
                androidLockBlacks.setLockIps(androidLockRst.getIp());
                androidLockBlacks.setLockOaids(androidLockRst.getOaid());
                androidLockBlacks.setCreateTime(new Date());
                androidLockBlacks.setUpdateTime(new Date());
                save(androidLockBlacks);
            } else {
                log.info("安卓新增lockBlack配置 " + JSON.toJSONString(androidLockRst));
                String lockCitys = androidLockBlacks.getLockCitys();
                if (StringUtils.isNotBlank(lockCitys) && StringUtils.isNotBlank(androidLockRst.getCity()) && !lockCitys.contains(androidLockRst.getCity())) {
                    androidLockBlacks.setLockCitys(lockCitys + "," + androidLockRst.getCity());
                }
                String lockIps = androidLockBlacks.getLockIps();
                if (StringUtils.isNotBlank(lockIps) && StringUtils.isNotBlank(androidLockRst.getIp()) && !lockIps.contains(androidLockRst.getIp())) {
                    androidLockBlacks.setLockIps(lockIps + "," + androidLockRst.getIp());
                }
                String lockOaids = androidLockBlacks.getLockOaids();
                if (StringUtils.isNotBlank(lockOaids) && StringUtils.isNotBlank(androidLockRst.getOaid()) && !lockOaids.contains(androidLockRst.getOaid())) {
                    androidLockBlacks.setLockOaids(lockOaids + "," + androidLockRst.getOaid());
                }
                this.updateById(androidLockBlacks);
            }

        } catch (Exception e) {
            log.error("安卓新增lockBlack配置 异常 " + e);
        }

    }

    public Set<String> getBlackPkgs(StoreNameEnums storeNameEnums) {
        AdBlackMes adBlackMes = mesBlcks.get(storeNameEnums.val);
        if(adBlackMes != null){
            return adBlackMes.getPkgBlacks();
        }
        return null;
    }

    public static void main(String[] args) {
        Set<Integer> set1 = new HashSet<>();
        set1.add(1);
        set1.add(2);
        set1.add(3);

        Set<Integer> set2 = new HashSet<>();
        set2.add(2);
        set2.add(3);
        set2.add(4);

        // 求set1与set2的交集
        set1.retainAll(set2);

        System.out.println("交集结果为：" + set1);
    }
}
