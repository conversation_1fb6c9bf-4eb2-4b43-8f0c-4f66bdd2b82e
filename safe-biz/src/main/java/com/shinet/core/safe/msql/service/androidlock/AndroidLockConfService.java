package com.shinet.core.safe.msql.service.androidlock;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.shinet.core.safe.enums.StoreNameEnums;
import com.shinet.core.safe.msql.config.SafeSwitcher;
import com.shinet.core.safe.msql.entity.AndroidLockConf;
import com.shinet.core.safe.msql.mapper.AndroidLockConfMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* <p>
    * 锁区IOS特殊策略 服务实现类
    * </p>
*
* <AUTHOR>
* @since 2024-03-15
*/
@Service
@Slf4j
public class AndroidLockConfService extends ServiceImpl<AndroidLockConfMapper, AndroidLockConf> {
    @Value("${lock.refresh.blcks.log.switch:false}")
    private boolean refreshLogSwitch;

    private Map<String, List<AndroidLockConf>> anlockConfMap = Maps.newHashMap();
    @PostConstruct
    @Scheduled(cron = "*/30 * * * * ? ")
    public void initAccountActionConfig() {
        try {
            Map<String, List<AndroidLockConf>> mtBlckMap = Maps.newHashMap();
            List<AndroidLockConf> list = lambdaQuery().eq(AndroidLockConf::getIsEnable, 1).list();
            for(AndroidLockConf androidLockConf : list){
                List<AndroidLockConf> adBlackMes = mtBlckMap.get(androidLockConf.getProduct());
                if(adBlackMes==null){
                    adBlackMes = new ArrayList<>();
                }
                adBlackMes.add(androidLockConf);
                mtBlckMap.put(androidLockConf.getProduct(),adBlackMes);
            }

            anlockConfMap = mtBlckMap;
            if (refreshLogSwitch) {
                log.info("成功刷新锁区配置数据 "+ JSON.toJSONString(mtBlckMap));
            }

        }catch (Exception e){
            log.error("",e);
        }
    }

    public List<AndroidLockConf> queryAllConfs(String product){
        LambdaQueryChainWrapper<AndroidLockConf> lab = lambdaQuery();

        if (StringUtils.isNotBlank(product)) {
            lab.eq(AndroidLockConf::getProduct, product);
            List<AndroidLockConf> cfgList = lab.list();
            return cfgList;
        } else {
            return new ArrayList<>();
        }
    }

    public List<AndroidLockConf> queryStoreConfs(String product,StoreNameEnums storeNameEnums){
        LambdaQueryChainWrapper<AndroidLockConf> lab = lambdaQuery();
        if (StringUtils.isNotBlank(product) && storeNameEnums!=null) {
            lab.eq(AndroidLockConf::getProduct, product);
            lab.eq(AndroidLockConf::getStoreName, storeNameEnums.val);
            List<AndroidLockConf> cfgList = lab.list();
            return cfgList;
        }
        return new ArrayList<>();
    }

    public static boolean isOcpc(StoreNameEnums storeNameEnums,String dsp,String product, String channel,String oaid){
        boolean isOcpc = StringUtils.contains(dsp,"kuaishou") ||
                StringUtils.contains(dsp,"toutiao") ||
                StringUtils.contains(dsp,"baidufeed") ||
                StringUtils.contains(dsp,"guangdiantong") ;
        if(StoreNameEnums.vivo.equals(storeNameEnums)){
            isOcpc = isOcpc && !StringUtils.equalsIgnoreCase(dsp,"baidufeed")  && !StringUtils.equalsIgnoreCase(dsp,"vivo");

            if(StringUtils.equalsIgnoreCase(dsp,"baidufeed")){
                log.info("baidufeed非ocpc "+" "+product+" "+channel+" "+oaid);
            }
        }

        if(StoreNameEnums.oppo.equals(storeNameEnums)){
            isOcpc =isOcpc  || StringUtils.contains(dsp,"oppo");
        }

        /**
         * kuaishou
         * toutiao
         * baidufeed
         * guangdiantong
         * oppo
         */
        return isOcpc;
    }

    public static void main(String[] args){
        System.out.println( isOcpc(StoreNameEnums.vivo,"oppo","jyxbt","vivo","123123"));
        System.out.println( isOcpc(StoreNameEnums.huawei,"oppo","jyxbt","huawei","123123"));
        System.out.println( isOcpc(StoreNameEnums.honor,"oppo","jyxbt","honor","123123"));
        System.out.println( isOcpc(StoreNameEnums.xiaomi,"oppo","jyxbt","mi","123123"));
    }
    @Autowired
    AndroidLockRstService androidLockRstService;
    @Autowired
    SafeSwitcher safeSwitcher;
    public Pair<Boolean,String> isLockByStore(String product, String channel, String appVersion, boolean isOcpc, StoreNameEnums storeNameEnums, String oaid){
        boolean isLoc = false;
        String reason = "";

        if(StoreNameEnums.vivo.equals(storeNameEnums)){
            if(StringUtils.isNotBlank(oaid) && oaid.length()>3){
                long alockNum = androidLockRstService.countLockOaidNum(oaid,storeNameEnums);
                if(alockNum>0){
                    log.info("huavivo有一个已锁 ，全锁 "+" "+product+" "+channel+" "+oaid);
                    return new Pair<>(true,"huavivo有一个已锁 "+storeNameEnums.val+" 已有锁 "+alockNum );
                }
            }
        }

        if(!isOcpc && StoreNameEnums.huawei.equals(storeNameEnums)){
            isLoc = true;
            reason = "华为渠道全锁";
            log.info("华为渠道 全锁 "+isLoc+" @ "+storeNameEnums+"  "+product+" "+channel+" "+appVersion);
            return new Pair<>(false,""+product+" 白名单" );
        } else if(!isOcpc && StoreNameEnums.honor.equals(storeNameEnums)){
            isLoc = true;
            reason = "荣耀渠道全锁";
            log.info("荣耀渠道 全锁 "+isLoc+" @ "+storeNameEnums+"  "+product+" "+channel+" "+appVersion);

            return new Pair<>(false,""+product+" 白名单" );
        }



        return new Pair<>(isLoc,reason);
    }

    public Pair<Boolean,String> isLockByChannelVersion(String product, String channel,String appVersion,boolean isOcpc,StoreNameEnums storeNameEnums){
        List<AndroidLockConf>  androidLockConfs = anlockConfMap.get(product);

        boolean isBdKs = StoreNameEnums.ks.equals(storeNameEnums) || StoreNameEnums.baidu.equals(storeNameEnums);

        if(androidLockConfs!=null && androidLockConfs.size()>0){
            for(AndroidLockConf androidLockConf : androidLockConfs) {
                boolean allLock = androidLockConf.getAllLock() == 1;
                String baseReason = allLock ? "全锁" : "非ocpc锁";
                if (StringUtils.isNotBlank(androidLockConf.getLockPkgs()) && StringUtils.isNotBlank(androidLockConf.getLockVersions())) {
                    Set<String> locChals = Sets.newHashSet(androidLockConf.getLockPkgs().split(","));
                    Set<String> locVes = Sets.newHashSet(androidLockConf.getLockVersions().split(","));
                    if (locChals.contains(channel) && locVes.contains(appVersion)) {
                        if(allLock || (!isOcpc && !isBdKs)) {
                            String msg = "["+baseReason+"]锁区命中渠道号且版本号 " + product + " " + channel + " " +  appVersion;
                            log.info("锁区命中渠道号且版本号 " + channel + " " + appVersion  + JSON.toJSONString(androidLockConf));
                            return new Pair<>(true, msg);
                        }
                    }
                } else {
                    if (StringUtils.isNotBlank(androidLockConf.getLockPkgs())) {
                        Set<String> locChals = Sets.newHashSet(androidLockConf.getLockPkgs().split(","));
                        if (locChals.contains(channel)) {
                            if(allLock || (!isOcpc && !isBdKs)) {
                                String msg = "["+baseReason+"]锁区命中渠道号 " + product + " " + channel;
                                log.info("锁区命中渠道号 " + channel + " " + JSON.toJSONString(androidLockConf));
                                return new Pair<>(true, msg);
                            }
                        }
                    }

                    //产品额外变更锁区逻辑，若"包名列表" 对应的lockPkgs 字段为空， 则无所谓版本，都不进行锁定操作
                    if (androidLockConf.getLockPkgs() == null || StringUtils.isBlank(androidLockConf.getLockPkgs())) {
                        if (allLock || (!isOcpc && !isBdKs)) {
                            String msg = "[" + baseReason + "]锁区配置项为空,无所谓版本都不进行锁定操作,渠道号 " + product + " " + channel;
                            log.info("锁区配置项为空,无所谓版本都不进行锁定操作,渠道号 " + channel + " " + JSON.toJSONString(androidLockConf));
                            return new Pair<>(false, msg);
                        }
                    }

                    if (StringUtils.isNotBlank(androidLockConf.getLockVersions())) {
                        Set<String> locVes = Sets.newHashSet(androidLockConf.getLockVersions().split(","));
                        if (locVes.contains(appVersion)) {
                            if(allLock || (!isOcpc && !isBdKs)) {
                                String msg = "["+baseReason+"]锁区命中版本号 " + product + " " + appVersion;
                                log.info("锁区命中版本号 " + appVersion + " " + JSON.toJSONString(androidLockConf));
                                return new Pair<>(true, msg);
                            }
                        }
                    }
                }
            }
        }
        return new Pair<>(false,"");
    }
}
