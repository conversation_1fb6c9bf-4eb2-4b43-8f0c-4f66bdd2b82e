package com.shinet.core.safe.msql.service.androidlock;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.shinet.core.safe.enums.StoreNameEnums;
import com.shinet.core.safe.msql.entity.*;
import com.shinet.core.safe.msql.mapper.AndroidLockRstMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.safe.msql.service.MysqlTableService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Service
@Slf4j
public class AndroidLockRstService extends ServiceImpl<AndroidLockRstMapper, AndroidLockRst> {
    public static String abdlockRstTname = "android_lock_rst";
    @Autowired
    MysqlTableService mysqlTableService;
    @Autowired
    AndroidLockBlacksService androidLockBlacksService;

    private AndroidLockRst getExistRst(String product, String oaId,String channel, String lockFlag,String ip,boolean isIp) {
        LambdaQueryChainWrapper<AndroidLockRst> lambdaQueryChainWrapper =
                lambdaQuery().eq(AndroidLockRst::getProduct, product).eq(AndroidLockRst::getChannel, channel)
                .eq(AndroidLockRst::getLockFlag, lockFlag);

        if(isIp){
            lambdaQueryChainWrapper.eq(AndroidLockRst::getIp, ip)
                    .last("limit 1");
        }else{
            if(StringUtils.isNotBlank(oaId)){
                lambdaQueryChainWrapper.eq(AndroidLockRst::getOaid, oaId)
                        .last("limit 1");
            }else{
                lambdaQueryChainWrapper.eq(AndroidLockRst::getIp, ip)
                        .last("limit 1");
            }
        }
        return lambdaQueryChainWrapper.one();
    }

    public void saveRst(AndroidLokReq androidLokReq, AndroidLockRst androidLockRst,boolean isIp) {
        mysqlTableService.createIfNot(abdlockRstTname);
        CompletableFuture.runAsync(() -> {
            try {
                AndroidLockRst existRst = getExistRst(androidLokReq.getProduct(), androidLokReq.getOaid(),androidLokReq.getChannel(), androidLockRst.getLockFlag(),androidLockRst.getIp(),isIp);

                if(isIp && StringUtils.isNotBlank(androidLockRst.getIp()) && Objects.nonNull(existRst)){
                    if(!StringUtils.contains(existRst.getRemark2(),androidLockRst.getRemark2()) || !StringUtils.equalsIgnoreCase(androidLockRst.getLockFlag(),existRst.getLockFlag())){
                        log.info("ip锁lockflg androidLokReq: {}, androidLockRst: {}", JSON.toJSONString(androidLokReq), JSON.toJSONString(androidLockRst));
                        existRst.setRemark(existRst.getRemark() + ";" + androidLockRst.getRemark());
                        if(StringUtils.isNotBlank(androidLockRst.getRemark2())) {
                            existRst.setRemark2(existRst.getRemark2() + ";" + androidLockRst.getRemark2());
                        }
                        existRst.setLockFlag(androidLockRst.getLockFlag());
                        if (StringUtils.isNotBlank(androidLokReq.getUserId()) && !"null".equals(androidLockRst.getUserId())) {
                            existRst.setUserId(androidLokReq.getUserId());
                        }
                        existRst.setUpdateTime(new Date());
                        updateById(existRst);
                    }
                }else  if (StringUtils.isNotBlank(androidLokReq.getOaid()) && Objects.nonNull(existRst)) {
                    // 原逻辑锁和非锁会各存一条，这里原逻辑是已经根据lock查出来数据了，所以只做补全更新时间、userId
                    if (Objects.equals("true", androidLockRst.getLockFlag())) {
                        log.info("安卓锁区同用户 androidLokReq: {}, androidLockRst: {}", JSON.toJSONString(androidLokReq), JSON.toJSONString(androidLockRst));
                        existRst.setRemark(existRst.getRemark() + ";" + androidLockRst.getRemark());
                        if(StringUtils.isNotBlank(androidLockRst.getRemark2())) {
                            existRst.setRemark2(existRst.getRemark2() + ";" + androidLockRst.getRemark2());
                        }
                        existRst.setUpdateTime(new Date());
                        if (StringUtils.isNotBlank(androidLokReq.getUserId()) && !"null".equals(androidLockRst.getUserId())) {
                            existRst.setUserId(androidLokReq.getUserId());
                        }
                        updateById(existRst);
                    } else {
                        UpdateWrapper<AndroidLockRst> updateWrapper = new UpdateWrapper<>();
                        updateWrapper.lambda().eq(AndroidLockRst::getId, existRst.getId())
                                .set(AndroidLockRst::getUpdateTime, new Date())
                                .set(StrUtil.isNotBlank(androidLokReq.getUserId())  && !"null".equals(androidLockRst.getUserId()), AndroidLockRst::getUserId, androidLokReq.getUserId());
                        update(updateWrapper);
                    }
                } else {
                    Date date = new Date();
                    androidLockRst.setProduct(androidLokReq.getProduct());
                    androidLockRst.setOs("android");
                    androidLockRst.setOaid(androidLokReq.getOaid());
                    androidLockRst.setImei(androidLokReq.getImei());
                    androidLockRst.setAndroidId(androidLokReq.androidId);
                    androidLockRst.setChannel(androidLokReq.channel);
                    androidLockRst.setUserId(androidLokReq.getUserId());
                    androidLockRst.setAppVersion(androidLokReq.getAppVersion());
                    androidLockRst.setCreateTime(date);
                    androidLockRst.setUpdateTime(date);

                    if(StringUtils.isBlank(androidLockRst.getStoreName())){
                        StoreNameEnums storeNameEnums = AndroidLockService.getStoreName(androidLokReq.getProduct(),androidLokReq.getChannel());
                        androidLockRst.setStoreName(storeNameEnums.val);
                    }
                    save(androidLockRst);
                }
            }catch (Exception e){
                log.error("android锁区存储失败 ",e);
            }
        });
//        androidLockBlacksService.insertAndroidLockByBlck(androidLockRst);
    }


    public AndroidLockRst queryByOaid(String product, String oaid, String channel) {
        if (StringUtils.isNotBlank(product) && StringUtils.isNotBlank(oaid) && StringUtils.isNotBlank(channel)) {
            AndroidLockRst androidLockRst = lambdaQuery().eq(AndroidLockRst::getProduct, product)
                    .eq(AndroidLockRst::getOaid, oaid)
                    .eq(AndroidLockRst::getChannel, channel)
                    .orderByDesc(AndroidLockRst::getUpdateTime)
                    .last("limit 1").one();
            return androidLockRst;
        } else {
            log.warn("ANDROID获取锁区结果错误 " + product + " oaid : " + oaid);
            return null;
        }
    }


    public AndroidLockRst queryByIp(String product, String ip, String channel) {
        if (StringUtils.isNotBlank(product) && StringUtils.isNotBlank(ip) && StringUtils.isNotBlank(channel)) {
            AndroidLockRst androidLockRst = lambdaQuery().eq(AndroidLockRst::getProduct, product)
                    .eq(AndroidLockRst::getIp, ip)
                    .eq(AndroidLockRst::getChannel, channel)
                    .eq(AndroidLockRst::getLockFlag, "true")
                    .last("limit 1").one();
            return androidLockRst;
        } else {
            log.warn("ANDROID获取锁区结果错误 " + product + " ip:" + ip);
            return null;
        }
    }


    public long countLockOaidNum(String oaid, StoreNameEnums storeNameEnums) {
        if(StoreNameEnums.huawei.equals(storeNameEnums) || StoreNameEnums.honor.equals(storeNameEnums) || StoreNameEnums.vivo.equals(storeNameEnums)){
            if(StringUtils.isNotBlank(oaid)){
                long cnum = lambdaQuery()
                        .eq(AndroidLockRst::getOaid, oaid)
                        .eq(AndroidLockRst::getLockFlag,"true")
                        .count();

                return cnum;
            }
        }
        return 0l;
    }
}
