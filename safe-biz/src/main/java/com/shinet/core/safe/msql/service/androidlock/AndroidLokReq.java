package com.shinet.core.safe.msql.service.androidlock;

import lombok.Data;

@Data
public class AndroidLokReq {
    String product;
    String channel;
    String oaid;
    String imei;
    String androidId;
    String pkgs;
    String userId;
    String mac;
    String appVersion;
    String ua;
    String model;
    //null、0:无录屏 1:有录屏
    Integer scRecord;

    String lcPkgs;//锁区pkgs
    Boolean isVp = false;

    Boolean isPkgSw = false;

    // 是否广告接口调用
    Boolean callByAdConfig;
}
