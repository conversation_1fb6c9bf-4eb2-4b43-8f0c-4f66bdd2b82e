package com.shinet.core.safe.msql.service.androidlock;

import com.google.common.collect.Sets;

import java.util.Set;

public class DeviceLcService {
    public static Set<String> lset = Sets.newHashSet(new String[]{
            "05ecd2b9-7550-48f6-8bf9-1c428aea594f",
            "B0EC343693454E6093E2EC38B14B4F9F33a71ebfa243aa2c03a7d9c9c8b8a1a7",
            "cfe43a4a646e26b2",
            "d2d7dcefbba8ac67204898314fe11beacdf7e07ef8ba8db81622610137811769",
            "2e0c464845f1d25b66da623577d1b6723969a16c24eef5970f47ee6699f3bb99",
            "555ba632-7507-42ce-8d3a-bf2da6415be1",
            "6c606db4-0e37-4247-9b77-d683afefda9c",
            "fbdb469b-0af0-4019-9599-cc7b8e1eae89",
            "0c445e8f91ca51a8",
            "539806c223160e93",
            "2F65D93B3E364B55ABD963464DCF1C8F54d9f0c8c45fd77c2f510dbfebf1fdd8",
            "C23F3F6E5923435E94A6AA9510744AEE6c8c8dd70f50bb6e33c32823b6dfb127",
            "de08355b-8ed8-46f0-b62a-1df40b8df2cd",
            "1519c9473175f8f6",
            "eb67c682-772a-46eb-8a09-cd50991b28f2",
            "02cb0eb5-7afa-4085-b533-0025279c6d45",
            "10f67e79d77399c1",
            "74688830d1aa2cfe",
            "3a8b6fbb00bbb4cc",
            "a69968e3d16c8307",
            "931f0fd36f63e299651a8f605dac02e7f422cb19faf9afd3b10010bd8ee2364a",
            "4a0258af-72f5-4743-946c-4aad233dc966",
            "ebe094555f2ab6e1",
            "f9e8bafc-fb6b-d1a9-3ffb-f7f04fa6f778",
            "D1E0C87A335E473F877CCB30426C3699274c835bde18a9c20c48fad0954fff8a",
            "9d269aa9-f9c7-44b8-9c13-0fbc4591485d",
            "oJ5yX63udA4xXe6hw7MGlyBlBtPw",
            "eb7b9609-ebb9-43fb-ae8c-0975a516e944",
            "9903b8cd-14de-447d-9cf8-8178043f0486",
            "oJ5yX63t5Uw7ukTpkicd0uD3DQVU",
            "ffe63997-fdef-a2d9-ffe7-fdf6fdfebbf3",
            "69a77a60-c877-4eda-86bb-68c13a47eeb9",
            "77a93ca1-2ff7-4a2b-a556-1d41bdfe2dc4",
            "7e594e0b-4f69-419c-a5d8-501e3457a395",
            "f1866b6e-9e69-421a-b360-90b6e9a1bf3f",
            "ba5e37ec786a8ab4",
            "2A58EFCC9B9A48C985D8422F964B370F5c2ea11b223994554be3f4e71b884ae5",
            "7fff67ed-e37d-9387-ef6b-fbff5f5fc001",
            "9e90623532018f139db0ff12d798c86315661def0f846c213286ba43de303ec2",
            "7c4804264917a9ef",
            "05ec3d77291cc42f7ad004cf893ffaffb5fcddb05c90811a67e2601d8be37a4d",
            "3edb459e-4485-468b-b01f-d6c6242d8854",
            "1be4048d-ab21-4ae1-b738-f90758475c8c",
            "dff5cc3f-8dc6-44de-ba1d-908c5b4d9b66",
            "045496faf0ee9f0f0b05c881040db4ab8410f25f13f2b0f4d1440c32538b3937",
            "9fba235e-6ecb-4401-b662-b70671328e30",
            "759f32a9-c607-4cfb-8528-8898a334ff2a",
            "cf44ba64-9b96-4b07-a465-b1043a002bef",
            "bcbfe3fc-fefd-0316-ff11-cbffd6ce8757",
            "0bd2f80cb3e94dfc",
            "d4627d88-9649-4378-9092-7a7f6fcf38d8",
            "148f19102a53c513760226e3a671543b0c0964e22fbd5b734cc6dd948d62bb46",
            "ef7e5cd53fbd67019f030be1a64382db700a3a95776b0bc91f2b68d0d0836383",
            "6FFCBA13050F4074B8942F3A0A1A4F2F259a8cf4cf3fbfdc998b3850e4ab2dca",
            "63A5CB2FBBAD4E46A9C972A515BCA53Dc80370de2c1723380aa9ddbf20b8034c",
            "4f4ff20a-0481-4df0-93c7-59a6908a083d",
            "3270a7e7-4f74-43fb-901e-8495f96ba811",
            "643216a94e0cae4c",
            "d04aea6e885c5fae",
            "bc0631daa03b6bb0",
            "956fa131c3ad3301",
            "156739dd5c09e7eb",
            "oJ5yX6wsMM5Qef6PcElFtLYO693w",
            "oJ5yX68uRnEIiHt4hB65qXxpAFk8",
            "49E13F4BD50E4541BDBE6126749906687e3dfabb33f8e0835becaacf23823b01",
            "oJ5yX688HLm1rjMP5VE4md3QLZdc",
            "56492ab350da3edb",
            "4C9C15CE9E784B86BB966AB457F1D42Abb7babc3ddac3f855d2cead6bd507184",
            "793c6380cd3e3438",
            "1ce33f97-1f6e-41d3-874c-a98b58ac1253",
            "48aca217e641fbcb",
            "a031ff5a-8637-4d2e-9077-a82bf00d6c0f",
            "8c559592fced1ee58a5a534aa07c9ffde5359222e43155213909da3eaf63fa2d",
            "f2a04a3b7b178c4d",
            "69E1663D9A63464BB3B62B3F11BBB9F7140db11e9e24b9de59e40fa266620029",
            "8d547fc4-dc33-448f-98ee-d79f1662c286",
            "oJ5yX6xUiFSrNiqkmGepl4kbaQTw",
            "a2e8c93c-4887-4be2-8ac0-c93bff7b19ba"
    });
    public static Set<String> imeiset = Sets.newHashSet(new String[]{
            "f7244bcafb37ce07",
            "20c2ba0c6404ed67",
            "fe6af880f42f33f3",
            "a03161dfa0cdcbd2",
            "9f5300ab2b364a3f",
            "e5bcdd95eb668a98",
            "26df04924c6d72db",
            "7b350b2aed7e9c1c",
            "a81815954b9cd58f",
            "7241d7049ffeabec",
            "975eee651483908b",
            "fd9d1ab04e03c24b",
            "6f54725915fa4925",
            "001eef6a712a9c81",
            "40f7f1a6cd9c87f5",
            "b94dd91f7969a71b",
            "cf5ade209246b7a1",
            "3d7286a4c914fbaf",
            "201748ea0716fd29",
            "51eb6ee6000335e7",
            "010139027421282",
            "a98c28f5385b63a4",
            "d57accdf2184f5eb",
            "656f4867c8825e8f",
            "2f25e97eec4e75b1",
            "de3a22075a86f909",
            "be8d16c5cc890fff",
            "83a46654573626ae",
            "c7b5fe7c6dd171b9",
            "1380cfae1626aca6",
            "353512027130613",
            "862859038150435",
            "63c56f1b8c0b7552",
            "1a27c832b5043c59",
            "d106d53279a389a5",
            "354732086864774",
            "78e14d215eeec759",
            "78ad204bdf2660cf",
            "d6983433abd997ee",
            "9cda51a2e1a20339",
            "96d995e61fe15a90",
            "93ac12bd6f03156f",
            "868375035892402",
            "3f643908bcaa1c90",
            "oJ5yX63udA4xXe6hw7MGlyBlBtPw",
            "010306026669316",
            "2e6c39a9930a0c26",
            "fb3791ea6c406442",
            "63dadc22534adaeb",
            "543f573c10792b7d",
            "984913d12ff3f1fa",
            "03ddc79a7d55346e",
            "0b80aecf899a26bc",
            "0d08e27d0cbc2f39",
            "6a804a0cc824d1fe",
            "08bcd8234e9543c8",
            "41049a17391b1787",
            "a80a73b2f4e7a520",
            "868981032144919",
            "be0a659b70fb95f0",
            "4d45fab968deaf32",
            "5d49b39d58b232a7",
            "3b084ab7f18c75f3",
            "43889e46765739d5",
            "040d16d393dde79f",
            "c33d5940ee8e7d2f",
            "860576037415310",
            "6eca876022f1889b",
            "6e61b15950d125cb",
            "ce6cf70e6f59dba2",
            "oJ5yX6wsMM5Qef6PcElFtLYO693w",
            "242d03346ef44df1",
            "f04d24c9eb0faf5c",
            "11d6646d1c508a4a",
            "a63fbb7f852f2089",
            "1eef95608e40d990",
            "d6b19e5b49886a85",
            "010306022421332",
            "3773f033ff57f4c9",
            "57a9196ca038a5ca",
            "da0231ea74a0716f",
            "933f4fa2f5ace3eb",
            "oJ5yX6xUiFSrNiqkmGepl4kbaQTw"
    });

}
