package com.shinet.core.safe.msql.service.androidlock;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.shinet.core.safe.core.constants.DeviceRetryConstants;
import com.shinet.core.safe.core.dto.DeviceRetryRecordDTO;
import com.shinet.core.safe.core.service.DeviceGyRetryRecordService;
import com.shinet.core.safe.enums.DspType;
import com.shinet.core.safe.enums.StoreNameEnums;
import com.shinet.core.safe.hsq.rsp.LcToutiaoCk;
import com.shinet.core.safe.hsq.rsp.LcUserActive;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTimeConstants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class OcpcLockService {

    @Value("${enable.ocpc.log:false}")
    private static boolean enableOcpcLog;

    @Autowired
    private DeviceGyRetryRecordService deviceGyRetryRecordService;

    public Pair<OcpcRsp,Boolean> isOcpcUser(CommonHeaderDTO commonHeaderDTO, AndroidLokReq androidLokReq, StoreNameEnums storeNameEnums, boolean useOcpc2ApiFlag){
        OcpcRsp ocpcRsp = new OcpcRsp();
        ocpcRsp.setNewUser(true);
        try {

            if (isTfChannel(storeNameEnums.channel)) {
                log.info("渠道属于投放用户，设为ocpc用户 {} {} {}", storeNameEnums.val, JSON.toJSONString(androidLokReq), " header " + JSON.toJSONString(commonHeaderDTO));
                ocpcRsp.setDsp(storeNameEnums.channel);
                ocpcRsp.setOcpc(true);
                return new Pair<OcpcRsp,Boolean>(ocpcRsp,true);
            }

            Map<String,Object> param = new HashMap<>();
            param.put("product",androidLokReq.getProduct());
            param.put("pkgChannel",commonHeaderDTO.getChannel());
            param.put("os",commonHeaderDTO.getOs().toLowerCase());
            param.put("ocpcDeviceId", MD5.create().digestHex(commonHeaderDTO.getDeviceId()));
            param.put("SourceDeviceId", commonHeaderDTO.getDeviceId());
            param.put("oaid",commonHeaderDTO.getOaid());
            param.put("androidId",commonHeaderDTO.getAndroidId());
            param.put("caid", commonHeaderDTO.getCaid());
            param.put("ua", commonHeaderDTO.getUa());
            param.put("userId", commonHeaderDTO.getUserId());

            /**
             * 增加ocpc 2.0接口
             */
            String result;
            String url;
            if (useOcpc2ApiFlag) {
                url = "http://ocpc-api.shinet-inc.com/dispense/user/event/storeGuiy2";
            } else {
                url = "http://ocpc-api.shinet-inc.com/dispense/user/event/storeGuiy";
            }
            result = HttpUtil.get(url, param);

            if (enableOcpcLog) {
                log.info("锁区ocpc接口 product:{}, url:{}, param:{}, res:{}", androidLokReq.getProduct(), url, param, result);
            }

            if (StringUtils.isNotBlank(result)){
                JSONObject jsonObject = JSONObject.parseObject(result);
                if(jsonObject.containsKey("data")){
                    JSONObject jdata = jsonObject.getJSONObject("data");
                    LcUserActive userActive = jdata.getObject("userActive", LcUserActive.class);
                    LcToutiaoCk toutiaoClick = jdata.getObject("toutiaoClick", LcToutiaoCk.class);
                    // 2022-12-26 非nosDsp 均为买量
                    Pair<OcpcRsp,Boolean> rpair = isPdOcpc(userActive,toutiaoClick,storeNameEnums);

                    return rpair;
                }else{
                    ocpcRsp.setNewUser(true);
                    ocpcRsp.setDsp("nodsp");

                    return new Pair<OcpcRsp,Boolean>(ocpcRsp,false);
                }
            }
        }catch (Exception e){
            ocpcRsp.setOcpc(true);
            ocpcRsp.setRemark("ocpc出错");
            log.error("storeGuiy ocpc出错 "+androidLokReq.getProduct()+" :",e);
        }

        return new Pair<OcpcRsp,Boolean>(ocpcRsp,true);
    }

    /**
     * 判断是否是tf渠道
     * @param channel
     * @return
     */
    public boolean isTfChannel(String channel) {
        if ((!DspType.INNER_OLD_PULL_NEW.name.equalsIgnoreCase(channel)
                && !DspType.INNER_VIDEO.name.equalsIgnoreCase(channel)
                && !DspType.INNER_DRAINAGE.name.equalsIgnoreCase(channel))
                && !"自然量".equalsIgnoreCase(channel)
                && !"neilaxin".equalsIgnoreCase(channel)
                && !"INNER_OLD_PULL".equalsIgnoreCase(channel)
                && !"huawei".equalsIgnoreCase(channel)
                && !"vivo".equalsIgnoreCase(channel)
                && !"oppo".equalsIgnoreCase(channel)
                && !"mi".equalsIgnoreCase(channel)
                && !"honor".equalsIgnoreCase(channel)
                && !"nodsp".equalsIgnoreCase(channel)
                && !"自然ALIYUN量".equalsIgnoreCase(channel)
                && !StoreNameEnums.ziran.val.equalsIgnoreCase(channel)
                && !StoreNameEnums.neilaxin.val.equalsIgnoreCase(channel)
                && !StoreNameEnums.update1.val.equalsIgnoreCase(channel)
                && !"AppStore".equalsIgnoreCase(channel)
        ) {
            return true;
        }
        return false;
    }


    private static Pair<OcpcRsp,Boolean> isPdOcpc(LcUserActive userActive, LcToutiaoCk toutiaoClick, StoreNameEnums storeNameEnums){
        OcpcRsp ocpcRsp = new OcpcRsp();
        ocpcRsp.setNewUser(true);
        if(userActive!=null){
//            if ((!DspType.INNER_OLD_PULL_NEW.name.equalsIgnoreCase(userActive.getSource())
//                    && !DspType.INNER_VIDEO.name.equalsIgnoreCase(userActive.getSource())
//                    && !DspType.INNER_DRAINAGE.name.equalsIgnoreCase(userActive.getSource()))
//                    && !"自然量".equalsIgnoreCase(userActive.getSource())
//                    && !"neilaxin".equalsIgnoreCase(userActive.getSource())
//                    && !"INNER_OLD_PULL".equalsIgnoreCase(userActive.getSource())
//                    && !"huawei".equalsIgnoreCase(userActive.getSource())
//                    && !"vivo".equalsIgnoreCase(userActive.getSource())
//                    && !"oppo".equalsIgnoreCase(userActive.getSource())
//                    && !"mi".equalsIgnoreCase(userActive.getSource())
//                    && !"honor".equalsIgnoreCase(userActive.getSource())
//                    && !"nodsp".equalsIgnoreCase(userActive.getSource())
//                    && !"自然ALIYUN量".equalsIgnoreCase(userActive.getSource())
//            ) {
//                log.info("已经激活用户 "+ JSON.toJSONString(userActive));
//                ocpcRsp.setOcpc(true);
//            }else {
//                ocpcRsp.setOcpc(false);
//            }
            if (StrUtil.isNotBlank(userActive.getGyType()) && !"INNER_OLD_PULL".equalsIgnoreCase(userActive.getSource())) {
                ocpcRsp.setOcpc(true);
            } else {
                ocpcRsp.setOcpc(false);
            }
            ocpcRsp.setNewUser(false);
            ocpcRsp.setDsp(userActive.getSource());
            if(userActive.getClickTime()!=null){
                long chour = (userActive.getCreateTime().getTime()-userActive.getClickTime().getTime())/ DateTimeConstants.MILLIS_PER_HOUR;
                long mins = (userActive.getCreateTime().getTime()-userActive.getClickTime().getTime())/ DateTimeConstants.MILLIS_PER_MINUTE;
                ocpcRsp.setCurHour(chour);
                ocpcRsp.setCurMins(mins);
            }
            ocpcRsp.setUserActive(userActive);
        }else if(toutiaoClick!=null){
            String dsp = toutiaoClick.getDsp();
            if(StringUtils.equalsIgnoreCase("nodsp",dsp)){
                ocpcRsp.setOcpc(false);
            }else{
                long chour = (System.currentTimeMillis()-toutiaoClick.getCreateTime().getTime())/ DateTimeConstants.MILLIS_PER_HOUR;
                long mins = (System.currentTimeMillis()-toutiaoClick.getCreateTime().getTime())/ DateTimeConstants.MILLIS_PER_MINUTE;
                ocpcRsp.setCurHour(chour);
                ocpcRsp.setToutiaoCk(toutiaoClick);
                ocpcRsp.setCurMins(mins);
                ocpcRsp.setOcpc(true);
            }
            ocpcRsp.setNewUser(true);
            ocpcRsp.setDsp(toutiaoClick.getDsp());
            ocpcRsp.setToutiaoCk(toutiaoClick);
        }

        Pair<OcpcRsp,Boolean> rpair = new Pair<OcpcRsp, Boolean>(ocpcRsp,ocpcRsp.isOcpc());
        return rpair;
    }

    /**
     * ios查询归因，与android大致相同，入参会有些不同，顾单独抽出一个方法
     * @param commonHeaderDTO
     * @param appId
     * @param product
     * @return
     */
    public Pair<OcpcRsp,Boolean> isOcpcUserForIos(CommonHeaderDTO commonHeaderDTO, Integer appId, String product) {
        OcpcRsp ocpcRsp = new OcpcRsp();
        ocpcRsp.setNewUser(true);
        try {
            Map<String,Object> param = new HashMap<>();
            param.put("product",product);
            param.put("pkgChannel",commonHeaderDTO.getChannel());
            param.put("os",commonHeaderDTO.getOs().toLowerCase());
            param.put("ocpcDeviceId", MD5.create().digestHex(commonHeaderDTO.getDeviceId()));
            param.put("SourceDeviceId", commonHeaderDTO.getDeviceId());
            param.put("oaid",commonHeaderDTO.getOaid());
            param.put("androidId",commonHeaderDTO.getAndroidId());
            param.put("caid", commonHeaderDTO.getCaid());
            param.put("ua", commonHeaderDTO.getUa());
            param.put("userId", commonHeaderDTO.getUserId());

            /**
             * 增加ocpc 2.0接口
             */
            String result;
            String url;
            url = "http://ocpc-api.shinet-inc.com/dispense/user/event/storeGuiy2";
            result = HttpUtil.get(url, param);

            log.info("锁区ocpc接口ios product:{} , url:{}, param:{}, res:{}", product, url, param, result);

            if (StringUtils.isNotBlank(result)){
                JSONObject jsonObject = JSONObject.parseObject(result);
                if(jsonObject.containsKey("data")){
                    JSONObject jdata = jsonObject.getJSONObject("data");
                    LcUserActive userActive = jdata.getObject("userActive", LcUserActive.class);
                    LcToutiaoCk toutiaoClick = jdata.getObject("toutiaoClick", LcToutiaoCk.class);
                    // 2022-12-26 非nosDsp 均为买量
                    Pair<OcpcRsp,Boolean> rpair = isPdOcpc(userActive,toutiaoClick, null);

                    return rpair;
                }else{
                    ocpcRsp.setNewUser(true);
                    ocpcRsp.setDsp("nodsp");

                    return new Pair<OcpcRsp,Boolean>(ocpcRsp,false);
                }
            }
        }catch (Exception e){
            ocpcRsp.setOcpc(true);
            ocpcRsp.setRemark("ocpc出错");
            log.error("storeGuiy ocpc出错 "+product+" :",e);
        }

        return new Pair<OcpcRsp,Boolean>(ocpcRsp,true);
    }

    /**
     * 触发设备归因重试记录
     * 当用户不是OCPC用户时，异步记录设备信息用于后续重试
     *
     * @param commonHeaderDTO 通用请求头信息
     * @param product 产品标识
     * @param isAndroid 是否为Android设备
     * @param userActive 用户活跃信息（用于24小时过滤）
     * @param toutiaoClick 头条点击信息（用于24小时过滤）
     */
    @Async(DeviceRetryConstants.DATA_PERSISTENCE_EXECUTOR)
    public void triggerDeviceRetryRecord(CommonHeaderDTO commonHeaderDTO, String product,
                                       boolean isAndroid, LcUserActive userActive, LcToutiaoCk toutiaoClick) {
        try {
            // 只记录24小时内的新用户
            if (!isNewUserWithin24Hours(userActive, toutiaoClick)) {
                log.debug("用户不在24小时新用户范围内，跳过记录: product={}, deviceId={}",
                         product, isAndroid ? commonHeaderDTO.getOaid() : commonHeaderDTO.getCaid());
                return;
            }

            DeviceRetryRecordDTO deviceRecord;
            if (DeviceRetryConstants.OS.ANDROID.equals(commonHeaderDTO.getOs())) {
                // Android设备：使用oaid作为设备ID
                deviceRecord = DeviceRetryRecordDTO.createAndroidRecord(product, commonHeaderDTO.getOaid());
            } else {
                // iOS设备：使用caid作为设备ID
                deviceRecord = DeviceRetryRecordDTO.createIosRecord(product, commonHeaderDTO.getCaid());
            }

            // 异步记录设备信息
            boolean success = deviceGyRetryRecordService.recordNonOcpcDevice(deviceRecord);

        } catch (Exception e) {
            log.error("{}触发设备归因重试记录异常: product={}, isAndroid={}",
                     DeviceRetryConstants.LogTag.ASYNC_RECORD, product, isAndroid, e);
        }
    }

    /**
     * 检查是否为24小时内的新用户
     *
     * @param userActive 用户激活信息
     * @param toutiaoClick 点击信息
     * @return 是否为24小时内新用户
     */
    private boolean isNewUserWithin24Hours(LcUserActive userActive, LcToutiaoCk toutiaoClick) {
        long currentTime = System.currentTimeMillis();
        long twentyFourHoursAgo = currentTime - (DeviceRetryConstants.NEW_USER_HOURS * 60 * 60 * 1000); // 24小时前

        // 检查用户活跃信息
        if (userActive != null && userActive.getCreateTime() != null) {
            return userActive.getCreateTime().getTime() >= twentyFourHoursAgo;
        }

        // 检查头条点击信息
        if (toutiaoClick != null && toutiaoClick.getCreateTime() != null) {
            return toutiaoClick.getCreateTime().getTime() >= twentyFourHoursAgo;
        }

        return true;
    }
}
