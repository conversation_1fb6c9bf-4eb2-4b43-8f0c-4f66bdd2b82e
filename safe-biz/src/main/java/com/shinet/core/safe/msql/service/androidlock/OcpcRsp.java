package com.shinet.core.safe.msql.service.androidlock;

import com.shinet.core.safe.hsq.rsp.LcToutiaoCk;
import com.shinet.core.safe.hsq.rsp.LcUserActive;
import lombok.Data;

import java.util.Date;

@Data
public class OcpcRsp {
    private boolean isOcpc;
    private LcToutiaoCk toutiaoCk;
    private LcUserActive userActive;
    private Date ctime;
    private long curHour;
    private long curMins;
    private String remark;
    private String dsp;
    private boolean isNewUser;
}
