package com.shinet.core.safe.msql.service.bytemin;

import cn.hutool.core.lang.Pair;
import com.google.common.collect.Sets;
import com.shinet.core.safe.dto.LockKeyResult;
import com.shinet.core.safe.msql.config.SafeSwitcher;
import com.shinet.core.safe.msql.entity.ByteminConf;
import com.shinet.core.safe.msql.entity.ByteminLockRst;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.service.ByteminConfService;
import com.shinet.core.safe.msql.service.ByteminLockRstService;
import com.shinet.core.safe.msql.service.LockAreaConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Component
@Slf4j
public class ByteminLockService {

    @Autowired
    ByteminLockRstService byteminLockRstService;
    @Autowired
    LockAreaConfigService lockAreaConfigService;

    public static String os="byte";
    public static String channel = "bytemin";
    @Autowired
    ByteminConfService byteminConfService;
    @Autowired
    SafeSwitcher safeSwitcher;
    public Pair<LockKeyResult,ByteminLockRst> isByteminLock(CommonHeaderDTO commonHeaderDTO, ByteminLokReq byteminLokReq, String ip){
        String product = byteminLokReq.getProduct();
        String openId = byteminLokReq.getOpenId();

        boolean isLock = true;

        commonHeaderDTO.setProduct(product);
        commonHeaderDTO.setOs(os);
        commonHeaderDTO.setOpenId(openId);
        commonHeaderDTO.setChannel(channel);//最严校验
        LockKeyResult lockKeyResult = new LockKeyResult();
        lockKeyResult.setLocked(Boolean.TRUE);
        lockKeyResult.setOcpc(Boolean.FALSE);

        if(safeSwitcher.openOpenids.contains(openId)){
            lockKeyResult.setLocked(false);
            String remark = "白名单 "+openId+"";
            log.info(remark);
            ByteminLockRst byteminLockRst = byteminLockRstService.nwByteminLockRst(product,os,openId,ip);
            byteminLockRst.setChannel("bytemin");
            byteminLockRst.setAppVersion(commonHeaderDTO.getAppVersion());
            byteminLockRst.setUserId(openId);
            byteminLockRst.setRemark(remark);
            byteminLockRstService.save(byteminLockRst);
            return new Pair<LockKeyResult,ByteminLockRst>(lockKeyResult,byteminLockRst);
        }

        ByteminConf byteminConf = byteminConfService.queryByPrj(product);
        if(byteminConf!=null && Sets.newHashSet(byteminConf.getLockVersions().split(",")).contains(byteminLokReq.getAppVersion())){
            lockKeyResult.setLocked(true);
            String remark = "bytemin全锁区 "+openId+"";
            log.info(remark);
            ByteminLockRst byteminLockRst = byteminLockRstService.nwByteminLockRst(product,os,openId,ip);
            byteminLockRst.setChannel("bytemin");
            byteminLockRst.setAppVersion(commonHeaderDTO.getAppVersion());
            byteminLockRst.setUserId(openId);
            byteminLockRst.setRemark(remark);
            byteminLockRstService.save(byteminLockRst);
            return new Pair<LockKeyResult,ByteminLockRst>(lockKeyResult,byteminLockRst);
        }else{
            String dsp = lockAreaConfigService.userIsOCPC(commonHeaderDTO, null, product);
            boolean isOcpc = !StringUtils.equalsIgnoreCase(dsp,"nodsp");
            String city = lockAreaConfigService.getCity(commonHeaderDTO,"bytemin");
            ByteminLockRst dbByteminLockRst = byteminLockRstService.queryByProductOpenId(product,openId);
            if(dbByteminLockRst!=null){
                lockKeyResult.setLocked(Boolean.parseBoolean(dbByteminLockRst.getLockFlag()));
                lockKeyResult.setOcpc(dbByteminLockRst.getIsOcpc()==1?true:false);
                log.info("bytemin锁区 "+openId+" 结果为 "+dbByteminLockRst.getRemark()+" 老数据存储");
                return new Pair<LockKeyResult,ByteminLockRst>(lockKeyResult,dbByteminLockRst);
            }else{
                ByteminLockRst byteminLockRst = byteminLockRstService.nwByteminLockRst(product,os,openId,ip);
                String remark = "";
                if(isOcpc){
                    isLock = false;
                    remark = "ocpc放开";
                }else{
                    Boolean contains = Arrays.asList("北京","成都","上海","广州","深圳").stream().anyMatch(r -> city.contains(r));
                    if(contains){
                        isLock = true;
                        remark = "锁区 "+city;
                    }else{
                        isLock = false;
                        remark = "非锁区 "+city;
                    }
                }
                byteminLockRst.setCity(city);
                byteminLockRst.setDsp(dsp);
                byteminLockRst.setIsOcpc(isOcpc?1:0);
                byteminLockRst.setChannel("bytemin");
                byteminLockRst.setAppVersion(commonHeaderDTO.getAppVersion());
                byteminLockRst.setLockFlag(isLock+"");
                byteminLockRst.setUserId(openId);
                byteminLockRst.setRemark(remark);
                byteminLockRstService.save(byteminLockRst);

                lockKeyResult.setOcpc(isOcpc);
                lockKeyResult.setLocked(isLock);

                log.info("bytemin锁区 "+openId+" 结果为 "+remark);
                return new Pair<LockKeyResult,ByteminLockRst>(lockKeyResult,byteminLockRst);
            }
        }


    }

}
