package com.shinet.core.safe.msql.service.distributionconf;

import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.shinet.core.safe.dto.LockKeyResult;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.service.androidlock.AndroidLokReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * 锁区统一下发各类配置service
 */
@Slf4j
@Service
public class LockDistributionConfigService {
    @ApolloJsonValue("${skip.event.action.list:[]}")
    private Set<String> skipEventActionList;

    public void buildDistributionConfigForAndroidLock2(CommonHeaderDTO commonHeaderDTO, AndroidLokReq androidLokReq, LockKeyResult lockKeyResult) {
        // 下发 客户端跳过上报的埋点类型列表
        lockKeyResult.setSkipEventActionList(skipEventActionList);
    }
}
