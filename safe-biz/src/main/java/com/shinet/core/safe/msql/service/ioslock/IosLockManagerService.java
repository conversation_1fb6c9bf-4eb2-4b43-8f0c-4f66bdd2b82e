package com.shinet.core.safe.msql.service.ioslock;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.shinet.core.safe.dto.LockKeyResult;
import com.shinet.core.safe.msql.config.SafeSwitcher;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.entity.IosIpRst;
import com.shinet.core.safe.msql.entity.LockIosConfig;
import com.shinet.core.safe.msql.service.IosIpRstService;
import com.shinet.core.safe.msql.service.LockAreaConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class IosLockManagerService {
    @Autowired
    LockIosConfigService lockIosConfigService;
    @Autowired
    IosLockService iosLockService;
    @Autowired
    LockAreaConfigService lockAreaConfigService;
    @Autowired
    SafeSwitcher safeSwitcher;
    @Autowired
    IosIpRstService iosIpRstService;

    private static long fourHoursInMillis = 4 * 60 * 60 * 1000;

    public Pair<Boolean,LockKeyResult> iosLockMag(String product, String appVersion,String ip, boolean isInstallWx,boolean isInstallDy,String timestamp){
        CommonHeaderDTO commonHeaderDTO = new CommonHeaderDTO();
        commonHeaderDTO.setIp(ip);
        commonHeaderDTO.setProduct(product);
        LockKeyResult lockKeyResult = new LockKeyResult();


        if (safeSwitcher.iosIpWhiteMap.getOrDefault(product,new ArrayList<>()).contains(commonHeaderDTO.getIp())){
            log.info("{} {} ios第一层白名单ip命中...",product,ip);
            lockKeyResult.setLocked(false);
            iosIpRstService.saveIosRst(product,ip,false,"","白名单ip");
            return new Pair<>(false,lockKeyResult);
        }
        IosIpRst iosIpRst = iosIpRstService.queryIosRst(product,ip);
        lockKeyResult.setIosIpRst(iosIpRst);
        String city = iosLockService.getCityAli(commonHeaderDTO,"dws");
//        String city = lockAreaConfigService.getCity(commonHeaderDTO,"dws");
        lockKeyResult.setCity(city);
        if (StringUtils.isEmpty(city)
                || (StringUtils.isNotBlank(city) && "海外".equals(city))
                || (StringUtils.isNotBlank(city) && "海外-苹果".equals(city))
                || StringUtils.contains(city,"海外")){
            lockKeyResult.setLocked(true);
            iosIpRstService.saveIosRst(product,ip,true,city,"海外直接锁");
            if(safeSwitcher.loadingLockProjects.contains(product)){
                return new Pair<>(true,lockKeyResult);
            }
            return new Pair<>(false,lockKeyResult);
        }

        //时区判断
        if (StringUtils.isNotBlank(timestamp)) {
            long clientTimestamp = Long.valueOf(timestamp);
            long serverTimeStamp = System.currentTimeMillis();
            long timeDifference = Math.abs(serverTimeStamp - clientTimestamp);
            if (timeDifference > fourHoursInMillis) {
                lockKeyResult.setLocked(true);
                iosIpRstService.saveIosRst(product, ip, true, city, "客户端与服务端时差超过四小时，时差：" + timeDifference);
                log.info("客户端与服务端时差超过四小时 {} {} {} {} 时差:{}", product, ip, city, commonHeaderDTO.getCaid(), timeDifference);
                return new Pair<>(false, lockKeyResult);
            }
        }

        if(iosIpRst!=null && iosIpRst.getLockFlag()!=null){
            log.info("命中存储ip判断结果 "+appVersion+" "+product+" "+iosIpRst.getLockFlag()+" "+ip+" "+ JSON.toJSONString(iosIpRst));
            if(iosIpRst.getLockFlag()==1){
                lockKeyResult.setLocked(true);
                boolean isLoading = false;
                if(iosIpRst.getLoadingFlg()!=null && iosIpRst.getLoadingFlg()==1){
                    isLoading = true;
                }
                return  new Pair<>(isLoading,lockKeyResult);
            }else{
                lockKeyResult.setLocked(false);
                return  new Pair<>(false,lockKeyResult);
            }
        }else{
            LockIosConfig lockIosConfig = lockIosConfigService.getIosConfig(product,null);

            if(lockIosConfig!=null){
                if(lockIosConfig.getIsDown()!=null && lockIosConfig.getIsDown()==1){
                    lockKeyResult.setLocked(false);
                    iosIpRstService.saveIosRst(product,ip,false,city,"产品下架false");
                    return new Pair<>(false,lockKeyResult);
                }
                if(lockIosConfig.getAllLock()!=null && lockIosConfig.getAllLock()==1){
                    lockKeyResult.setLocked(true);
                    iosIpRstService.saveIosRst(product,ip,true,city,"产品全锁"+lockIosConfig.getAllLock());
                    return new Pair<>(false,lockKeyResult);
                }
            }
            if(lockIosConfig!=null && StringUtils.isNotBlank(lockIosConfig.getVersionLocks())){
                List<String> verLockList = Lists.newArrayList(lockIosConfig.getVersionLocks().split(","));
                if(verLockList.contains(appVersion)){
                    lockKeyResult.setLocked(true);
                    iosIpRstService.saveIosRst(product,ip,true,city,"版本锁区"+appVersion+" "+product);
                    log.info("命中单独版本锁区逻辑 "+appVersion+" "+product);
                    return  new Pair<>(false,lockKeyResult);
                }
            }

            if(lockIosConfig!=null && lockIosConfig.getWxLock()!=null && lockIosConfig.getWxLock()==1){
                if(!isInstallWx){
                    lockKeyResult.setLocked(true);
                    iosIpRstService.saveIosRst(product,ip,true,city,"wx未安装new "+appVersion+" "+product);
                    log.info("wx未安装new1 "+appVersion+" "+product);
                    return  new Pair<>(false,lockKeyResult);
                }


                if(!isInstallDy){
                    lockKeyResult.setLocked(true);
                    iosIpRstService.saveIosRst(product,ip,true,city,"dy未安装new "+appVersion+" "+product);
                    log.info("dy未安装new1 "+appVersion+" "+product);
                    return  new Pair<>(false,lockKeyResult);
                }
            }
        }
        iosIpRstService.saveIosRst(product,ip,false,city,"");
        lockKeyResult.setLocked(false);
        return  new Pair<>(false,lockKeyResult);
    }

    /**
     * 直接添加锁区设备
     */
    public void addLock(String product, String appVersion,String ip){
        CommonHeaderDTO commonHeaderDTO = new CommonHeaderDTO();
        commonHeaderDTO.setIp(ip);
        commonHeaderDTO.setProduct(product);

        String city = iosLockService.getCity(commonHeaderDTO,"dws");

        iosIpRstService.saveIosRst(product,ip,true,city,"wx未安装");

    }
}
