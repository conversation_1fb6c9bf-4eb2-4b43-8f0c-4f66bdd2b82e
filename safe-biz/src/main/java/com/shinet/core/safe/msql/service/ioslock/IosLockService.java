package com.shinet.core.safe.msql.service.ioslock;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import com.coohua.bp.account.remote.api.AccountRPC;
import com.coohua.bp.account.remote.dto.AccountDTO;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.shinet.core.safe.dto.BasicApp;
import com.shinet.core.safe.dto.LockKeyResult;
import com.shinet.core.safe.dto.StrategyResult;
import com.shinet.core.safe.hsq.rsp.GdIpRsp;
import com.shinet.core.safe.lock.bean.CaidRegisterBean;
import com.shinet.core.safe.lock.service.LockHbaseService;
import com.shinet.core.safe.msql.config.SafeSwitcher;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.entity.LockIosConfig;
import com.shinet.core.safe.msql.entity.LockIosRst;
import com.shinet.core.safe.msql.service.*;
import com.shinet.core.safe.msql.service.DeviceBlackService;
import com.shinet.core.safe.core.vo.CommonHeaderVo;
import com.shinet.core.safe.core.vo.ALiYunIpLocationVo;
import com.shinet.core.safe.msql.service.androidlock.OcpcLockService;
import com.shinet.core.safe.msql.service.androidlock.OcpcRsp;
import com.shinet.core.safe.util.DateUtils;
import com.weibo.api.motan.config.springsupport.annotation.MotanReferer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.directory.api.util.Strings;
import org.redisson.api.RMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.shinet.core.safe.container.AppBuilder.productMap;

@Component
@Slf4j
public class IosLockService {
    @Autowired
    LockAreaConfigService lockAreaConfigService;
    @Autowired
    LockIosConfigService lockIosConfigService;
    @ApolloJsonValue("${hw.ios.caid.list:[]}")
    public List<String> caidList;
    @ApolloJsonValue("${hw.ios.product.list:[]}")
    public List<String> productList;
    @ApolloJsonValue("${ios.city.list:[]}")
    public List<String> iosCityList;
    @ApolloJsonValue("${ios.skip.city.list:[]}")
    public Set<String> iosSkipCityList;
    @ApolloJsonValue("${ios.skip.city.group.map:{}}")
    public Map<String, Integer> iosSkipCityGroupMap;
    //*************
    @ApolloJsonValue("${ip.black.list:[\"*************\"]}")
    public List<String> ipBlackList;
    @ApolloJsonValue("${lock.caid.black.list:[]}")
    public Set<String> caidBlackList;
    @Autowired
    LockIosRstService lockIosRstService;
    @Autowired
    private LockHbaseService lockHbaseService;

    @ApolloJsonValue("${lock.ios.appid.notocpc:[]}")
    private List<String> notOcpcList;
    @ApolloJsonValue("${lock.ipad.group.conf:{}}")
    private Map<String, Map<String, Object>> lockIpadGroupConfMap;

    @ApolloJsonValue("${lock.qz.ios.city.notocpc:[]}")
    private List<String> qzIosCityNotOcpcList;

    @MotanReferer(basicReferer = "bp-account")
    private AccountRPC accountRPC;

    @Autowired
    LockIosBlackService lockIosBlackService;
    @Autowired
    private LockWhiteStrategyService lockWhiteStrategyService;
    @Resource
    private OcpcLockService ocpcLockService;
    @Autowired
    private SafeSwitcher safeSwitcher;
    @Autowired
    private DeviceBlackService deviceBlackService;
    private static long fourHoursInMillis = 4 * 60 * 60 * 1000;

    public LockKeyResult iosLockCheck(CommonHeaderDTO commonHeaderDTO, Integer appId, String trans) {
        String product = commonHeaderDTO.getProduct();
//        String dsp = lockAreaConfigService.userIsOCPC(commonHeaderDTO, appId, product);
        Pair<OcpcRsp, Boolean> ocpcUserForIos = ocpcLockService.isOcpcUserForIos(commonHeaderDTO, appId, product);
        Boolean isOcpc = ocpcUserForIos.getValue();
        OcpcRsp ocpcRsp = ocpcUserForIos.getKey();
        String dsp = ocpcRsp.getDsp();
        String city = getCity(commonHeaderDTO, trans);
        Pair<String, LockKeyResult> resultPair = iosLockCheckSd(commonHeaderDTO, appId, dsp, product, city, isOcpc);
        String reason = resultPair.getKey();
        StrategyResult strategyResult = null;
        if(resultPair.getValue().getLocked()) {
            //是否为加白a
            strategyResult = lockWhiteStrategyService.getStrategyResult(product, commonHeaderDTO);
            if(null != strategyResult && strategyResult.isWhiteFlag()) {
                resultPair.getValue().setLocked(false);
                reason = strategyResult.getRemark();
            }

            try {
                if (!isOcpc) {
                    ocpcLockService.triggerDeviceRetryRecord(commonHeaderDTO, product, false, ocpcRsp.getUserActive(), ocpcRsp.getToutiaoCk());
                }
            } catch (Exception e) {
                log.error("触发设备归因重试记录异常", e);
            }
        }

        lockIosRstService.addIosRst(dsp, commonHeaderDTO.getProduct(), commonHeaderDTO.getCaid(), commonHeaderDTO.getDeviceId(), resultPair.getValue().getLocked(),
                commonHeaderDTO.getUserId() + "",
                commonHeaderDTO.getIp(), city,
                reason, commonHeaderDTO.getAppVersion());
        resultPair.getValue().setOcpc(!StringUtils.equalsIgnoreCase("nodsp", dsp));
        LockKeyResult lockKeyResult = resultPair.getValue();
        return lockKeyResult;
    }


    public Pair<String, LockKeyResult> iosLockCheckSd(CommonHeaderDTO commonHeaderDTO, Integer appId, String dsp, String product, String city, Boolean isOcpc) {
        LockKeyResult lockKeyResult = new LockKeyResult();
        lockKeyResult.setLocked(Boolean.FALSE);
        lockKeyResult.setOcpc(Boolean.FALSE);

        lockKeyResult.setCity(city);
        CaidRegisterBean caidRegisterBean = lockHbaseService.queryIfNotExistSave(product, commonHeaderDTO.getCaid());

        if (ipBlackList.contains(commonHeaderDTO.getIp())) {
            lockKeyResult.setLocked(true);
            return new Pair<String, LockKeyResult>("black", lockKeyResult.build());
        }

        // 设备拉黑统一检测
        try {
            CommonHeaderVo headerVo = new CommonHeaderVo()
                    .setBasicInfo(commonHeaderDTO.getOs(), commonHeaderDTO.getIp(), product);
            headerVo.setCaid(commonHeaderDTO.getCaid());
            headerVo.setIdfa(commonHeaderDTO.getIdfa());

            if (deviceBlackService.isDeviceBlocked(headerVo)) {
                lockKeyResult.setLocked(true);
                log.info("iOS设备拉黑检测命中：product={}, ip={}, caid={}",
                        product, commonHeaderDTO.getIp(), commonHeaderDTO.getCaid());
                return new Pair<String, LockKeyResult>("设备拉黑检测命中", lockKeyResult.build());
            }
        } catch (Exception e) {
            log.warn("iOS设备拉黑检测异常：product={}, error={}",
                    product, e.getMessage(), e);
        }

//        if (!isOcpc){
        if (!isOcpc && notOcpcList.contains(commonHeaderDTO.getAppId())) {
            if (!safeSwitcher.ipShinetZiyou.contains(commonHeaderDTO.getIp())) {
                log.info("ios产品自然量命中,caid: {} appId:{}", commonHeaderDTO.getCaid(), commonHeaderDTO.getAppId());
                lockKeyResult.setLocked(true);
                return new Pair<String, LockKeyResult>("ios自然量命中!", lockKeyResult.build());
            } else {
                log.info("公司ip自然量不锁,caid: {} appId:{}", commonHeaderDTO.getCaid(), commonHeaderDTO.getAppId());
            }
        }

        BasicApp app = productMap.get(product);

        String model = commonHeaderDTO.getModel();
        if (!safeSwitcher.ipShinetZiyou.contains(commonHeaderDTO.getIp()) && checkIpad(model, isOcpc, app)) {
                log.info("ipad设备命中, model: {} caid: {} appId:{}", commonHeaderDTO.getModel(), commonHeaderDTO.getCaid(), commonHeaderDTO.getAppId());
                lockKeyResult.setLocked(true);
                return new Pair<String, LockKeyResult>("ipad设备命中 " + model, lockKeyResult.build());
        }

        if (caidBlackList.contains(commonHeaderDTO.getCaid())) {
            lockKeyResult.setLocked(true);
            log.info("caid黑名单 " + commonHeaderDTO.getCaid());
            return new Pair<String, LockKeyResult>("caid黑名单", lockKeyResult.build());
        }

        if (lockIosBlackService.isInBlack(commonHeaderDTO.getCaid())) {
            lockKeyResult.setLocked(true);
            log.info("ios 锁区黑名单A " + commonHeaderDTO.getCaid());
            return new Pair<String, LockKeyResult>("ioslockblack", lockKeyResult.build());
        }
        if (city != null && StringUtils.isNotBlank(city) && ("海外".equals(city) || StringUtils.contains(city, "海外"))) {
            lockKeyResult.setLocked(true);
            log.info(commonHeaderDTO.getProduct() + " " + commonHeaderDTO.getIp() + " " + commonHeaderDTO.getCaid() + " ip海外，直接锁 ");
            return new Pair<String, LockKeyResult>("海外", lockKeyResult.build());
        } else {
            LockIosConfig lockIosConfig = lockIosConfigService.getIosConfig(product, appId);

            if (Sets.newHashSet(new String[]{"香港特别行政区", "中国香港", "台湾省", "中国台湾", "台北", "台北市", "台南市", "台中", "台中市", "澳门特别行政区"}).contains(city)) {
                if (!isOcpc) {
                    lockKeyResult.setLocked(true);
                    String msg = "港澳台命中全锁 " + lockIosConfig.getAllLock() + ":" + commonHeaderDTO.getProduct();
                    log.info(commonHeaderDTO.getCaid() + " " + commonHeaderDTO.getProduct() + " 港澳台命中全锁 ");
                    return new Pair<String, LockKeyResult>(msg, lockKeyResult.build());
                }
            }

            if (lockIosConfig != null && lockIosConfig.getAllLock() != null && lockIosConfig.getAllLock() == 1) {
                lockKeyResult.setLocked(true);
                String msg = "命中全锁 " + lockIosConfig.getAllLock() + ":" + commonHeaderDTO.getProduct();
                log.info("" + commonHeaderDTO.getCaid() + " " + commonHeaderDTO.getProduct() + " 命中全锁-直接锁 ");
                return new Pair<String, LockKeyResult>(msg, lockKeyResult.build());
            }

            if (lockIosConfig != null && StringUtils.isNotBlank(lockIosConfig.getVersionLocks())) {
                List<String> verLockList = Lists.newArrayList(lockIosConfig.getVersionLocks().split(","));
                if (verLockList.contains(commonHeaderDTO.getAppVersion())) {
                    lockKeyResult.setLocked(true);
                    log.info("" + commonHeaderDTO.getCaid() + " 命中版本全锁，直接锁 ");
                    return new Pair<String, LockKeyResult>("命中版本全锁，直接锁 " + lockIosConfig.getVersionLocks() + ":" + commonHeaderDTO.getAppVersion(), lockKeyResult.build());
                }
            }

            if (lockIosConfig != null && lockIosConfig.getAllOpen() != null && lockIosConfig.getAllOpen() == 1) {
                lockKeyResult.setLocked(false);
                log.info("" + commonHeaderDTO.getCaid() + " " + commonHeaderDTO.getProduct() + " 命中全开测试 ");
                return new Pair<String, LockKeyResult>("命中全开 测试 " + lockIosConfig.getAllOpen() + ":" + commonHeaderDTO.getProduct(), lockKeyResult.build());
            }

            //时区判断
            String timestamp = commonHeaderDTO.getTimestamp();
            if (StringUtils.isNotBlank(timestamp)) {
                long clientTimestamp = Long.valueOf(timestamp);
                long serverTimeStamp = System.currentTimeMillis();
                long timeDifference = Math.abs(serverTimeStamp - clientTimestamp);
                if (timeDifference > fourHoursInMillis) {
                    lockKeyResult.setLocked(true);
                    log.info("客户端与服务端时差超过四小时 {} {} {} {} 时差:{}", product, commonHeaderDTO.getIp(), city, commonHeaderDTO.getCaid(), timeDifference);
                    return new Pair<String, LockKeyResult>("客户端与服务端时差超过四小时，时差："+timeDifference, lockKeyResult.build());
                }
            }


            //用户创建时间大于cday 自动转换成非锁区
            Pair<Long, Boolean> isThanCday = userAccountCtime(commonHeaderDTO.getUserId(), commonHeaderDTO.getAppId(), caidRegisterBean);
            if (isThanCday.getValue()) {
                lockKeyResult.setLocked(false);
                log.info("" + commonHeaderDTO.getCaid() + " 创建时间大于cday " + isThanCday.getKey() + "@" + isThanCday.getValue());
                return new Pair<String, LockKeyResult>("创建时间大于 " + isThanCday.getKey() + ">" + userCrDays + "@" + isThanCday.getValue(), lockKeyResult.build());
            }
            //存储首次判断
            LockIosRst lockIosRst = lockIosRstService.queryById(product, commonHeaderDTO.getCaid());
//            isThan2Day(lockIosRst);

            if (multiProvinceCheck(product, commonHeaderDTO.getCaid(), lockIosRst, commonHeaderDTO.getIp())) {
//                lockKeyResult.setLocked(true);
                log.info("多省份检测命中，设备ID: {}, 产品: {}", commonHeaderDTO.getCaid(), product);
//                return new Pair<String, LockKeyResult>("多省份检测命中", lockKeyResult.build());
            }

            if (lockIosRst != null) {
                if ("true".equalsIgnoreCase(lockIosRst.getLockFlag())) {
                    lockKeyResult.setLocked(true);
                } else {
                    lockKeyResult.setLocked(false);
                }
                log.info(commonHeaderDTO.getProduct() + " " + commonHeaderDTO.getCaid() + " 获取到存储信息，以此判断 " + lockKeyResult.getLocked());
                return new Pair<String, LockKeyResult>(" 获取到存储信息，以此判断 " + lockKeyResult.getLocked(), lockKeyResult.build());
            }

            if (productList.contains(commonHeaderDTO.getProduct())) {
                //最严 排除所有自然量
                if (StringUtils.equalsIgnoreCase("nodsp", dsp) || !isOcpc) {
                    lockKeyResult.setLocked(true);
                    log.info(commonHeaderDTO.getProduct() + " " + commonHeaderDTO.getCaid() + " 最严格判断排除所有非投放用户 " + lockKeyResult.getLocked());
                    return new Pair<String, LockKeyResult>(" 严格判断排除所有非投放用户 " + lockKeyResult.getLocked(), lockKeyResult.build());
                }
            }

            Pair<Boolean, String> isLock = null;
            if (lockIosConfig != null) {
                if (lockIosConfig.getIsDown() != null && lockIosConfig.getIsDown() == 1) {
                    isLock = new Pair<>(false, "" + commonHeaderDTO.getCaid() + " 产品已经下线，不锁 ");
                    log.info("" + commonHeaderDTO.getCaid() + " 产品已经下线，不锁 " + commonHeaderDTO.getProduct());
                } else {
                    //开始走配置
                    Date toufangTime = lockIosConfig.getToufangTime();
                    long days = 1;
                    if (toufangTime != null) {
                        days = DateUtil.between(toufangTime, new Date(), DateUnit.DAY);
                    } else {
                        days = DateUtil.between(lockIosConfig.getCreateTime(), new Date(), DateUnit.DAY) - 6;
                    }
                    if (days < 0) {
                        days = 0;
                    }
                    isLock = isLockAllCyan(city, dsp, commonHeaderDTO.getCaid(), isOcpc, lockIosConfig.getProduct());
                    //保留旧从严逻辑给公司ip通行
                    if (days <= safeSwitcher.iosLockDays && safeSwitcher.ipShinetZiyou.contains(commonHeaderDTO.getIp())) {
                        log.info("shinetip进入正常逻辑 " + commonHeaderDTO.getIp());
                        isLock = isLockCyan(city, dsp, commonHeaderDTO.getCaid(), false, isOcpc);
                    }
                }
            } else {
                log.error("ios锁区无配置 请及时配置");
                //从严
                isLock = isLockAllCyan(city, dsp, commonHeaderDTO.getCaid(), isOcpc, lockIosConfig.getProduct());
            }
            lockKeyResult.setLocked(isLock.getKey());

            return new Pair<String, LockKeyResult>(isLock.getValue(), lockKeyResult.build());
        }
    }

    private boolean checkIpad(String model, Boolean isOcpc, BasicApp app) {
        if (StringUtils.isBlank(model)) return false;

        Map<String, Object> groupConfMap = lockIpadGroupConfMap.get(app.getProductGroup());

        if (CollUtil.isEmpty(groupConfMap)) return false;

        // true代表用户全锁，false只锁自然量
        boolean lockAll = (boolean) groupConfMap.get("lockAll");

        List<String> skipProductList = (List<String>) groupConfMap.get("skipProductList");

        if (CollUtil.isNotEmpty(skipProductList) && skipProductList.contains(app.getProduct())) return false;

        Map<String, Boolean> productMap = (Map<String, Boolean>) groupConfMap.get("productMap");

        if (CollUtil.isNotEmpty(productMap)) {
            Boolean b = productMap.get(app.getProduct());
            if (b != null) {
                if (isOcpc && !b) {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            // 若只锁自然量，放行ocpc
            if (!lockAll && isOcpc) return false;
        }

        if (model.toLowerCase().startsWith("j") && model.toLowerCase().endsWith("ap")) return true;

        if (model.toLowerCase().contains("pad")) return true;

        return false;
    }

    @Value("${safe.ios.lockdays:2}")
    public Integer lockdays = 2;
    @Value("${safe.ios.udays:2}")
    public Integer userCrDays = 2;

    @Value("${safe.ios.query.hbase.switch:false}")
    private Boolean openHBaseCreateTimeSwitch;

    public Pair<Long, Boolean> userAccountCtime(Long userId, String appId, CaidRegisterBean caidRegisterBean) {
        try {
            AccountDTO accountDTO = accountRPC.getAccountInfo(userId, Long.parseLong(appId));
            if (accountDTO == null) {
                if (StringUtils.equalsIgnoreCase("888", appId)) {
                    accountDTO = accountRPC.getAccountInfo(userId, 834L);
                }
                if (accountDTO == null) {
                    if (openHBaseCreateTimeSwitch) {
                        if (caidRegisterBean != null) {
                            Long createTime = caidRegisterBean.getCreateTime();
                            Long days = DateUtil.between(new Date(), new Date(createTime), DateUnit.DAY);
                            if (days >= userCrDays) {
                                return new Pair<Long, Boolean>(days, true);
                            } else {
                                return new Pair<Long, Boolean>(days, false);
                            }
                        }
                    }
                    return new Pair<Long, Boolean>(-1L, false);
                }
            }
            Long actime = accountDTO.getCreateTime();
            Long days = DateUtil.between(new Date(), new Date(actime), DateUnit.DAY);
            if (days >= userCrDays) {
                return new Pair<Long, Boolean>(days, true);
            } else {
                return new Pair<Long, Boolean>(days, false);
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return new Pair<Long, Boolean>(-1L, false);
    }

    public void isThan2Day(LockIosRst lockIosRst) {

        if (lockIosRst != null) {
            if (StringUtils.equalsIgnoreCase("true", lockIosRst.getLockFlag())) {
                long days = DateUtil.between(new Date(), lockIosRst.getCreateTime(), DateUnit.DAY);
                if (days >= lockdays && !StringUtils.equalsIgnoreCase("海外", lockIosRst.getRemark())) {
                    //锁时间为1Day
                    lockIosRst.setLockFlag("false");
                    lockIosRst.setRemark("首次进入时间大于" + lockdays + "天 " + lockIosRst.getRemark());
                    lockIosRstService.updateById(lockIosRst);

                    log.info("用户锁区期已过 放开 " + lockIosRst.getCaid());
                }
            }
        }

    }


    private Pair<Boolean, String> allLock(String city, String dsp, String caid, boolean isYan, String product, Boolean isOcpc) {
        String rsg = "";
        BasicApp app = productMap.get(product);
        if (Objects.nonNull(app) && ("项目七组".equals(app.getProductGroup()) && "北京市".equals(city))){
            rsg = "组别城市确定全锁 " + product + " " + app.getProductGroup() + " " + city  + " " + isYan + " " + caid;
            log.info(rsg);
            return new Pair<>(true, rsg);
        } else if (StringUtils.equalsIgnoreCase("nodsp", dsp) || !isOcpc) {
            rsg = "全锁规则最严 " + isYan + " :: " + dsp + " " + city + " ";
            log.info(product + " 全锁北京上海深圳 " + rsg + " " + caid + " " + city);
            return new Pair<>(true, rsg);
        } else {
            rsg = "ocpc用户 " + isYan + " :: " + dsp + " " + city;
            log.info(product + " ocpc用户北京上海深圳 " + rsg + " " + caid + " " + city);
            return new Pair<>(false, rsg);
        }

    }

    //从严
    private Pair<Boolean, String> isLockCyan(String city, String dsp, String caid, boolean isYan, Boolean isOcpc) {
        String rsg = "";
        if (StringUtils.equalsIgnoreCase("nodsp", dsp) || !isOcpc) {
            //自然量
            if (StringUtils.isBlank(city)) {
                if (isYan) {
                    rsg = "获取到城市为空，是否严格规则 " + isYan + " :: " + dsp + " " + city;
                    log.info("" + caid + " 获取到城市为空，且投放时间 " + dsp + " " + city);
                    return new Pair<>(true, rsg);
                } else {
                    rsg = "获取到城市为空，是否严格规则 " + isYan + " :: " + dsp + " " + city;
                    log.info("" + caid + " 获取到城市为空，且投放时间 " + isYan + " :: " + dsp + " " + city);
                    return new Pair<>(false, rsg);
                }
            } else {
                //有城市
                if (isYan) {
                    Boolean contains = Arrays.asList("海外", "北京", "上海", "深圳", "成都").stream().anyMatch(r -> city.contains(r));
                    rsg = " 获取到城市为 " + city + "，是否严格规则 " + isYan + " :: " + dsp + " " + city + " " + contains;
                    log.info("" + caid + " 获取到城市为 " + city + "，且投放时间 " + isYan + " :: " + dsp + " " + city + " " + contains);
                    return new Pair<>(contains, rsg);
                } else {
                    return new Pair<>(false, "非投放少于固定天数直接放过");
                }
            }
        } else {
            //ocpc
            rsg = "ocpc用户 " + isYan + " :: " + dsp + " " + city;
            return new Pair<>(false, rsg);
        }
    }

    //去除是否从严概念，全部从严
    public Pair<Boolean, String> isLockAllCyan(String city, String dsp, String caid, Boolean isOcpc, String product) {

        BasicApp app = productMap.get(product);
        if (Objects.nonNull(app) && ("项目七组".equals(app.getProductGroup()) && StringUtils.contains(city, "北京"))) {
            if (qzIosCityNotOcpcList.contains(product)) {
                String rsg = "组别城市全锁 " + product + " " + app.getProductGroup() + " " + city + " " + caid;
                log.info(rsg);
                return new Pair<>(true, rsg);
            } else {
                if (!isOcpc) {
                    String rsg = "组别城市自然量全锁 " + product + " " + app.getProductGroup() + " " + city + " " + caid;
                    log.info(rsg);
                    return new Pair<>(true, rsg);
                }
            }
        }

        if (StringUtils.equalsIgnoreCase("nodsp", dsp) || !isOcpc) {
            if (StringUtils.isBlank(city) || iosCityList.stream().anyMatch(r -> city.contains(r))) {
                if (iosSkipCityList.contains(product)) {
                    return new Pair<>(false, "跳过从严城市");
                }
                // 1670之前算老产品
                if (iosSkipCityGroupMap.containsKey(app.getProductGroup())
                        && app.getAppId() <= iosSkipCityGroupMap.getOrDefault(app.getProductGroup(), 1670)) {
                    return new Pair<>(false, app.getProductGroup() + "跳过从严城市");
                }
                String rsg = String.format(" 从严获取到城市为 %s %s", city, dsp);
                log.info(caid + rsg);
                return new Pair<>(true, rsg);
            }
        } else {
            String rsg = "ocpc用户 " + dsp + " " + city;
            return new Pair<>(false, rsg);
        }
        return new Pair<>(false, "非自然量锁区城市");
    }


    @Autowired
    IpService ipService;
    @Autowired
    IpAgtService ipAgtService;
    @Autowired
    IpAliYunService ipAliYunService;
    @Autowired
    LockMultiLocationRecordService lockMultiLocationRecordService;
    @Resource(name = "redissonClient2")
    private org.redisson.api.RedissonClient redissonClient;

    public String getCityAli(CommonHeaderDTO commonHeaderDTO, String trans) {
        try {
            Pair<String, String> cityd = ipAgtService.getIpPair(commonHeaderDTO.getProduct(), commonHeaderDTO.getCaid(), commonHeaderDTO.getIp());
            if (cityd != null) {
                String city = cityd.getValue();
                return city;
            }

            String city = ipService.getCityByIpPlus(commonHeaderDTO.getIp(), commonHeaderDTO.getGps());
            if (Strings.isNotEmpty(city)) {
                return city;
            }
        } catch (Exception e) {
            log.info("获取Ip失败..", e);
            // IOS若判定的确异常 直接认为是锁区
        }
        return "海外-未识别";
    }


    public String getCity(CommonHeaderDTO commonHeaderDTO, String trans) {
        try {
            String saveKey = ipService.getSaveKey(commonHeaderDTO);
            GdIpRsp gdIpRsp = ipService.getFormHBase(saveKey);
            if (gdIpRsp == null) {
                gdIpRsp = ipService.getIpLocation(commonHeaderDTO.getIp());
            }
            // 缓存
            if (StringUtils.isEmpty(gdIpRsp.getCity()) || gdIpRsp.getCity().contains("[")) {
                // 使用ipPlus
                String city = ipService.getCityByIpPlus(commonHeaderDTO.getIp(), commonHeaderDTO.getGps());
                if (Strings.isNotEmpty(city)) {
                    gdIpRsp.setCity(city);
                    log.info("[{}] Get By GD Failed Succeed Use IpPlus Get city: {}", trans, city);
                    return city;
                } else {
                    if (StringUtils.isBlank(city) || city.contains("[]")) {
                        Pair<String, String> cityd = ipAgtService.getIpPair(commonHeaderDTO.getProduct(), commonHeaderDTO.getCaid(), commonHeaderDTO.getIp());
                        if (cityd != null) {
                            city = cityd.getValue();
                            gdIpRsp.setCity(city);
                            return city;
                        }
                    }
                    log.info("[{}] Get By GD Failed IpPlus Also Failed", trans);
                }
            } else {
                return gdIpRsp.getCity();
            }
        } catch (Exception e) {
            log.info("获取Ip失败..", e);
            // IOS若判定的确异常 直接认为是锁区
        }
        return null;
    }

    public static void main(String[] args) {
        Date toufangTime = DateUtils.parse("2023-09-20", DateUtils.PATTERN_YMD);
        long days = DateUtil.between(toufangTime, new Date(), DateUnit.DAY);
        System.out.println(days);
    }

    /**
     * 多省份检测方法
     * 检测设备是否在多个省份出现，用于识别异常设备
     *
     * @param product 产品标识
     * @param caid 设备ID
     * @param lockIosRst iOS锁区结果记录
     * @param ip 当前IP地址
     * @return true-检测到多省份(触发锁区), false-单一省份或首次访问
     */
    private boolean multiProvinceCheck(String product, String caid, LockIosRst lockIosRst, String ip) {
        try {
            // 决策理由：只有lockIosRst不存在或创建时间在今天才执行检测，避免重复检测历史数据
            if (lockIosRst != null && !isToday(lockIosRst.getCreateTime())) {
                return false;
            }

            // 获取IP位置信息
            ALiYunIpLocationVo locationVo = ipAliYunService.getIpAddress(ip);
            if (locationVo == null || locationVo.getData() == null || StringUtils.isBlank(locationVo.getData().getProvince())) {
                log.warn("多省份检测：获取IP位置信息失败，ip={}", ip);
                return false;
            }

            String currentProvince = locationVo.getData().getProvince();
            String today = DateUtils.formatDateForYMDSTR(new Date());
            String redisKey = String.format("lock:multi:location:%s:ios:%s:%s", today, product, caid);

            // 获取Redis中的省份数据 - 使用Redisson
            RMap<String, String> redisMap = redissonClient.getMap(redisKey);
            Map<String, String> redisData = redisMap.readAllMap();

            if (redisData.isEmpty()) {
                // 首次访问，写入Redis并设置过期时间
                redisMap.put(ip, currentProvince);
                redisMap.expire(1, TimeUnit.DAYS);
                log.info("多省份检测：首次访问，记录省份，caid={}, ip={}, province={}", caid, ip, currentProvince);
                return false;
            }

            // 检查是否为新省份
            boolean isNewProvince = true;
            for (String existingProvince : redisData.values()) {
                if (isSameProvince(currentProvince, existingProvince)) {
                    isNewProvince = false;
                    break;
                }
            }

            // 更新Redis数据
            redisMap.put(ip, currentProvince);

            if (isNewProvince) {
                // 检测到新省份，统计总省份数量
                Set<String> uniqueProvinces = new HashSet<>(redisData.values());
                uniqueProvinces.add(currentProvince);

                int provinceCount = uniqueProvinces.size();
                if (provinceCount > 1) {
                    // 重新获取最新数据用于保存到数据库
                    Map<String, String> latestData = redisMap.readAllMap();
                    String locationInfo = com.alibaba.fastjson.JSON.toJSONString(latestData);
                    lockMultiLocationRecordService.saveOrUpdate(caid, product, "ios", provinceCount, locationInfo);

                    log.warn("多省份检测命中：caid={}, product={}, 省份数量={}, 当前省份={}",
                            caid, product, provinceCount, currentProvince);
                    return true;
                }
            }

            return false;
        } catch (Exception e) {
            log.error("多省份检测异常：caid={}, product={}, ip={}", caid, product, ip, e);
            return false;
        }
    }

    /**
     * 判断两个省份是否为同一省份
     * 支持模糊匹配：安徽、安徽省、中国安徽等视为同一省份
     */
    private boolean isSameProvince(String province1, String province2) {
        if (StringUtils.isBlank(province1) || StringUtils.isBlank(province2)) {
            return false;
        }
        // 决策理由：使用互相包含的方式判断省份相似性，处理"安徽"、"安徽省"、"中国安徽"等变体
        return province1.contains(province2) || province2.contains(province1);
    }

    /**
     * 判断日期是否为今天
     */
    private boolean isToday(Date date) {
        if (date == null) {
            return false;
        }
        return DateUtils.isEqualsSameDay(date.getTime(), System.currentTimeMillis());
    }

}
