package com.shinet.core.safe.msql.service.ioslock;

import cn.hutool.core.lang.Pair;
import com.shinet.core.safe.msql.config.SafeSwitcher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class IpAgtService {
    @Autowired
    SafeSwitcher safeSwitcher;
    public Pair<String,String> getIpPair(String product, String caid, String ip) {
        Pair<String,String>  cityd = null;
        if(safeSwitcher.reqstg==1){
            cityd = IpAliYunService.ipAdress(product,caid,ip);
            log.info("使用策略1  老版本阿里云"+cityd.getValue());
        }else{
            cityd = IpAliYunService.ipAdress2(product,caid,ip);
            log.info("使用策略2 阿里云ip"+cityd.getValue());
        }
        return cityd;
    }
}
