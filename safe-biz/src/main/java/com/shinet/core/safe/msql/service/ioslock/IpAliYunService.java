package com.shinet.core.safe.msql.service.ioslock;

import cn.hutool.core.lang.Pair;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import com.shinet.core.safe.core.vo.ALiYunIpLocationVo;
import com.shinet.core.safe.enums.StoreNameEnums;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
public class IpAliYunService {

    private static final CloseableHttpClient httpClient;

    static {
        PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();

        cm.setDefaultMaxPerRoute(150);

        cm.setMaxTotal(200);

        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(2000)       // 2秒连接超时
                .setSocketTimeout(3000)        // 3秒读取超时
                .setConnectionRequestTimeout(500) // 从池中获取连接的超时时间
                .build();

        httpClient = HttpClients.custom()
                .setConnectionManager(cm)
                .setDefaultRequestConfig(requestConfig)
                .build();
    }

    private static final Cache<String, Pair<String, String>> ipAddressCache = Caffeine.newBuilder()
            .maximumSize(10_0000)
            .expireAfterAccess(7, TimeUnit.DAYS)
            .recordStats()
            .build();

    private static final Cache<String, ALiYunIpLocationVo> aliYunIpLocationCache = Caffeine.newBuilder()
            .maximumSize(10_0000)
            .expireAfterAccess(7, TimeUnit.DAYS)
            .recordStats()
            .build();

    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    static {
        // 定期记录缓存状态
        scheduler.scheduleAtFixedRate(IpAliYunService::logCacheStats, 5, 5, TimeUnit.MINUTES);
    }


    private static void logCacheStats() {
        CacheStats stats = ipAddressCache.stats();
        log.info("\nCache Stats [ipAddressCache] - " +
                        "\nHit Rate: {}%, " +
                        "\nRequests: {}, " +
                        "\nHits: {}, " +
                        "\nMisses: {}, " +
                        "\nEvictions: {}, " +
                        "\nAvg Load Time: {} ms",
                stats.hitRate() * 100.0,
                stats.requestCount(),
                stats.hitCount(),
                stats.missCount(),
                stats.evictionCount(),
                stats.averageLoadPenalty() / 1_000_000.0);
    }

    public static Pair<String,String> ipAdress(String product, String caid, String ip) {
        return ipAndroidAdress(product, caid, ip, null);
        /*String appcode = "e8dc5f48274e47cd993f92736c931a6d";
        String url = "https://ipcity.market.alicloudapi.com/ip/city/query?ip=" + ip;
        // 200 MS 超时
        try {
            String body = HttpRequest.get(url).header("Authorization", "APPCODE " + appcode).execute().body();

            //{"msg":"成功","success":true,"code":200,"data":{"orderNo":"092522707093612514","result":
            //{"continent":"亚洲","owner":"中国联通","country":"中国","lng":"111.758228","adcode":"150000","city":"","timezone":"UTC+8","isp":"中国联通","accuracy":"省","source":"数据挖掘",
            //"asnumber":"4837","areacode":"CN","zipcode":"","radius":"1266.5013","prov":"内蒙古自治区","lat":"40.817125"}}}
            JSONObject jsonObject = JSON.parseObject(body);

            if (jsonObject.getInteger("code") == 200) {
                JSONObject rstObject = jsonObject.getJSONObject("data").getJSONObject("result");

                String countryName = rstObject.getString("country");
                String cityName = rstObject.getString("city");
                String prov = rstObject.getString("prov");
//                isp -> 苹果公司
                String isp = rstObject.getString("isp");
                if(StringUtils.equalsIgnoreCase(countryName,"中国") && StringUtils.isBlank(cityName) && StringUtils.isNotBlank(prov)){
                    cityName = prov;
//                    log.info("阿里云获取填充城市为  城市替换 "+product+ " "+caid+" "+countryName+" "+cityName+" prov:"+prov+" "+ip);
                }else{
//                    log.info("阿里云获取填充城市为 "+product+ " "+caid+" "+countryName+" "+cityName+" "+ip);
                }
                if(StringUtils.isNotBlank(countryName) && !StringUtils.equalsIgnoreCase(countryName,"中国")){
                    cityName = "海外";
                    if(StringUtils.contains(isp,"苹果")){
                        cityName = "海外-苹果";
                        log.warn("苹果开始开始审核注意 "+product+ " "+caid+" "+countryName+" "+cityName+" "+ip);
                    }
                    log.info("阿里云获取填充海外 "+product+ " "+caid+" "+countryName+" "+cityName+" "+ip);
                }
                return new Pair<>(countryName,cityName);
            }else{
                log.error("阿里云ip定位服务异常 "+body+" "+ip);
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return null;*/
    }

    private static Map<String,Pair<String,String>> ipAndroidAdressCache = new HashMap<>();

    public static Pair<String,String> ipAndroidAdress3(String product, String caid, String ip,String channel) {
        String appcode = "e8dc5f48274e47cd993f92736c931a6d";
        if(ipAndroidAdressCache.containsKey(ip)){
            log.info("ipAndroidAdress 缓存 String product : {}, String caid : {}, String ip : {},String channel : {}", product, caid, ip, channel);
            return ipAndroidAdressCache.get(ip);
        }else{
            log.info("ipAndroidAdress 调用 String product : {}, String caid : {}, String ip : {},String channel : {}", product, caid, ip, channel);
        }

        String url = "https://ipcity.market.alicloudapi.com/ip/city/query?ip=" + ip;
        String body = null;
        // 200 MS 超时
        try {
            body = HttpRequest.get(url).header("Authorization", "APPCODE " + appcode).execute().body();

            //{"msg":"成功","success":true,"code":200,"data":{"orderNo":"092522707093612514","result":
            //{"continent":"亚洲","owner":"中国联通","country":"中国","lng":"111.758228","adcode":"150000","city":"","timezone":"UTC+8","isp":"中国联通","accuracy":"省","source":"数据挖掘",
            //"asnumber":"4837","areacode":"CN","zipcode":"","radius":"1266.5013","prov":"内蒙古自治区","lat":"40.817125"}}}
            JSONObject jsonObject = JSON.parseObject(body);

            if (jsonObject.getInteger("code") == 200) {
                JSONObject rstObject = jsonObject.getJSONObject("data").getJSONObject("result");

                String countryName = rstObject.getString("country");
                String cityName = rstObject.getString("city");
                String prov = rstObject.getString("prov");
//                isp -> 苹果公司
                String isp = rstObject.getString("isp");
                if(StringUtils.equalsIgnoreCase(countryName,"中国") && StringUtils.isBlank(cityName) && StringUtils.isNotBlank(prov)){
                    cityName = prov;
//                    log.info("阿里云获取填充城市为  城市替换 "+product+ " "+caid+" "+countryName+" "+cityName+" prov:"+prov+" "+ip);
                }else{
//                    log.info("阿里云获取填充城市为 "+product+ " "+caid+" "+countryName+" "+cityName+" "+ip);
                }
                if(StringUtils.isNotBlank(countryName) && !StringUtils.equalsIgnoreCase(countryName,"中国")){
                    cityName = "海外";
                    if(StringUtils.contains(isp,"苹果")){
                        cityName = "海外-苹果";
                        log.warn("苹果开始开始审核注意 "+product+ " "+caid+" "+countryName+" "+cityName+" "+ip);
                    }
                    log.info("阿里云获取填充海外 "+product+ " "+caid+" "+countryName+" "+cityName+" "+ip);
                }

                if(rstObject.containsKey("owner")){
                    if(StringUtils.equalsIgnoreCase(countryName,"中国") && StringUtils.equalsIgnoreCase("华为",rstObject.getString("owner"))
                            && StringUtils.contains(channel, StoreNameEnums.huawei.val)){
                        countryName = "华为-"+countryName;
                        log.info("huawei渠道华为供应商 "+countryName+"");
                    }
                }
                Pair<String, String> res = new Pair<>(countryName, cityName);
                ipAndroidAdressCache.put(ip, res);
                return res;
            }else{
                log.error("阿里云ip定位服务异常 "+body+" "+ip);
            }
        } catch (Exception e) {
            log.error("body  : {} ipAndroidAdress异常 ", body, e);
        }
        return null;
    }
    public static Pair<String,String> ipAdress2(String product, String caid, String ip) {
        String appcode = "e8dc5f48274e47cd993f92736c931a6d";
        String url = "https://c2ba.api.huachen.cn/ip?ip=" + ip;
        // 200 MS 超时
        try {
            String body = HttpRequest.get(url).header("Authorization", "APPCODE " + appcode).execute().body();

            //{"msg":"成功","success":true,"code":200,"data":{"orderNo":"092522707093612514","result":
            //{"continent":"亚洲","owner":"中国联通","country":"中国","lng":"111.758228","adcode":"150000","city":"","timezone":"UTC+8","isp":"中国联通","accuracy":"省","source":"数据挖掘",
            //"asnumber":"4837","areacode":"CN","zipcode":"","radius":"1266.5013","prov":"内蒙古自治区","lat":"40.817125"}}}
            //{"ret":200,"msg":"success","data":{"ip":"***************","long_ip":"2028397536","isp":"中国移动","area":"华南","region_id":"440000","region":"广东","city_id":"441700","city":"阳江","district":"阳春市","district_id":"441781","country_id":"CN","country":"中国","lat":"22.170438","lng":"111.791539"},"log_id":"c9f51b6c01164b0887873bb72ea48d46"}
            JSONObject jsonObject = JSON.parseObject(body);

            if (jsonObject.getInteger("ret") == 200) {
                JSONObject rstObject = jsonObject.getJSONObject("data");

                String countryName = rstObject.getString("country");
                String cityName = rstObject.getString("city");
                String prov = rstObject.getString("prov");
//                isp -> 苹果公司
                String isp = rstObject.getString("isp");
                if(StringUtils.equalsIgnoreCase(countryName,"中国") && StringUtils.isBlank(cityName) && StringUtils.isNotBlank(prov)){
                    cityName = prov;
//                    log.info("阿里云获取填充城市为  城市替换 "+product+ " "+caid+" "+countryName+" "+cityName+" prov:"+prov+" "+ip);
                }else{
//                    log.info("阿里云获取填充城市为 "+product+ " "+caid+" "+countryName+" "+cityName+" "+ip);
                }
                if(StringUtils.isNotBlank(countryName) && !StringUtils.equalsIgnoreCase(countryName,"中国")){
                    cityName = "海外";
                    if(StringUtils.contains(isp,"苹果")){
                        cityName = "海外-苹果";
                        log.warn("苹果开始开始审核注意 "+product+ " "+caid+" "+countryName+" "+cityName+" "+ip);
                    }
                    log.info("阿里云获取填充海外 "+product+ " "+caid+" "+countryName+" "+cityName+" "+ip);
                }
                return new Pair<>(countryName,cityName);
            }else{
                log.error("阿里云ip定位服务异常 "+body+" "+ip);
            }
        } catch (Exception e) {
            log.error("", e);
        }
        return null;
    }

    public static Pair<String,String> ipAndroidAdress(String product, String caid, String ip,String channel) {
        if(ipAndroidAdressCache.containsKey(ip)){
            log.info("ipAndroidAdress 缓存 String product : {}, String caid : {}, String ip : {},String channel : {}", product, caid, ip, channel);
            return ipAndroidAdressCache.get(ip);
        }else{
            log.info("ipAndroidAdress 调用 String product : {}, String caid : {}, String ip : {},String channel : {}", product, caid, ip, channel);
        }

        String host = "https://kzipglobal.market.alicloudapi.com";
        String path = "/api/ip/query";
        String method = "POST";
        String appcode = "e8dc5f48274e47cd993f92736c931a6d";
        Map<String, String> headers = new HashMap<String, String>();
        //最后在header中的格式(中间是英文空格)为Authorization:APPCODE 83359fd73fe94948385f570e3c139105
        headers.put("Authorization", "APPCODE " + appcode);
        //根据API的要求，定义相对应的Content-Type
        headers.put("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        Map<String, String> querys = new HashMap<String, String>();
        Map<String, String> bodys = new HashMap<String, String>();
        bodys.put("ip", ip);
        try {
            /**
             * 重要提示如下:
             * HttpUtils请从
             * https://github.com/aliyun/api-gateway-demo-sign-java/blob/master/src/main/java/com/aliyun/api/gateway/demo/util/HttpUtils.java
             * 下载
             *
             * 相应的依赖请参照
             * https://github.com/aliyun/api-gateway-demo-sign-java/blob/master/pom.xml
             */
            HttpResponse response = HttpUtils.doPost(host, path, method, headers, querys, bodys);
            //获取response的body
//            System.out.println(EntityUtils.toString(response.getEntity()));
            String body = EntityUtils.toString(response.getEntity());
            JSONObject jsonObject = JSON.parseObject(body);

            if (jsonObject.getInteger("code") == 200) {
                JSONObject rstObject = jsonObject.getJSONObject("data");

                String countryName = rstObject.getString("nation");
                String cityName = rstObject.getString("city");
                String prov = rstObject.getString("province");
//                isp -> 苹果公司
                String isp = rstObject.getString("isp");
                if(StringUtils.equalsIgnoreCase(countryName,"中国") && StringUtils.isBlank(cityName) && StringUtils.isNotBlank(prov)){
                    cityName = prov;
//                    log.info("阿里云获取填充城市为  城市替换 "+product+ " "+caid+" "+countryName+" "+cityName+" prov:"+prov+" "+ip);
                }else{
//                    log.info("阿里云获取填充城市为 "+product+ " "+caid+" "+countryName+" "+cityName+" "+ip);
                }
                if(StringUtils.isNotBlank(countryName) && !StringUtils.equalsIgnoreCase(countryName,"中国")){
                    cityName = "海外";
                    if(StringUtils.contains(isp,"苹果")){
                        cityName = "海外-苹果";
                        log.warn("苹果开始开始审核注意 "+product+ " "+caid+" "+countryName+" "+cityName+" "+ip);
                    }
                    log.info("阿里云获取填充海外 "+product+ " "+caid+" "+countryName+" "+cityName+" "+ip);
                }

                if(rstObject.containsKey("owner")){
                    if(StringUtils.equalsIgnoreCase(countryName,"中国") && StringUtils.equalsIgnoreCase("华为",rstObject.getString("owner"))
                            && StringUtils.contains(channel, StoreNameEnums.huawei.val)){
                        countryName = "华为-"+countryName;
                        log.info("huawei渠道华为供应商 "+countryName+"");
                    }
                }
                Pair<String, String> res = new Pair<>(countryName, cityName);
                ipAndroidAdressCache.put(ip, res);
                return res;
            }else{
                log.error("阿里云ip定位服务异常 "+body+" "+ip);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static final String HOST = "https://kzipglobal.market.alicloudapi.com";
    public static final String PATH = "/api/ip/query";
    public static final String METHOD = "POST";
    public static final String APP_CODE = "e8dc5f48274e47cd993f92736c931a6d";

    public static Pair<String, String> getIpAddress(String product, String caid, String ip, String channel) {
        if (StringUtils.isBlank(ip)) {
            return null;
        }
        try {
            return ipAddressCache.get(ip, k -> {
                log.info("ipAddressCache miss. Fetching from API for IP: {}. Context: product={}, caid={}, channel={}", k, product, caid, channel);
                try {
                    return fetchIpAddressFromApi(k, product, caid, channel);
                } catch (Exception e) {
                    log.error("Error fetching IP address from API for IP: {}, product: {}, caid: {}, channel: {}", k, product, caid, channel, e);
                    return null;
                }
            });
        } catch (Exception e) {
            log.error("Failed to get IP address for: {}, product: {}, caid: {}", ip, product, caid, e);
            return null;
        }
    }

    private static Pair<String, String> fetchIpAddressFromApi(String ip , String product, String caid, String channel) throws Exception {
        HttpPost httpPost = new HttpPost(HOST + PATH);
        httpPost.setHeader("Authorization", "APPCODE " + APP_CODE);
        httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        List<BasicNameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("ip", ip));
        httpPost.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            HttpEntity entity = response.getEntity();
            String body = EntityUtils.toString(entity);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                JSONObject jsonObject = JSON.parseObject(body);
                if (jsonObject.getInteger("code") == 200) {
                    JSONObject rstObject = jsonObject.getJSONObject("data");

                    String countryName = rstObject.getString("nation");
                    String cityName = rstObject.getString("city");
                    String prov = rstObject.getString("province");
//                isp -> 苹果公司
                    String isp = rstObject.getString("isp");
                    if(StringUtils.equalsIgnoreCase(countryName,"中国") && StringUtils.isBlank(cityName) && StringUtils.isNotBlank(prov)){
                        cityName = prov;
                    }
                    if(StringUtils.isNotBlank(countryName) && !StringUtils.equalsIgnoreCase(countryName,"中国")){
                        cityName = "海外";
                        if(StringUtils.contains(isp,"苹果")){
                            cityName = "海外-苹果";
                            log.warn("苹果开始开始审核注意 "+product+ " "+caid+" "+countryName+" "+cityName+" "+ip);
                        }
                        log.info("阿里云获取填充海外 "+product+ " "+caid+" "+countryName+" "+cityName+" "+ip);
                    }

                    if(rstObject.containsKey("owner")){
                        if(StringUtils.equalsIgnoreCase(countryName,"中国") && StringUtils.equalsIgnoreCase("华为",rstObject.getString("owner"))
                                && StringUtils.contains(channel, StoreNameEnums.huawei.val)){
                            countryName = "华为-"+countryName;
                            log.info("huawei渠道华为供应商 "+countryName+"");
                        }
                    }
                    Pair<String, String> res = new Pair<>(countryName, cityName);
                    return res;
                }else{
                    log.error("阿里云ip定位服务异常 "+body+" "+ip);
                }
            } else {
                log.error("HTTP request failed for IP: {}. Status: {}, Response: {}", ip, statusCode, body);
                return null;
            }
        }
        return null;
    }



    /**
     * todo 后续优化
     * @param ip
     * @return
     */
    public ALiYunIpLocationVo getIpAddress(String ip) {
        return aliYunIpLocationCache.get(ip, k -> {
            log.info("aliYunIpLocationCache miss. Fetching from API for IP: {}", k);
            try {
                return getLocationFromALiYun(k);
            } catch (Exception e) {
                log.error("Error fetching IP address from API for IP: {}", k, e);
                return null;
            }
        });
    }

    private ALiYunIpLocationVo getLocationFromRedis(String ip) {

    }

    private ALiYunIpLocationVo getLocationFromALiYun(String ip) {
        HttpPost httpPost = new HttpPost(HOST + PATH);
        httpPost.setHeader("Authorization", "APPCODE " + APP_CODE);
        httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded; charset=UTF-8");
        List<BasicNameValuePair> params = new ArrayList<>();
        params.add(new BasicNameValuePair("ip", ip));
        httpPost.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));
        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            HttpEntity entity = response.getEntity();
            String body = EntityUtils.toString(entity);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode == 200) {
                ALiYunIpLocationVo resVo = JSON.parseObject(body, ALiYunIpLocationVo.class);
                if (resVo != null && Integer.valueOf(200).equals(resVo.getCode())) {
                    return res;
                } else {
                    log.error("阿里云ip定位服务异常 "+body+" "+ip);
                }
            } else {
                log.error("HTTP request failed for IP: {}. Status: {}, Response: {}", ip, statusCode, body);
                return null;
            }
        }
        return null;
    }

    public static void main(String[] args){
        //************* 空
        //*************** bj
        //'*************','*************'
        String[] strs = new String[]{
                "**************"/*,
                "***************",
                "************",
                "***************",
                "**************",
                "************",
                "**************",
                "***************",
                "***************",
                "**************",*/
        };

        for(String strd :strs){
            Pair<String,String>  dpair = ipAdress("","",strd);

            System.out.println(dpair.getKey()+"                "+dpair.getValue()+"            "+strd);
        }
    }

}
