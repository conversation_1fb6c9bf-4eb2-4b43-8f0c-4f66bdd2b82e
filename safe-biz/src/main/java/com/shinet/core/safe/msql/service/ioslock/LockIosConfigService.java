package com.shinet.core.safe.msql.service.ioslock;

import com.baomidou.mybatisplus.extension.service.additional.query.impl.LambdaQueryChainWrapper;
import com.shinet.core.safe.msql.entity.LockIosConfig;
import com.shinet.core.safe.msql.entity.LockedAreaRecordAu;
import com.shinet.core.safe.msql.mapper.LockIosConfigMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 锁区IOS特殊策略 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@Service
public class LockIosConfigService extends ServiceImpl<LockIosConfigMapper, LockIosConfig> {
    public LockIosConfig getIosConfig(String product, Integer appId) {
        LambdaQueryChainWrapper<LockIosConfig> lab = lambdaQuery();

        if (StringUtils.isNotBlank(product)) {
            lab.eq(LockIosConfig::getProduct, product);
        } else {
            lab.eq(LockIosConfig::getAppId, appId);
        }
        List<LockIosConfig> lockIosConfigList = lab.list();
        if (lockIosConfigList.size() > 0) {
            return lockIosConfigList.get(0);
        }
        return null;
    }

    /**
     * select `name`,remark,app_id from product where remark in ('停个车','猪猪快跑','停车大师','今晚娶貂蝉','菜鸟爱消消','挖宝成首富','李逍遥的客栈','真香大饭店','方块拼一拼ios','我家猫舍ios')
     */
    public void initData() {
        try {
            List<String> list = FileUtils.readLines(new File("D:\\33.txt"), "UTF-8");

            list.forEach(a -> {
                String product = a.split(",")[0];
                String productName = a.split(",")[1];
                String appId = a.split(",")[2];
                LockIosConfig lockIosConfig = getIosConfig(product,null);
                if(lockIosConfig==null){
                    lockIosConfig = new LockIosConfig();
                    lockIosConfig.setCreateTime(new Date());
                    lockIosConfig.setUpdateTime(new Date());
                    lockIosConfig.setProduct(product);
                    lockIosConfig.setProductName(productName);
                    lockIosConfig.setAppId(Integer.parseInt(appId));
                    lockIosConfig.setLockHw(1);
                    lockIosConfig.setLockBj(0);
                    save(lockIosConfig);
                }
            });
        } catch (Exception e) {

        }
    }
}
