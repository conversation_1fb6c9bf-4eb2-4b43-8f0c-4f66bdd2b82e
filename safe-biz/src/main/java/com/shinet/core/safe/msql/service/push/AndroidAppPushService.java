package com.shinet.core.safe.msql.service.push;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.shinet.core.safe.msql.entity.AndroidAppPushEntity;
import com.shinet.core.safe.msql.entity.AndroidAppPushOppoEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * push 配置
 */
@Service
@Slf4j
public class AndroidAppPushService {

    private static final String PUSH_URL = "http://oppo-push.hnshamu.com/push/message/oppoPushKeyList";
    private static volatile ConcurrentHashMap<String, AndroidAppPushOppoEntity> OPPO_PUSH_DATA = new ConcurrentHashMap<>(1024);

    public List<AndroidAppPushOppoEntity> queryAllPushConfig() {
        String res = HttpRequest.post(PUSH_URL)
                .header("Content-Type", "application/json")
                .body("")
                .execute()
                .body();

        if (StrUtil.isBlank(res)) {
            log.error("oppoPushKeyList 无返回, url: {}", PUSH_URL);
            return null;
        }

        JSONObject jsonObject = JSONObject.parseObject(res);

        if (jsonObject == null || jsonObject.size() == 0) {
            log.error("oppoPushKeyList 无返回, url: {}", PUSH_URL);
            return null;
        }

        if (!jsonObject.containsKey("status") || jsonObject.getIntValue("status") != 200) {
            log.error("oppoPushKeyList 接口返回失败, url: {}, content: {}", PUSH_URL, res);
            return null;
        }

        JSONArray jsonArray = jsonObject.getJSONArray("data");

        if (jsonArray == null || jsonArray.size() == 0) {
            log.error("oppoPushKeyList 无配置数据, url: {}, content: {}", PUSH_URL, res);
            return null;
        }

        return jsonArray.toJavaList(AndroidAppPushOppoEntity.class);
    }

    @PostConstruct
    @Scheduled(cron = "0 0 */1 * * *")
    public void refreshPushData() {
        try {
            List<AndroidAppPushOppoEntity> androidAppPushOppoEntities = queryAllPushConfig();

            if (androidAppPushOppoEntities == null) {
                log.warn("无push数据");
                return;
            }

            ConcurrentHashMap<String, AndroidAppPushOppoEntity> newPushData = new ConcurrentHashMap<>(androidAppPushOppoEntities.size() * 2);

            for (AndroidAppPushOppoEntity entity : androidAppPushOppoEntities) {
                entity.setType("oppo");
                newPushData.put(entity.getProduct(), entity);
            }

            OPPO_PUSH_DATA = newPushData;
        } catch (Exception e) {
            log.error("refresh push data error: {}", e.getMessage(), e);
        }
    }

    public List<AndroidAppPushEntity> getPushConfig(String product) {
        List<AndroidAppPushEntity> res = new ArrayList<>();

        AndroidAppPushOppoEntity pushOppo = OPPO_PUSH_DATA.get(product);

        if (pushOppo != null) {
            res.add(pushOppo);
        }

        return res;
    }
}
