package com.shinet.core.safe.msql.service.quickapp;

import cn.hutool.core.util.StrUtil;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.enums.AndroidVersionEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class QuickAppConfigService {

    @ApolloJsonValue("${check.quick.app.android.version.list:[]}")
    private List<String> checkAndoridVersionList;

    public boolean enableCheckQuickApp(String osLevel){

        try {
            if (StrUtil.isBlank(osLevel)) return false;

            AndroidVersionEnum androidVersionEnum = AndroidVersionEnum.getByApi(osLevel);

            if (androidVersionEnum == null) return false;

            return checkAndoridVersionList.contains(androidVersionEnum.getVersion());
        } catch (Exception e) {
            log.error("安卓快应用os错误 {}", e.getMessage(), e);
        }

        return false;
    }

    /**
     * 暂默认false，之后可能会适配逻辑
     * @param commonHeaderDTO
     * @return
     */
    public Boolean enableAndroidInterceptQuickApp(CommonHeaderDTO commonHeaderDTO) {

        // 默认false
        return false;
    }
}
