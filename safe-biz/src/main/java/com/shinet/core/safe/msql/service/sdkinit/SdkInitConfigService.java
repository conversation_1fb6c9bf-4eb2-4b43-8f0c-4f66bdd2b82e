package com.shinet.core.safe.msql.service.sdkinit;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.shinet.core.safe.container.AppBuilder;
import com.shinet.core.safe.core.dto.AbTestRequestDTO;
import com.shinet.core.safe.core.enums.SdkTypeEnum;
import com.shinet.core.safe.core.service.AbTestService;
import com.shinet.core.safe.dto.BasicApp;
import com.shinet.core.safe.dto.LockKeyResult;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.service.androidlock.AndroidLokReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class SdkInitConfigService {
    @Value("${enable.sdk.init.ab:false}")
    public boolean enableSdkInitAb;
    @ApolloJsonValue("${sdk.init.product.config:{}}")
    public Map<String, Integer> sdkInitProductConfig;

    @Autowired
    private AbTestService abTestService;

    public static final String DEFAULT_CONFIG_NAME = "default";

    /**
     * 获取产品sdk初始化配置
     *
     * @param commonHeaderDTO
     * @param androidLokReq
     * @param lockKeyResult
     */
    public void getProductSdkConfig(CommonHeaderDTO commonHeaderDTO, AndroidLokReq androidLokReq, LockKeyResult lockKeyResult) {
        try {
            String product = androidLokReq.getProduct();
            if (StrUtil.isBlank(product)) return;

            if (sdkInitProductConfig.containsKey(product)) {
                lockKeyResult.setSdkInitType(sdkInitProductConfig.get(product));
            } else {
                lockKeyResult.setSdkInitType(sdkInitProductConfig.getOrDefault(DEFAULT_CONFIG_NAME, SdkTypeEnum.SDK_V1.getCode()));
            }

            // AB实验集成
            if (enableSdkInitAb) {
                try {
                    AbTestRequestDTO abTestRequest = buildAbTestRequest(commonHeaderDTO, androidLokReq);
                    Integer abTestCategoryId = abTestService.getAbTestCategoryId(abTestRequest);

                    if (abTestCategoryId != null && abTestCategoryId != 0) {
                        SdkTypeEnum sdkTypeEnum = SdkTypeEnum.getByCode(abTestCategoryId);
                        if (sdkTypeEnum != null) {
                            lockKeyResult.setSdkInitType(sdkTypeEnum.getCode());
                        }
                        log.info("AB实验分组生效，产品: {}, 分组: {}", product, abTestCategoryId);
                    }
                } catch (Exception e) {
                    log.error("AB实验获取分组异常，使用默认配置，产品: {}", product, e);
                }
            }
        } catch (Exception e) {
            log.error("getProductSdkConfig error", e);
        }
    }

    /**
     * 构建AB实验请求参数
     *
     * @param commonHeaderDTO 通用请求头
     * @param androidLokReq   Android锁区请求
     * @return AB实验请求参数
     */
    private AbTestRequestDTO buildAbTestRequest(CommonHeaderDTO commonHeaderDTO, AndroidLokReq androidLokReq) {

        if (commonHeaderDTO.getAppId() == null) {
            BasicApp basicApp = AppBuilder.getByProduct(commonHeaderDTO.getProduct());
            if (basicApp != null && basicApp.getAppId() != 0) {
                commonHeaderDTO.setAppId(String.valueOf(basicApp.getAppId()));
            } else {
                log.warn("AbTestRequest未找到产品对应的 appId {}", JSONObject.toJSONString(commonHeaderDTO));
                commonHeaderDTO.setAppId("0");
            }
        }
        return AbTestRequestDTO.builder()
                .deviceId(commonHeaderDTO.getDeviceId())
                .userId(String.valueOf(commonHeaderDTO.getUserId()))
                .appVersion(commonHeaderDTO.getAppVersion())
                .os(commonHeaderDTO.getOs())
                .channel(commonHeaderDTO.getChannel())
                .appId(commonHeaderDTO.getAppId())
                .sdkVersion(commonHeaderDTO.getSdkVersion())
                .brand(commonHeaderDTO.getBrand())
                .oaid(commonHeaderDTO.getOaid())
                .caid(commonHeaderDTO.getCaid())
                .androidId(commonHeaderDTO.getAndroidId())
                .idfa(null) // Android请求无IDFA
                .idfv(null) // Android请求无IDFV
                .product(androidLokReq.getProduct())
                .build();
    }


}
