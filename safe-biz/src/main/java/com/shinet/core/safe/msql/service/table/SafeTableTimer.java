package com.shinet.core.safe.msql.service.table;

import com.shinet.core.safe.msql.service.MysqlTableService;
import com.shinet.core.safe.msql.service.ScreenRecordLogServiceImpl;
import com.shinet.core.safe.msql.service.UserExpInfoService;
import com.shinet.core.safe.msql.service.androidlock.AndroidLockRstService;
import com.shinet.core.safe.msql.service.usrpkgs.UserPkgsService;
import com.shinet.core.safe.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SafeTableTimer {
    @Autowired
    MysqlTableService mysqlTableService;

    @Autowired
    private RedisUtils redisUtils;

    @Scheduled(cron = "0 58 23 * * ?")
    public void renameTables(){

        if(redisUtils.tryLock("gpt.lock1","haha","500")){
            mysqlTableService.renameTable(AndroidLockRstService.abdlockRstTname,60);
            mysqlTableService.renameTable(UserPkgsService.ustname,3);
            mysqlTableService.renameTable(UserExpInfoService.ustname,3);
            mysqlTableService.renameTable(ScreenRecordLogServiceImpl.recordLogTableName,10);
        }

    }
}
