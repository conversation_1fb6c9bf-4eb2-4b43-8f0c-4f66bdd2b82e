package com.shinet.core.safe.msql.service.usrpkgs;

import com.shinet.core.safe.msql.entity.UserPkgs;
import com.shinet.core.safe.msql.mapper.UserPkgsMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.safe.msql.service.MysqlTableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.ThreadPoolExecutor;

/**
* <p>
    *  服务实现类
    * </p>
*
* <AUTHOR>
* @since 2024-04-12
*/
@Slf4j
@Service
public class UserPkgsService extends ServiceImpl<UserPkgsMapper, UserPkgs> {

    private static final ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();

    public UserPkgsService() {
        taskExecutor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
        taskExecutor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2);
        taskExecutor.setQueueCapacity(5000);
        taskExecutor.setKeepAliveSeconds(60);
        taskExecutor.setThreadNamePrefix("UserPkgsService-");
        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        taskExecutor.initialize();
    }


    @Autowired
    MysqlTableService mysqlTableService;
    public static String ustname = "user_pkgs";
    public void savePkgs(UserPkgs userPkgs){
        // 异步处理
        taskExecutor.execute(() -> {
            try {
                mysqlTableService.createIfNot(ustname);
                userPkgs.setCreateTime(new Date());
                userPkgs.setUpdateTime(new Date());
                save(userPkgs);
            } catch (Exception e) {
                log.error("Error saving user package info", e);
            }
        });
    }
}
