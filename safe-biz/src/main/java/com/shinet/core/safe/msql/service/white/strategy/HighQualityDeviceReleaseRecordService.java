package com.shinet.core.safe.msql.service.white.strategy;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.safe.container.AppBuilder;
import com.shinet.core.safe.core.constants.DeviceRetryConstants;
import com.shinet.core.safe.dto.BasicApp;
import com.shinet.core.safe.dto.StrategyResult;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.entity.HighQualityDeviceReleaseRecord;
import com.shinet.core.safe.msql.enums.WhiteReasonTypeEnum;
import com.shinet.core.safe.msql.mapper.HighQualityDeviceReleaseRecordMapper;
import com.shinet.core.safe.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Slf4j
public class HighQualityDeviceReleaseRecordService extends ServiceImpl<HighQualityDeviceReleaseRecordMapper, HighQualityDeviceReleaseRecord> {


    @Autowired
    private HighQualityDeviceReleaseRecordMapper highQualityDeviceReleaseRecordMapper;

    @Async(DeviceRetryConstants.DATA_PERSISTENCE_EXECUTOR)
    public void insertRecord(CommonHeaderDTO commonHeaderDTO, StrategyResult result) {
        if (commonHeaderDTO.getAppId() == null) {
            BasicApp basicApp = AppBuilder.getByProduct(commonHeaderDTO.getProduct());
            if (basicApp != null && basicApp.getAppId() != 0) {
                commonHeaderDTO.setAppId(String.valueOf(basicApp.getAppId()));
            } else {
                log.warn("记录锁区高价值释放期间未找到产品对应的 appId {}", JSONObject.toJSONString(commonHeaderDTO));
                commonHeaderDTO.setAppId("0");
            }
        }
        Date date = new Date();
        String logday = DateUtils.formatDateForYMD(date);
        HighQualityDeviceReleaseRecord highQualityDeviceReleaseRecord = new HighQualityDeviceReleaseRecord();
        if("ios".equalsIgnoreCase(commonHeaderDTO.getOs())){
            highQualityDeviceReleaseRecord.setDeviceType(WhiteReasonTypeEnum.caid.toString());
            highQualityDeviceReleaseRecord.setDeviceId(commonHeaderDTO.getCaid());
        }else {
            if (StringUtils.isNotBlank(commonHeaderDTO.getOaid())){
                highQualityDeviceReleaseRecord.setDeviceType(WhiteReasonTypeEnum.oaid.toString());
                highQualityDeviceReleaseRecord.setDeviceId(commonHeaderDTO.getOaid());
            }else {
                highQualityDeviceReleaseRecord.setDeviceType(result.getReasonType());
                highQualityDeviceReleaseRecord.setDeviceId(result.getResDeviceId());
            }
        }
        highQualityDeviceReleaseRecord.setOs(commonHeaderDTO.getOs());
        highQualityDeviceReleaseRecord.setAppId(Integer.valueOf(commonHeaderDTO.getAppId()));
        highQualityDeviceReleaseRecord.setProduct(commonHeaderDTO.getProduct());
        highQualityDeviceReleaseRecord.setStrategy(result.getReason().getStrategy());
        highQualityDeviceReleaseRecord.setLogday(logday);
        highQualityDeviceReleaseRecord.setCreateTime(date);
        highQualityDeviceReleaseRecord.setUpdateTime(date);
        highQualityDeviceReleaseRecordMapper.insertIfNotExist(highQualityDeviceReleaseRecord);
    }
}
