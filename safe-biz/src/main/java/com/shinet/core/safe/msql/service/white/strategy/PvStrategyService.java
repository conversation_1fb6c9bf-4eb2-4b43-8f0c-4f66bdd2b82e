package com.shinet.core.safe.msql.service.white.strategy;

import com.alibaba.fastjson.JSONObject;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.shinet.core.safe.constant.DeviceConstants;
import com.shinet.core.safe.dto.StrategyResult;
import com.shinet.core.safe.msql.entity.CommonHeaderDTO;
import com.shinet.core.safe.msql.entity.LockWhiteStrategy;
import com.shinet.core.safe.msql.enums.WhiteReasonTypeEnum;
import com.shinet.core.safe.msql.enums.WhiteStrategyEnums;
import com.shinet.core.safe.msql.service.LockWhiteStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class PvStrategyService implements StrategyInterface {

    @Autowired
    private LockWhiteStrategyService lockWhiteStrategyService;
    @Value("${white.pv.strategy.default:40}")
    private int defaultPv;
    @ApolloJsonValue("${white.pv.strategy.product:[\"allP\"]}")
    private List<String> productList;
    @Resource(name = "stringRedisTemplate2")
    private StringRedisTemplate stringRedisTemplate2;

    private int getStrategyPv(String product) {
        LockWhiteStrategy strategy =  lockWhiteStrategyService.getByProductAndStrategy(product, WhiteStrategyEnums.PV.getStrategy());
        return null == strategy ? defaultPv : strategy.getIntValue1();
    }

    private StrategyResult getResultValue(String device, WhiteReasonTypeEnum typeEnum, int pvLimit) {
        String redisKey = String.format("devicePv:%s", device);
        String value = stringRedisTemplate2.opsForValue().get(redisKey);
        if(null == value) {
            return null;
        }
        //get devicePv:f0dde815-8a41-4c4f-94ef-a0e6a1f19169
        //"{\"1\": \"{'wdcy': '85925'}\", \"2\": \"{'wdcdxfsh': '11665'}\", \"3\": \"{'yyct': '2316'}\", \"4\": \"{'wdqc': '113'}\", \"5\": \"{'snsj2': '87'}\"}"
        JSONObject jo = JSONObject.parseObject(value);
        JSONObject top = jo.getJSONObject("1");
        for(String product : top.keySet()) {
            int topPv = top.getIntValue(product);
            if(topPv > pvLimit) {
                jo.put("pvLimit", pvLimit);
                return new StrategyResult(true, device, WhiteStrategyEnums.PV, typeEnum.name(), jo.toJSONString());
            }
        }
        return null;
    }

    @Override
    public StrategyResult checkStrategy(String product, CommonHeaderDTO commonHeaderDTO) {
        if(!productList.contains("allP") && !productList.contains(product)) {
            return null;
        }
        int pvLimit = getStrategyPv(product);
        StrategyResult result = null;
        boolean isIos = "ios".equalsIgnoreCase(commonHeaderDTO.getOs());
        if(isIos) {
            if(DeviceConstants.validDeviceId(commonHeaderDTO.getDeviceId())) {
                result = getResultValue(commonHeaderDTO.getDeviceId(), WhiteReasonTypeEnum.deviceId, pvLimit);
            }
            if(null == result && DeviceConstants.validDeviceId(commonHeaderDTO.getCaid())) {
                result = getResultValue(commonHeaderDTO.getCaid(), WhiteReasonTypeEnum.caid, pvLimit);
            }
        }else {
            if(DeviceConstants.validDeviceId(commonHeaderDTO.getDeviceId())) {
                result = getResultValue(commonHeaderDTO.getDeviceId(), WhiteReasonTypeEnum.deviceId, pvLimit);
            }
            if(null == result && DeviceConstants.validDeviceId(commonHeaderDTO.getOaid())) {
                result = getResultValue(commonHeaderDTO.getOaid(), WhiteReasonTypeEnum.oaid, pvLimit);
            }
            if(null == result && DeviceConstants.validDeviceId(commonHeaderDTO.getImei())) {
                result = getResultValue(commonHeaderDTO.getImei(), WhiteReasonTypeEnum.imei, pvLimit);
            }
            if(null == result && DeviceConstants.validDeviceId(commonHeaderDTO.getAndroidId())) {
                result = getResultValue(commonHeaderDTO.getAndroidId(), WhiteReasonTypeEnum.androidId, pvLimit);
            }
        }
        return result;
    }
}
