package com.shinet.core.safe.redis;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisClusterConfiguration;
import org.springframework.data.redis.connection.RedisNode;
import org.springframework.data.redis.connection.lettuce.LettuceClientConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;

import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/2/14
 */
@Slf4j
@Configuration
public class RedisConfig {

    @Value("${spring.redis.cluster.nodes}")
    private String redisNodes;
    @Value("${spring.redis.lettuce.pool.max-active:100}")
    private int redisMaxActive;
    @Value("${spring.redis.lettuce.pool.max-wait:-1}")
    private int redisMaxWait;
    @Value("${spring.redis.lettuce.pool.max-idle:8}")
    private int redisMaxIdle;
    @Value("${spring.redis.lettuce.pool.min-idle:0}")
    private int redisMinIdle;
    @Value("${spring.redis.timeout:2000}")
    private long redisTimeout;

    @Bean(name = "stringRedisTemplate")
    @Primary
    public StringRedisTemplate stringRedisTemplate() {

        RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration();
        clusterConfig.setClusterNodes(Arrays.stream(redisNodes.split(","))
                .map(o -> new RedisNode(o.split(":")[0], Integer.parseInt(o.split(":")[1])))
                .collect(Collectors.toList()));

        GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
        poolConfig.setMaxTotal(redisMaxActive);
        poolConfig.setMaxIdle(redisMaxIdle);
        poolConfig.setMaxWaitMillis(redisMaxWait);
        poolConfig.setMinIdle(redisMinIdle);

        LettuceClientConfiguration clientConfig = LettucePoolingClientConfiguration.builder()
                .poolConfig(poolConfig)
                .commandTimeout(java.time.Duration.ofMillis(redisTimeout))
                .build();

        LettuceConnectionFactory lettuceConnectionFactory = new LettuceConnectionFactory(clusterConfig, clientConfig);
        lettuceConnectionFactory.afterPropertiesSet();
        StringRedisTemplate template = new StringRedisTemplate();
        template.setKeySerializer(RedisSerializer.string());
        template.setValueSerializer(RedisSerializer.string());
        template.setConnectionFactory(lettuceConnectionFactory);
        log.info("默认StringRedisTemplate已创建");
        return template;
    }

    @Value("${spring.redis2.cluster.nodes}")
    private String redis2Nodes;
    @Value("${spring.redis2.lettuce.pool.max-active}")
    private int redis2MaxActive;
    @Value("${spring.redis2.lettuce.pool.max-wait}")
    private int redis2MaxWait;
    @Value("${spring.redis2.lettuce.pool.max-idle}")
    private int redis2MaxIdle;
    @Value("${spring.redis2.lettuce.pool.min-idle}")
    private int redis2MinIdle;
    @Value("${spring.redis2.timeout}")
    private long redis2Timeout;
    @Bean(name = "stringRedisTemplate2")
    public StringRedisTemplate stringRedisTemplate2() {
        RedisClusterConfiguration clusterConfig = new RedisClusterConfiguration();
        clusterConfig.setClusterNodes(Arrays.stream(redis2Nodes.split(",")).map(o -> new RedisNode(o.split(":")[0], Integer.parseInt(o.split(":")[1]))).collect(Collectors.toList()));
        GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
        poolConfig.setMaxTotal(redis2MaxActive);
        poolConfig.setMaxIdle(redis2MaxIdle);
        poolConfig.setMaxWaitMillis(redis2MaxWait);
        poolConfig.setMinIdle(redis2MinIdle);
        LettuceClientConfiguration clientConfig = LettucePoolingClientConfiguration.builder().poolConfig(poolConfig).commandTimeout(java.time.Duration.ofMillis(redis2Timeout)).build();
        LettuceConnectionFactory lettuceConnectionFactory = new LettuceConnectionFactory(clusterConfig, clientConfig);
        lettuceConnectionFactory.afterPropertiesSet();
        StringRedisTemplate template = new StringRedisTemplate();
        template.setKeySerializer(RedisSerializer.string());
        template.setValueSerializer(RedisSerializer.string());
        template.setConnectionFactory(lettuceConnectionFactory);
        log.info("RedisTemplate2 已创建");
        return template;
    }

}
