package com.shinet.core.safe.util;

import static com.shinet.core.safe.constant.BaseConstants.CHAR_NINE;
import static com.shinet.core.safe.constant.BaseConstants.CHAR_ZERO;

/**
 * <AUTHOR>
 * @date 2021/9/17
 */
public class CustomStringUtils {

    /**
     * 非纯粹的数字
     *
     * @param text 判断文本
     * @return true-含有非数字文本
     */
    public static boolean isNotPureNumber(String text) {

        for (int index = 0; index < text.length(); index++) {

            char currentLetter = text.charAt(index);

            if (currentLetter < CHAR_ZERO || currentLetter > CHAR_NINE) {
                return true;
            }
        }

        return false;
    }
}
