package com.shinet.core.safe.util;

import com.shinet.core.safe.hbase.config.HBaseParameterEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.TableNotFoundException;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.io.compress.Compression;
import org.apache.hadoop.hbase.io.encoding.DataBlockEncoding;
import org.apache.hadoop.hbase.util.Bytes;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.shinet.core.safe.constant.BaseConstants.CHAR_NINE;
import static com.shinet.core.safe.constant.BaseConstants.CHAR_ZERO;
import static com.shinet.core.safe.hbase.config.HBaseParameterEnum.DEFAULT_COLUMN;
import static com.shinet.core.safe.hbase.config.HBaseParameterEnum.FAMILY;

/**
 * <AUTHOR>
 */
@Slf4j
public class HBaseUtils {

    /**
     * 本工程唯一指定splitKey
     */
    public static final byte[][] ONLY_SPLIT_KEYS = {Bytes.toBytes("0"), Bytes.toBytes("1"), Bytes.toBytes("2"),
            Bytes.toBytes("3"), Bytes.toBytes("4"), Bytes.toBytes("5"), Bytes.toBytes("6"),
            Bytes.toBytes("7"), Bytes.toBytes("8"), Bytes.toBytes("9")};

    /**
     * rowKey前缀数组
     */
    private static final String[] ROW_KEY_PREFIX_ARRAY = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9"};

    public static String getCellValStr(Result res) {
        List<Cell> cells = res.listCells();
        if (cells != null && cells.size() > 0) {
            byte[] val = res.getValue(HBaseParameterEnum.FAMILY.getBinaryContent(), HBaseParameterEnum.DEFAULT_COLUMN.getBinaryContent());
            return Bytes.toString(val);
        }
        return null;
    }

    /**
     * 初始化hadoop表
     *
     * @param connection 连接
     * @param tableName  表名
     * @param dataTtl    数据存续时长
     * @throws IOException 可能的异常
     */
    public static void initHadoopTable(Connection connection, String tableName, int dataTtl) throws IOException {

        try (Admin admin = connection.getAdmin()) {
            // 建表
            try {
                admin.getDescriptor(TableName.valueOf(tableName));
            } catch (TableNotFoundException te) {

                ColumnFamilyDescriptor familyDescriptor = ColumnFamilyDescriptorBuilder
                        .newBuilder(HBaseParameterEnum.FAMILY.getBinaryContent())
                        .setTimeToLive(dataTtl)
                        .setCompressionType(Compression.Algorithm.ZSTD)
                        .setDataBlockEncoding(DataBlockEncoding.DIFF)
                        .build();

                TableDescriptor tableDescriptor = TableDescriptorBuilder.newBuilder(TableName.valueOf(tableName))
                        .setColumnFamily(familyDescriptor)
                        .setCompactionEnabled(true)
                        .build();

                admin.createTable(tableDescriptor, ONLY_SPLIT_KEYS);
            }
        } catch (IOException e) {
            log.error("{}表创建失败:{}", tableName, e);
            throw e;
        }
    }

    /**
     * 将数据存入Hadoop的指定表
     *
     * @param connection 连接
     * @param tableName  表名
     * @param rowKey     行key
     * @param content    列值
     * @return 存储Hadoop结果
     */
    public static boolean saveToHadoop(Connection connection, String tableName, String rowKey, byte[] content) {

        try (Table table = connection.getTable(TableName.valueOf(tableName))) {

            String processedRowKey = hashRowKeyByLastCharacter(rowKey);

            Put put = new Put(Bytes.toBytes(processedRowKey));
            put.addColumn(HBaseParameterEnum.FAMILY.getBinaryContent(), HBaseParameterEnum.DEFAULT_COLUMN.getBinaryContent(),
                    content);

            table.put(put);

            return true;
        } catch (Exception e) {
            log.error("Hadoop表存储失败 ", e);
            return false;
        }
    }


    public static boolean saveToHadoopBatch(Connection connection, String tableName, Map<String,byte[]> targetContent) {

        try (Table table = connection.getTable(TableName.valueOf(tableName))) {
            List<Put> puts = new ArrayList<>();
            targetContent.forEach((processedRowKey, content) -> {
                Put put = new Put(Bytes.toBytes(processedRowKey));
                put.addColumn(FAMILY.getBinaryContent(), DEFAULT_COLUMN.getBinaryContent(), content);
                puts.add(put);
            });

            table.put(puts);
            return true;
        } catch (Exception e) {
            log.error("Hadoop表存储失败 ", e);
            return false;
        }
    }


    /**
     * 指定表名从Hadoop中检索特定数据
     *
     * @param connection 连接
     * @param tableName  表名
     * @param rowKey     行key
     * @return 数据检索结果
     */
    public static byte[] searchDataFromHadoop(Connection connection, String tableName, String rowKey) {

        try (Table table = connection.getTable(TableName.valueOf(tableName))) {

            String processedRowKey = hashRowKeyByLastCharacter(rowKey);

            Get get = new Get(Bytes.toBytes(processedRowKey));

            Result result = table.get(get);

            return result.getValue(FAMILY.getBinaryContent(), DEFAULT_COLUMN.getBinaryContent());

        } catch (Exception e) {
            log.error("Hadoop表检索失败:", e);
            return null;
        }
    }

    /**
     * 从HBase删数据(RowKey暂不散列)
     *
     * @param connection 连接
     * @param tableName  表名
     * @param rowKey     rowKey
     * @return 删除结果
     */
    public static boolean deleteFromHBase(Connection connection, String tableName, String rowKey) {

        try (Table table = connection.getTable(TableName.valueOf(tableName))) {

            Delete delete = new Delete(Bytes.toBytes(hashRowKeyByLastCharacter(rowKey)));

            table.delete(delete);

            return true;

        } catch (Exception e) {
            log.error("Hadoop表数据删除失败:", e);
            return false;
        }
    }

    /**
     * 清理HBase数据，慎用！！！
     *
     * @param connection 连接
     * @param tableName 表名
     */
    public static boolean clearHBaseData(Connection connection, String tableName) {

        try (Table table = connection.getTable(TableName.valueOf(tableName))) {

            Scan scan = new Scan();

            ResultScanner scanner = table.getScanner(scan);

            Result result;

            while (true) {

                result = scanner.next();

                if (result == null) {
                    break;
                }

                Delete delete = new Delete(result.getRow());

                table.delete(delete);
            }

            scanner.close();

            return true;
        } catch (Exception e) {
            log.error("Hadoop表数据清理失败", e);
            return false;
        }
    }

    /**
     * 根据rowKey最后一位来散列rowKey：根据ASCII码加上0-9前缀
     *
     * @param rowKey rowKey
     * @return 散列后的rowKey
     */
    private static String hashRowKeyByLastCharacter(String rowKey) {

        char lastCharacter = rowKey.charAt(rowKey.length() - 1);

        int prefixIndex;

        if (lastCharacter > CHAR_NINE || lastCharacter < CHAR_ZERO) {
            prefixIndex = lastCharacter % 10;
        } else {
            prefixIndex = lastCharacter - CHAR_ZERO;
        }

        return ROW_KEY_PREFIX_ARRAY[prefixIndex] + rowKey;
    }

    /**
     * 指定表名从Hadoop中检索特定数据(无分片版本)
     *
     * @param connection 连接
     * @param tableName  表名
     * @param rowKey     行key
     * @return 数据检索结果
     */
    public static Pair<Boolean, byte[]> searchDataFromHadoopWithoutHash(Connection connection, String tableName, String rowKey) {

        try (Table table = connection.getTable(TableName.valueOf(tableName))) {

            Get get = new Get(Bytes.toBytes(rowKey));

            Result result = table.get(get);

            return Pair.of(true, result.getValue(HBaseParameterEnum.FAMILY.getBinaryContent(),
                    HBaseParameterEnum.DEFAULT_COLUMN.getBinaryContent()));

        } catch (Exception e) {
            log.error("Hadoop表检索失败 ", e);
            return Pair.of(false, null);
        }
    }
}
