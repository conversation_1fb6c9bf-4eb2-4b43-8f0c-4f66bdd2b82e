package com.shinet.core.safe.util;

import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBatch;
import org.redisson.api.RKeys;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class RedisUtils {

    private static final String LOCK_SCRIPT = "return redis.call('set', KEYS[1], ARGV[1], 'NX', 'PX', ARGV[2])";
    private static final String LOCK_SUCCESS = "OK";
    private static final String RELEASE_SCRIPT = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
    private static final long RELEASE_SUCCESS = 1L;

    // 锁最长等待时间，单位ms
    private static final String MAX_WAIT_LOCK_TIME = "3000";
    // 锁轮询间隔时间，单位ms
    private static final int WAIT_LOCK_PER_CHECK = 200;
    
    private static final StringRedisSerializer stringRedisSerializer =  new StringRedisSerializer();

    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate ;

    @Resource(name = "redissonClient2")
    private RedissonClient redissonClient2;

    public Long incr(String key){
        return stringRedisTemplate.opsForValue().increment(key);
    }

    public Long incrAmount(String key,Long amount){
        return stringRedisTemplate.opsForValue().increment(key,amount);
    }

    public String get(String key){
        return stringRedisTemplate.opsForValue().get(key);
    }

    public List<String> batchGet(List<String> keys){
        return stringRedisTemplate.opsForValue().multiGet(keys);
    }


    /**
     * 尝试获取分布式锁
     * @param key 锁名称
     * @param identification 身份认证标识
     * @param expireTime 超时时间，ms
     * @return 是否成功获取锁
     */
    public  boolean tryLock(String key, String identification, String expireTime) {
        Object result = stringRedisTemplate.execute(new DefaultRedisScript<>(LOCK_SCRIPT, String.class), Collections.singletonList(key), identification, expireTime);
        return Objects.equals(result, LOCK_SUCCESS);
    }


    private  boolean tryLockLoop(String key, String identification, String expireTime) {
        // 或许会导致死循环？
        while (!tryLock(key, identification, expireTime)) {
            log.info("wait lock {}-{}", key, identification);
            try {
                Thread.sleep(WAIT_LOCK_PER_CHECK);
            } catch (Exception e) {
                log.error("", e);
            }
        }
        return true;
    }

    /**
     * 尝试释放分布式锁
     * @param key 锁名称
     * @param identification 身份认证标识
     * @return 是否成功释放锁
     */
    private  boolean releaseLock(String key, String identification) {
        Object result = stringRedisTemplate.execute(new DefaultRedisScript<>(RELEASE_SCRIPT, Long.class), Collections.singletonList(key), identification);
        return Objects.equals(result,RELEASE_SUCCESS);
    }

    private  <R> R baseLockProcess(boolean isLoop, String key, Function<? super String, ? extends R> successProcess, Function<? super String, ? extends R> failProcess) {
        String identification = UUID.randomUUID().toString().replaceAll("-", "");
        try {
            if (isLoop ? tryLockLoop(key, identification, MAX_WAIT_LOCK_TIME) : tryLock(key, identification, MAX_WAIT_LOCK_TIME)) {
                log.info("success lock {}-{}", key, identification);
                return successProcess.apply(identification);
            } else {
                log.info("fail lock {}-{}", key, identification);
                return failProcess.apply(identification);
            }
        } finally {
            if (releaseLock(key, identification)) {
                log.info("success release lock {}-{}", key, identification);
            } else {
                log.info("fail release lock {}-{}", key, identification);
            }
        }
    }

    /**
     * 通用锁控制流程（等待锁释放）
     * @param key 加锁主键
     * @param successProcess 获取锁成功后执行流程
     * @param failProcess 获取锁失败后执行流程
     * @return 执行流程返回结果
     */
    @SuppressWarnings("UnusedReturnValue")
    public  <R> R processWithWait(String key, Function<? super String, ? extends R> successProcess, Function<? super String, ? extends R> failProcess) {
        return baseLockProcess(true, key, successProcess, failProcess);
    }

    /**
     * 通用锁控制流程（只尝试一次）
     * @param key 加锁主键
     * @param successProcess 获取锁成功后执行流程
     * @param failProcess 获取锁失败后执行流程
     * @return 执行流程返回结果
     */
    @SuppressWarnings("UnusedReturnValue")
    public  <R> R processWithoutWait(String key, Function<? super String, ? extends R> successProcess, Function<? super String, ? extends R> failProcess) {
        return baseLockProcess(false, key, successProcess, failProcess);
    }

    /**
     * 高效删除指定前缀的所有键 - 使用Redisson SCAN方式
     * @param keyPrefix 键前缀，如 "devicePv:"
     * @return 删除的键数量
     */
    public long deleteKeysByPrefix(String keyPrefix) {
        log.info("开始使用SCAN方式删除前缀为 {} 的键", keyPrefix);

        try {
            RKeys keys = redissonClient2.getKeys();
            long deletedCount = 0;

            // 使用分批删除方式，避免阻塞
            Iterable<String> keyIterable = keys.getKeysByPattern(keyPrefix + "*", 50000);

            RBatch batch = redissonClient2.createBatch();
            int batchCount = 0;

            for (String keyName : keyIterable) {
                batch.getBucket(keyName).deleteAsync();
                batchCount++;
                deletedCount++;

                // 每1000个键执行一次批量删除
                if (batchCount >= 20000) {
                    batch.execute();
                    batch = redissonClient2.createBatch();
                    batchCount = 0;

                    // 短暂休眠避免阻塞Redis
                    Thread.sleep(1);

                    // 进度日志
                    if (deletedCount % 50000 == 0) {
                        XxlJobLogger.log("SCAN删除进度: 已删除 {} 个键", deletedCount);
                    }
                }
            }

            // 执行剩余的删除操作
            if (batchCount > 0) {
                batch.execute();
            }

            XxlJobLogger.log("SCAN删除完成，共删除 {} 个键", deletedCount);
            return deletedCount;

        } catch (Exception e) {
            XxlJobLogger.log("SCAN删除键失败, prefix: {}", keyPrefix, e);
            throw new RuntimeException("删除失败: " + e.getMessage(), e);
        }
    }

}
