package com.shinet.core.safe.core.client;

import com.alibaba.fastjson.JSON;
import com.shinet.core.safe.core.constants.AbTestConstants;
import com.shinet.core.safe.core.dto.AbTestRequestDTO;
import com.shinet.core.safe.core.vo.AbTestResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;

/**
 * AB实验HTTP客户端
 * 使用连接池和1秒超时配置
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class AbTestHttpClient {
    
    @Value("${abtest.service.url:}")
    private String abTestServiceUrl;
    
    private CloseableHttpClient httpClient;
    private PoolingHttpClientConnectionManager connectionManager;
    
    @PostConstruct
    public void init() {
        connectionManager = new PoolingHttpClientConnectionManager();
        connectionManager.setMaxTotal(AbTestConstants.HTTP_MAX_TOTAL);
        connectionManager.setDefaultMaxPerRoute(AbTestConstants.HTTP_MAX_PER_ROUTE);

        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(AbTestConstants.HTTP_CONNECT_TIMEOUT)  // 连接超时1秒
                .setSocketTimeout(AbTestConstants.HTTP_SOCKET_TIMEOUT)   // 读取超时1秒
                .setConnectionRequestTimeout(AbTestConstants.HTTP_REQUEST_TIMEOUT)  // 从连接池获取连接超时500ms
                .build();
        
        httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(requestConfig)
                .build();
        
        log.info("AB实验HTTP客户端初始化完成，服务地址: {}", abTestServiceUrl);
    }
    
    @PreDestroy
    public void destroy() {
        try {
            if (httpClient != null) {
                httpClient.close();
            }
            if (connectionManager != null) {
                connectionManager.close();
            }
            log.info("AB实验HTTP客户端已关闭");
        } catch (Exception e) {
            log.error("关闭AB实验HTTP客户端异常", e);
        }
    }
    
    /**
     * 调用AB实验服务
     * 
     * @param request 请求参数
     * @return AB实验响应，异常时返回默认分组0
     */
    public AbTestResponseVO callAbTestService(AbTestRequestDTO request) {
        if (abTestServiceUrl == null || abTestServiceUrl.trim().isEmpty()) {
            log.warn("AB实验服务URL未配置，返回默认分组");
            return AbTestResponseVO.createDefault();
        }
        
        try {
            HttpPost httpPost = new HttpPost(abTestServiceUrl);
            
            // 设置请求头参数
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("deviceId", request.getDeviceId());
            httpPost.setHeader("userId", request.getUserId());
            httpPost.setHeader("appVersion", request.getAppVersion());
            httpPost.setHeader("os", request.getOs());
            httpPost.setHeader("channel", request.getChannel());
            httpPost.setHeader("appId", request.getAppId());
            httpPost.setHeader("sdkVersion", request.getSdkVersion());
            httpPost.setHeader("brand", request.getBrand());
            httpPost.setHeader("oaid", request.getOaid());
            httpPost.setHeader("caid", request.getCaid());
            httpPost.setHeader("androidId", request.getAndroidId());
            httpPost.setHeader("idfa", request.getIdfa());
            httpPost.setHeader("idfv", request.getIdfv());
            httpPost.setHeader("product", request.getProduct());
            
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                
                if (response.getStatusLine().getStatusCode() == 200) {
                    AbTestResponseVO result = JSON.parseObject(responseBody, AbTestResponseVO.class);
                    if (result != null
                            && Integer.valueOf(200).equals(result.getStatus())
                            && result.getData() != null
                            && result.getData().getCategoryId() != null) {
                        return result;
                    }
                }
                
                log.warn("AB实验服务响应异常，状态码: {}, 响应: {}", 
                        response.getStatusLine().getStatusCode(), responseBody);
                
            }
        } catch (Exception e) {
            log.error("调用AB实验服务异常，返回默认分组", e);
        }
        
        return AbTestResponseVO.createDefault();
    }
}
