package com.shinet.core.safe.core.config;

import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Redisson配置类 - 用于高性能Redis操作
 * 迁移到safe-core模块，为AB实验服务和其他模块提供统一的Redis分布式锁和缓存支持
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class RedissonConfig {

    @Value("${spring.redis2.cluster.nodes}")
    private String redis2Nodes;
    
    @Value("${spring.redis2.timeout:2000}")
    private long redis2Timeout;
    
    @Value("${spring.redis2.lettuce.pool.max-active:100}")
    private int redis2MaxActive;

    @Bean(name = "redissonClient2", destroyMethod = "shutdown")
    public RedissonClient redissonClient2() {
        Config config = new Config();
        
        // 配置集群模式
        String[] nodes = redis2Nodes.split(",");
        String[] redisAddresses = new String[nodes.length];
        
        for (int i = 0; i < nodes.length; i++) {
            redisAddresses[i] = "redis://" + nodes[i];
        }
        
        config.useClusterServers()
                .addNodeAddress(redisAddresses)
                .setConnectTimeout((int) redis2Timeout)
                .setTimeout((int) redis2Timeout)
                .setMasterConnectionPoolSize(redis2MaxActive)
                .setSlaveConnectionPoolSize(redis2MaxActive)
                .setRetryAttempts(3)
                .setRetryInterval(1500)
                .setScanInterval(2000);
        
        // 性能优化配置
        config.setThreads(16)
              .setNettyThreads(32)
              .setKeepPubSubOrder(false)
              .setUseScriptCache(true);
        
        RedissonClient redissonClient = Redisson.create(config);
        log.info("Redisson客户端已创建，集群节点: {}", redis2Nodes);
        
        return redissonClient;
    }
}
