package com.shinet.core.safe.core.constants;

/**
 * 设备归因重试机制常量类
 *
 * <AUTHOR>
 */
public class DeviceRetryConstants {

    /**
     * 新用户时间窗口（小时）
     */
    public static final int NEW_USER_HOURS = 24;

    /**
     * 定时任务查询时间窗口（小时）
     */
    public static final int TASK_QUERY_HOURS = 1;

    /**
     * 定时任务批量大小
     */
    public static final int BATCH_SIZE = 1000;

    /**
     * API调用批量大小
     */
    public static final int API_BATCH_SIZE = 100;

    /**
     * 最大重试次数
     */
    public static final int MAX_RETRY_COUNT = 30;

    /**
     * 备份数据保留天数
     */
    public static final int BACKUP_RETENTION_DAYS = 30;

    /**
     * 操作系统类型
     */
    public static final class OS {
        public static final String ANDROID = "android";
        public static final String IOS = "ios";
    }


    /**
     * 删除原因
     */
    public static final class DeleteReason {
        public static final String ATTRIBUTION_SUCCESS = "设备归因成功后清理";
        public static final String EXPIRED_CLEANUP = "过期数据清理";
        public static final String MANUAL_CLEANUP = "手动清理";
    }

    /**
     * 线程池名称
     */
    public static final String DATA_PERSISTENCE_EXECUTOR = "dataPersistenceExecutor";

    /**
     * 数据清理保留时间（小时）
     */
    public static final int RETRY_RECORD_RETENTION_HOURS = 168;

    /**
     * 批量处理时间窗口（小时）
     */
    public static final int BATCH_PROCESS_WINDOW_HOURS = 72;

    /**
     * 日志标识
     */
    public static final class LogTag {
        public static final String DEVICE_RETRY = "[DeviceRetry]";
        public static final String ASYNC_RECORD = "[AsyncRecord]";
        public static final String BATCH_PROCESS = "[BatchProcess]";
        public static final String DATA_BACKUP = "[DataBackup]";
        public static final String API_CALL = "[ApiCall]";
        public static final String TIMER_TASK = "[TimerTask]";
    }

    /**
     * 数据库表名
     */
    public static final class TableName {
        public static final String RETRY_RECORD = "lock_device_gy_retry_record";
        public static final String BACKUP_RECORD = "lock_device_attribution_rst_backup";
    }

    /**
     * 缓存键前缀
     */
    public static final class CacheKey {
        public static final String DEVICE_RETRY_PREFIX = "device:retry:";
        public static final String BACKUP_STATS_PREFIX = "backup:stats:";
    }
}
