package com.shinet.core.safe.core.constants;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

public class GlobalRedisConstants {

    public static final String SCREEN_VIDEO_BLACKLIST = "lock:screen:video:";

    public static final String IOS_PRODUCT_UP_DAYS = "ios:up:days:%s";
    public static final String IOS_PRODUCT_UP_DAYS_CAN_INCR_CHECK = "ios:up:can:incr:%s:%s";

    // ========== 设备拉黑Redis Key前缀 ==========
    public static final String DEVICE_BLACK_PREFIX = "lock:black:";
    public static final String DEVICE_BLACK_IOS_PREFIX = "lock:black:ios:";

    // ========== 异常设备标识过滤 ==========
    private static final Set<String> INVALID_DEVICE_VALUES = new HashSet<>(Arrays.asList(
        "", "null", "NULL", "undefined", "UNDEFINED",
        "00000000-0000-0000-0000-000000000000",
        "000000000000000000000000000000000000",
        "00000000000000000000000000000000",
        "0000000000000000",
        "00000000",
        "0"
    ));

    public static String getIosProductUpDays(String product) {
        return String.format(IOS_PRODUCT_UP_DAYS, product);
    }

    public static String getIosProductUpDaysCanIncrCheck(String product) {
        Date now = new Date();
        String day = DateUtil.format(now, DatePattern.PURE_DATE_PATTERN);
        return String.format(IOS_PRODUCT_UP_DAYS_CAN_INCR_CHECK, day, product);
    }

    /**
     * 构建设备拉黑Redis Key
     * Android设备：lock:black:{targetId}
     * iOS设备：lock:black:ios:{targetId}
     */
    public static String buildDeviceBlackKey(String os, String targetId) {
        if (!isValidDeviceValue(targetId)) {
            return null;
        }
        
        if ("ios".equalsIgnoreCase(os)) {
            return DEVICE_BLACK_IOS_PREFIX + targetId;
        } else {
            return DEVICE_BLACK_PREFIX + targetId;
        }
    }

    /**
     * 检查设备标识是否有效
     * 过滤空字符串、00000等异常字段
     */
    public static boolean isValidDeviceValue(String deviceValue) {
        if (StrUtil.isBlank(deviceValue)) {
            return false;
        }
        
        String trimmed = deviceValue.trim();
        if (INVALID_DEVICE_VALUES.contains(trimmed)) {
            return false;
        }
        
        // 检查是否全为0
        if (trimmed.matches("^0+$")) {
            return false;
        }
        
        return true;
    }
}
