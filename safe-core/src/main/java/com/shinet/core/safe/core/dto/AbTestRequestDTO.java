package com.shinet.core.safe.core.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AB实验请求参数DTO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AbTestRequestDTO {
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 应用版本
     */
    private String appVersion;
    
    /**
     * 操作系统 (android/ios)
     */
    private String os;
    
    /**
     * 渠道
     */
    private String channel;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * SDK版本
     */
    private String sdkVersion;
    
    /**
     * 设备品牌
     */
    private String brand;
    
    /**
     * Android OAID
     */
    private String oaid;
    
    /**
     * iOS CAID
     */
    private String caid;
    
    /**
     * Android ID
     */
    private String androidId;
    
    /**
     * iOS IDFA
     */
    private String idfa;
    
    /**
     * iOS IDFV
     */
    private String idfv;
    
    /**
     * 产品标识
     */
    private String product;
}
