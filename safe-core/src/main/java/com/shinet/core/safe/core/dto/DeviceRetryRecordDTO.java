package com.shinet.core.safe.core.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 设备重试记录DTO
 * 用于异步记录设备信息的数据传输
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Data
@Accessors(chain = true)
public class DeviceRetryRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品标识
     */
    private String product;

    /**
     * 设备ID：Android为oaid，iOS为caid
     */
    private String deviceId;

    /**
     * 操作系统：android/ios
     */
    private String os;

    /**
     * IP地址
     */
    private String ip;

    /**
     * 城市信息
     */
    private String city;

    /**
     * 应用版本
     */
    private String appVersion;

    /**
     * 是否OCPC用户：0-自然量，1-买量
     */
    private Integer isOcpc;

    /**
     * 创建Android设备记录
     * 
     * @param product 产品标识
     * @param oaid Android设备的oaid
     * @return DeviceRetryRecordDTO实例
     */
    public static DeviceRetryRecordDTO createAndroidRecord(String product, String oaid) {
        return new DeviceRetryRecordDTO()
                .setProduct(product)
                .setDeviceId(oaid)
                .setOs("android")
                .setIsOcpc(0); // 默认为自然量，后续会根据OCPC判断结果更新
    }

    /**
     * 创建iOS设备记录
     * 
     * @param product 产品标识
     * @param caid iOS设备的caid
     * @return DeviceRetryRecordDTO实例
     */
    public static DeviceRetryRecordDTO createIosRecord(String product, String caid) {
        return new DeviceRetryRecordDTO()
                .setProduct(product)
                .setDeviceId(caid)
                .setOs("ios")
                .setIsOcpc(0); // 默认为自然量，后续会根据OCPC判断结果更新
    }

    /**
     * 检查必要字段是否完整
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return product != null && !product.trim().isEmpty() &&
               deviceId != null && !deviceId.trim().isEmpty() &&
               os != null && !os.trim().isEmpty();
    }

    /**
     * 获取唯一标识
     * 用于去重检查
     * 
     * @return 唯一标识字符串
     */
    public String getUniqueKey() {
        return String.format("%s_%s_%s", deviceId, product, os);
    }
}
