package com.shinet.core.safe.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备归因备份表
 * 统一存储AndroidLockRst和LockIosRst删除记录
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lock_device_attribution_rst_backup")
@ApiModel(value = "LockDeviceAttributionRstBackup对象", description = "设备归因备份表")
public class LockDeviceAttributionRstBackup implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "来源表名：AndroidLockRst/LockIosRst")
    private String sourceTable;

    @ApiModelProperty(value = "来源记录ID")
    private String sourceId;

    @ApiModelProperty(value = "设备ID")
    private String deviceId;

    @ApiModelProperty(value = "产品标识")
    private String product;

    @ApiModelProperty(value = "操作系统")
    private String os;

    @ApiModelProperty(value = "用户ID")
    private String userId;

    @ApiModelProperty(value = "原始数据JSON格式")
    private String originalData;

    @ApiModelProperty(value = "删除原因")
    private String deleteReason;

    @ApiModelProperty(value = "关联重试记录ID")
    private Long retryRecordId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    public static LockDeviceAttributionRstBackup createBackup(String sourceTable, String sourceId,
                                                              String deviceId, String product, String os, 
                                                              String originalData, Long retryRecordId) {
        return new LockDeviceAttributionRstBackup()
                .setSourceTable(sourceTable)
                .setSourceId(sourceId)
                .setDeviceId(deviceId)
                .setProduct(product)
                .setOs(os)
                .setOriginalData(originalData)
                .setRetryRecordId(retryRecordId)
                .setDeleteReason("设备归因成功后清理")
                .setCreateTime(new Date())
                .setUpdateTime(new Date());
    }
}
