package com.shinet.core.safe.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 锁区设备拉黑统一管理表
 * 
 * <AUTHOR>
 * @since 2025-01-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lock_device_black")
@ApiModel(value = "LockDeviceBlack对象", description = "锁区设备拉黑统一管理表")
public class LockDeviceBlack implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "操作系统：android/ios")
    private String os;

    @ApiModelProperty(value = "设备类型：1-IP, 2-OAID, 3-CAID, 4-IDFA")
    private Integer targetType;

    @ApiModelProperty(value = "设备标识值")
    private String targetId;

    @ApiModelProperty(value = "拉黑原因备注")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    // ========== 设备类型常量 ==========
    public static final int TARGET_TYPE_IP = 1;      // IP地址（Android/iOS通用）
    public static final int TARGET_TYPE_OAID = 2;    // OAID（Android专用）
    public static final int TARGET_TYPE_CAID = 3;    // CAID（iOS专用）
    public static final int TARGET_TYPE_IDFA = 4;    // IDFA（iOS专用）

    // ========== 操作系统常量 ==========
    public static final String OS_ANDROID = "android";
    public static final String OS_IOS = "ios";

    /**
     * 构建Redis Key
     * Android设备：lock:black:{targetId}
     * iOS设备：lock:black:ios:{targetId}
     */
    public String buildRedisKey() {
        if (OS_IOS.equals(this.os)) {
            return "lock:black:ios:" + this.targetId;
        } else {
            return "lock:black:" + this.targetId;
        }
    }

    /**
     * 设置基础信息的便捷方法
     */
    public LockDeviceBlack setBasicInfo(String os, Integer targetType, String targetId, String remark) {
        this.os = os;
        this.targetType = targetType;
        this.targetId = targetId;
        this.remark = remark;
        return this;
    }
}
