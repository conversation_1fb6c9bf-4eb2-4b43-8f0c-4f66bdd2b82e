package com.shinet.core.safe.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备归因重试记录表
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lock_device_gy_retry_record")
@ApiModel(value = "LockDeviceGyRetryRecord对象", description = "设备归因重试记录表")
public class LockDeviceGyRetryRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "产品标识")
    private String product;

    @ApiModelProperty(value = "设备ID：Android为oaid，iOS为caid")
    private String deviceId;

    @ApiModelProperty(value = "操作系统：android/ios")
    private String os;

    @ApiModelProperty(value = "是否OCPC用户：0-自然量，1-买量")
    private Integer isOcpc;

    @ApiModelProperty(value = "重试次数")
    private Integer retryCount;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    public LockDeviceGyRetryRecord setBasicInfo(String product, String deviceId, String os) {
        this.product = product;
        this.deviceId = deviceId;
        this.os = os;
        this.isOcpc = 0; // 默认为自然量
        this.retryCount = 0; // 初始重试次数为0
        return this;
    }
}
