package com.shinet.core.safe.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 锁区多位置记录表
 * 
 * <AUTHOR>
 * @since 2025-01-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lock_multi_location_record")
@ApiModel(value = "LockMultiLocationRecord对象", description = "锁区多位置记录表")
public class LockMultiLocationRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "产品标识")
    private String product;

    @ApiModelProperty(value = "设备id：安卓为oaid，ios为caid")
    private String deviceId;

    @ApiModelProperty(value = "操作系统：android/ios")
    private String os;

    @ApiModelProperty(value = "位置数量")
    private Integer locationCount;

    @ApiModelProperty(value = "详情")
    private String locationInfo;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    // ========== 操作系统常量 ==========
    public static final String OS_ANDROID = "android";
    public static final String OS_IOS = "ios";

    /**
     * 设置基础信息的便捷方法
     */
    public LockMultiLocationRecord setBasicInfo(String product, String deviceId, String os, Integer locationCount, String locationInfo) {
        this.product = product;
        this.deviceId = deviceId;
        this.os = os;
        this.locationCount = locationCount;
        this.locationInfo = locationInfo;
        return this;
    }

    /**
     * 构建唯一标识
     * 用于唯一索引：device_id + product + os
     */
    public String buildUniqueKey() {
        return deviceId + "_" + product + "_" + os;
    }
}
