package com.shinet.core.safe.core.enums;

import lombok.Getter;

@Getter
public enum SdkTypeEnum {

    SDK_V1("sdk1.0", 1),
    SDK_V2("sdk2.0", 2),
//    SDK3("sdk3", 3),
    ;

    private String name;
    private Integer code;

    SdkTypeEnum(String name, Integer code) {
        this.name = name;
        this.code = code;
    }

    public static SdkTypeEnum getByCode(Integer code) {
        for (SdkTypeEnum sdkTypeEnum : values()) {
            if (sdkTypeEnum.code.equals(code)) {
                return sdkTypeEnum;
            }
        }
        return null;
    }
}
