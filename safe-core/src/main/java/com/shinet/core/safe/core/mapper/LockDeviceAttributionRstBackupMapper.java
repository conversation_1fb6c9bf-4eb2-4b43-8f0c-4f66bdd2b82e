package com.shinet.core.safe.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shinet.core.safe.core.entity.LockDeviceAttributionRstBackup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 设备归因备份表Mapper接口
 * 使用XML配置复杂SQL，MyBatis-Plus Lambda处理简单查询
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Mapper
public interface LockDeviceAttributionRstBackupMapper extends BaseMapper<LockDeviceAttributionRstBackup> {

    /**
     * 根据来源表和记录ID查询备份记录
     *
     * @param sourceTable 来源表名
     * @param sourceId 来源记录ID
     * @return 备份记录
     */
    LockDeviceAttributionRstBackup selectBySource(@Param("sourceTable") String sourceTable,
                                                  @Param("sourceId") String sourceId);

    /**
     * 根据设备信息查询备份记录
     *
     * @param deviceId 设备ID
     * @param product 产品标识
     * @param os 操作系统
     * @return 备份记录列表
     */
    List<LockDeviceAttributionRstBackup> selectByDeviceInfo(@Param("deviceId") String deviceId,
                                                            @Param("product") String product,
                                                            @Param("os") String os);

    /**
     * 查询过期的备份记录
     * 用于定期清理
     *
     * @param days 保留天数
     * @return 过期记录列表
     */
    List<LockDeviceAttributionRstBackup> selectExpiredRecords(@Param("days") int days);

    /**
     * 根据重试记录ID查询备份记录
     *
     * @param retryRecordId 重试记录ID
     * @return 备份记录列表
     */
    List<LockDeviceAttributionRstBackup> selectByRetryRecordId(@Param("retryRecordId") Long retryRecordId);

    /**
     * 统计备份记录数量
     *
     * @param sourceTable 来源表名（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @return 记录数量
     */
    Long countBackupRecords(@Param("sourceTable") String sourceTable,
                           @Param("startTime") Date startTime,
                           @Param("endTime") Date endTime);

    /**
     * 批量查询备份记录（分页）
     *
     * @param sourceTable 来源表名（可选）
     * @param product 产品标识（可选）
     * @param os 操作系统（可选）
     * @param offset 偏移量
     * @param limit 查询数量限制
     * @return 备份记录列表
     */
    List<LockDeviceAttributionRstBackup> selectBackupRecordsByPage(@Param("sourceTable") String sourceTable,
                                                                   @Param("product") String product,
                                                                   @Param("os") String os,
                                                                   @Param("offset") int offset,
                                                                   @Param("limit") int limit);
}
