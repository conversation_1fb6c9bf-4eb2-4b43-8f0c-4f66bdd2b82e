package com.shinet.core.safe.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shinet.core.safe.core.entity.LockDeviceBlack;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 锁区设备拉黑统一管理表Mapper接口
 *
 * <AUTHOR>
 * @since 2025-01-24
 */
@Mapper
public interface LockDeviceBlackMapper extends BaseMapper<LockDeviceBlack> {

    /**
     * 根据os和设备标识列表批量查询拉黑记录
     * 用于统一检测服务批量查询
     *
     * @param os 操作系统：android/ios
     * @param targetIds 设备标识列表
     * @return 拉黑记录列表
     */
    List<LockDeviceBlack> selectByOsAndTargetIds(@Param("os") String os, 
                                                 @Param("targetIds") List<String> targetIds);

    /**
     * 批量插入设备拉黑记录
     * 用于批量新增接口
     *
     * @param list 拉黑记录列表
     * @return 插入行数
     */
    int batchInsert(@Param("list") List<LockDeviceBlack> list);

    /**
     * 根据os和target_type查询所有设备标识
     * 用于Redis数据同步
     *
     * @param os 操作系统：android/ios
     * @param targetType 设备类型
     * @return 设备标识列表
     */
    List<String> selectTargetIdsByOsAndType(@Param("os") String os, 
                                            @Param("targetType") Integer targetType);

    /**
     * 根据os查询所有拉黑记录
     * 用于Redis全量同步
     *
     * @param os 操作系统：android/ios
     * @return 拉黑记录列表
     */
    List<LockDeviceBlack> selectAllByOs(@Param("os") String os);

    /**
     * 检查设备是否已拉黑
     * 用于去重检查
     *
     * @param os 操作系统
     * @param targetType 设备类型
     * @param targetId 设备标识
     * @return 拉黑记录（如果存在）
     */
    LockDeviceBlack selectByOsAndTypeAndId(@Param("os") String os,
                                           @Param("targetType") Integer targetType,
                                           @Param("targetId") String targetId);

    /**
     * 分页查询拉黑记录
     * 用于管理后台列表展示
     *
     * @param offset 偏移量
     * @param limit 查询数量
     * @param os 操作系统（可选）
     * @param targetType 设备类型（可选）
     * @return 拉黑记录列表
     */
    List<LockDeviceBlack> selectByPage(@Param("offset") int offset,
                                       @Param("limit") int limit,
                                       @Param("os") String os,
                                       @Param("targetType") Integer targetType);

    /**
     * 统计拉黑记录总数
     * 用于分页查询
     *
     * @param os 操作系统（可选）
     * @param targetType 设备类型（可选）
     * @return 记录总数
     */
    Long countByCondition(@Param("os") String os, @Param("targetType") Integer targetType);
}
