package com.shinet.core.safe.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shinet.core.safe.core.entity.LockDeviceGyRetryRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 设备归因重试记录Mapper接口
 * 使用XML配置复杂SQL，MyBatis-Plus Lambda处理简单查询
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Mapper
public interface LockDeviceGyRetryRecordMapper extends BaseMapper<LockDeviceGyRetryRecord> {

    /**
     * 查询待处理的重试记录
     * 按创建时间倒序，优先处理自然量用户
     *
     * @param limit 查询数量限制
     * @param maxRetryCount 最大重试次数
     * @return 待处理记录列表
     */
    List<LockDeviceGyRetryRecord> selectPendingRecords(@Param("limit") int limit,
                                                       @Param("maxRetryCount") int maxRetryCount);

    /**
     * 更新重试次数
     *
     * @param id 记录ID
     * @param retryCount 新的重试次数
     * @return 更新行数
     */
    int updateRetryCount(@Param("id") Long id, @Param("retryCount") Integer retryCount);

    /**
     * 根据设备信息查询记录
     * 用于去重检查
     *
     * @param deviceId 设备ID
     * @param product 产品标识
     * @param os 操作系统
     * @return 记录对象
     */
    LockDeviceGyRetryRecord selectByDeviceInfo(@Param("deviceId") String deviceId,
                                               @Param("product") String product,
                                               @Param("os") String os);

    /**
     * 批量查询指定时间范围内的记录
     * 用于定时清理
     *
     * @param hours 小时数
     * @return 记录列表
     */
    List<LockDeviceGyRetryRecord> selectExpiredRecords(@Param("hours") int hours);

    /**
     * 批量查询待处理记录（用于定时任务）
     *
     * @param limit 查询数量限制
     * @param maxRetryCount 最大重试次数
     * @param withinHours 时间范围（小时）
     * @return 待处理记录列表
     */
    List<LockDeviceGyRetryRecord> selectBatchPendingRecords(@Param("limit") int limit,
                                                            @Param("maxRetryCount") int maxRetryCount,
                                                            @Param("withinHours") int withinHours);

    /**
     * 统计待处理记录数量
     *
     * @param maxRetryCount 最大重试次数
     * @return 记录数量
     */
    Long countPendingRecords(@Param("maxRetryCount") int maxRetryCount);

    /**
     * 根据产品和操作系统统计记录数量
     *
     * @param product 产品标识
     * @param os 操作系统（可选）
     * @return 记录数量
     */
    Long countByProductAndOs(@Param("product") String product, @Param("os") String os);

    /**
     * 分页查询一小时内创建的is_ocpc=0且未达到最大重试次数的记录
     * 用于定时任务分批处理
     *
     * @param offset 偏移量
     * @param limit 查询数量限制
     * @param withinHours 时间范围（小时）
     * @param maxRetryCount 最大重试次数
     * @return 待处理记录列表
     */
    List<LockDeviceGyRetryRecord> selectRecentRecordsByPage(@Param("offset") int offset,
                                                            @Param("limit") int limit,
                                                            @Param("withinHours") int withinHours,
                                                            @Param("maxRetryCount") int maxRetryCount);
}
