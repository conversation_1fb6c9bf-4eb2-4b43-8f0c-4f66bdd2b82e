package com.shinet.core.safe.core.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.shinet.core.safe.core.entity.LockMultiLocationRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 锁区多位置记录表Mapper接口
 *
 * <AUTHOR>
 * @since 2025-01-24
 */
@Mapper
public interface LockMultiLocationRecordMapper extends BaseMapper<LockMultiLocationRecord> {

    /**
     * 根据设备ID、产品和操作系统查询记录
     * 对应唯一索引：device_id + product + os
     *
     * @param deviceId 设备ID
     * @param product 产品标识
     * @param os 操作系统
     * @return 记录对象
     */
    LockMultiLocationRecord selectByDeviceProductOs(@Param("deviceId") String deviceId, 
                                                    @Param("product") String product, 
                                                    @Param("os") String os);

    /**
     * 根据产品和操作系统查询记录列表
     *
     * @param product 产品标识
     * @param os 操作系统
     * @return 记录列表
     */
    List<LockMultiLocationRecord> selectByProductAndOs(@Param("product") String product, 
                                                       @Param("os") String os);

    /**
     * 根据位置数量范围查询记录
     *
     * @param minCount 最小位置数量
     * @param maxCount 最大位置数量
     * @return 记录列表
     */
    List<LockMultiLocationRecord> selectByLocationCountRange(@Param("minCount") Integer minCount, 
                                                             @Param("maxCount") Integer maxCount);

    /**
     * 统计指定产品和操作系统的记录数量
     *
     * @param product 产品标识
     * @param os 操作系统
     * @return 记录数量
     */
    Long countByProductAndOs(@Param("product") String product, @Param("os") String os);

    /**
     * 批量插入记录
     *
     * @param list 记录列表
     * @return 插入行数
     */
    int batchInsert(@Param("list") List<LockMultiLocationRecord> list);

    /**
     * 根据设备ID列表批量查询
     *
     * @param deviceIds 设备ID列表
     * @param product 产品标识
     * @param os 操作系统
     * @return 记录列表
     */
    List<LockMultiLocationRecord> selectByDeviceIds(@Param("deviceIds") List<String> deviceIds,
                                                    @Param("product") String product,
                                                    @Param("os") String os);
}
