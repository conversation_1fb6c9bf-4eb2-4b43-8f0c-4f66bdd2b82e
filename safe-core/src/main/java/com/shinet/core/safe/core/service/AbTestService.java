package com.shinet.core.safe.core.service;

import com.shinet.core.safe.core.dto.AbTestRequestDTO;
import com.shinet.core.safe.core.vo.AbTestResponseVO;

/**
 * AB实验服务接口
 * 
 * <AUTHOR>
 */
public interface AbTestService {
    
    /**
     * 获取AB实验分组ID
     * 
     * @param request 请求参数
     * @return 分组ID，异常时返回默认分组0
     */
    Integer getAbTestCategoryId(AbTestRequestDTO request);
    
    /**
     * 获取AB实验完整响应
     * 
     * @param request 请求参数
     * @return AB实验响应对象，异常时返回默认分组0的响应
     */
    AbTestResponseVO getAbTestResponse(AbTestRequestDTO request);
}
