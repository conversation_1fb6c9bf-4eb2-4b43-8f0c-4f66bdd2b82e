package com.shinet.core.safe.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shinet.core.safe.core.dto.DeviceRetryRecordDTO;
import com.shinet.core.safe.core.entity.LockDeviceGyRetryRecord;

import java.util.List;

/**
 * 设备归因重试记录服务接口
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
public interface DeviceGyRetryRecordService extends IService<LockDeviceGyRetryRecord> {

    /**
     * 异步记录非OCPC用户设备信息
     *
     * @param deviceRecord 设备记录DTO
     * @return 是否记录成功
     */
    boolean recordNonOcpcDevice(DeviceRetryRecordDTO deviceRecord);

    /**
     * 查询待处理的重试记录
     * 用于定时任务批量处理
     *
     * @param limit 查询数量限制
     * @return 待处理记录列表
     */
    List<LockDeviceGyRetryRecord> getPendingRecords(int limit);

    /**
     * 查询指定时间范围内的待处理记录
     * 用于定时任务，只处理最近创建的记录
     *
     * @param limit 查询数量限制
     * @param withinHours 时间范围（小时）
     * @return 待处理记录列表
     */
    List<LockDeviceGyRetryRecord> getPendingRecordsWithinHours(int limit, int withinHours);

    /**
     * 分页查询一小时内创建的待处理记录
     * 用于定时任务分批处理所有符合条件的记录
     *
     * @param offset 偏移量
     * @param limit 查询数量限制
     * @param withinHours 时间范围（小时）
     * @return 待处理记录列表
     */
    List<LockDeviceGyRetryRecord> getRecentRecordsByPage(int offset, int limit, int withinHours);

    /**
     * 更新重试次数
     * 
     * @param id 记录ID
     * @param retryCount 新的重试次数
     * @return 是否更新成功
     */
    boolean updateRetryCount(Long id, Integer retryCount);

    /**
     * 根据设备信息查询记录
     * 用于去重检查
     * 
     * @param deviceId 设备ID
     * @param product 产品标识
     * @param os 操作系统
     * @return 记录对象，不存在返回null
     */
    LockDeviceGyRetryRecord getByDeviceInfo(String deviceId, String product, String os);

    /**
     * 删除过期记录
     * 用于定期清理，避免数据积累
     * 
     * @param hours 保留小时数
     * @return 删除记录数
     */
    int deleteExpiredRecords(int hours);

    /**
     * 批量保存记录
     * 用于批量处理场景
     * 
     * @param records 记录列表
     * @return 保存成功数量
     */
    int batchSave(List<LockDeviceGyRetryRecord> records);
}
