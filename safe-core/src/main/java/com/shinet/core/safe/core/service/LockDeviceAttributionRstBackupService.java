package com.shinet.core.safe.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.shinet.core.safe.core.entity.LockDeviceAttributionRstBackup;

import java.util.List;

/**
 * 设备归因备份服务接口
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
public interface LockDeviceAttributionRstBackupService extends IService<LockDeviceAttributionRstBackup> {

    /**
     * 创建备份记录
     * 
     * @param sourceTable 来源表名
     * @param sourceId 来源记录ID
     * @param deviceId 设备ID
     * @param product 产品标识
     * @param os 操作系统
     * @param originalData 原始数据JSON
     * @param retryRecordId 关联重试记录ID
     * @return 是否创建成功
     */
    boolean createBackup(String sourceTable, String sourceId, String deviceId, 
                        String product, String os, String originalData, Long retryRecordId);

    /**
     * 根据来源信息查询备份记录
     * 
     * @param sourceTable 来源表名
     * @param sourceId 来源记录ID
     * @return 备份记录
     */
    LockDeviceAttributionRstBackup getBySource(String sourceTable, String sourceId);

    /**
     * 根据设备信息查询备份记录
     * 
     * @param deviceId 设备ID
     * @param product 产品标识
     * @param os 操作系统
     * @return 备份记录列表
     */
    List<LockDeviceAttributionRstBackup> getByDeviceInfo(String deviceId, String product, String os);

    /**
     * 根据重试记录ID查询备份记录
     * 
     * @param retryRecordId 重试记录ID
     * @return 备份记录列表
     */
    List<LockDeviceAttributionRstBackup> getByRetryRecordId(Long retryRecordId);

    /**
     * 删除过期备份记录
     * 
     * @param days 保留天数
     * @return 删除记录数
     */
    int deleteExpiredBackups(int days);

    /**
     * 批量保存备份记录
     * 
     * @param backups 备份记录列表
     * @return 保存成功数量
     */
    int batchSave(List<LockDeviceAttributionRstBackup> backups);
}
