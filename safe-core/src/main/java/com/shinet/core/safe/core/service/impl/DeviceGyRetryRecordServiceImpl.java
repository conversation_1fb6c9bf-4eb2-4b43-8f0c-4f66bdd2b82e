package com.shinet.core.safe.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.safe.core.constants.DeviceRetryConstants;
import com.shinet.core.safe.core.dto.DeviceRetryRecordDTO;
import com.shinet.core.safe.core.entity.LockDeviceGyRetryRecord;
import com.shinet.core.safe.core.mapper.LockDeviceGyRetryRecordMapper;
import com.shinet.core.safe.core.service.DeviceGyRetryRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备归因重试记录服务实现
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@Service
public class DeviceGyRetryRecordServiceImpl extends ServiceImpl<LockDeviceGyRetryRecordMapper, LockDeviceGyRetryRecord>
        implements DeviceGyRetryRecordService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean recordNonOcpcDevice(DeviceRetryRecordDTO deviceRecord) {
        // 参数校验
        if (deviceRecord == null || !deviceRecord.isValid()) {
            log.warn("设备记录参数无效: {}", deviceRecord);
            return false;
        }

        // 去重检查
        LambdaQueryWrapper<LockDeviceGyRetryRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LockDeviceGyRetryRecord::getDeviceId, deviceRecord.getDeviceId())
                .eq(LockDeviceGyRetryRecord::getProduct, deviceRecord.getProduct())
                .eq(LockDeviceGyRetryRecord::getOs, deviceRecord.getOs())
                .last("LIMIT 1");

        LockDeviceGyRetryRecord existingRecord = getOne(queryWrapper);
        if (existingRecord != null) {
            log.debug("设备记录已存在， {}", deviceRecord.getUniqueKey());
            // 说明归因查询异常，进行重试
            existingRecord.setIsOcpc(0);
            existingRecord.setUpdateTime(new Date());
            return this.updateById(existingRecord);
        }

        // 创建新记录
        LockDeviceGyRetryRecord record = new LockDeviceGyRetryRecord()
                .setBasicInfo(deviceRecord.getProduct(), deviceRecord.getDeviceId(), deviceRecord.getOs())
                .setCreateTime(new Date())
                .setUpdateTime(new Date());

        boolean success = save(record);

        if (!success) {
            log.error("设备归因重试记录创建失败: {}", deviceRecord.getUniqueKey());
        }

        return success;

    }

    @Override
    public List<LockDeviceGyRetryRecord> getPendingRecords(int limit) {
        try {
            return baseMapper.selectPendingRecords(limit, DeviceRetryConstants.MAX_RETRY_COUNT);
        } catch (Exception e) {
            log.error("查询待处理重试记录异常, limit={}", limit, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<LockDeviceGyRetryRecord> getPendingRecordsWithinHours(int limit, int withinHours) {
        try {
            return baseMapper.selectBatchPendingRecords(limit, DeviceRetryConstants.MAX_RETRY_COUNT, withinHours);
        } catch (Exception e) {
            log.error("查询时间范围内待处理记录异常, limit={}, withinHours={}", limit, withinHours, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<LockDeviceGyRetryRecord> getRecentRecordsByPage(int offset, int limit, int withinHours) {
        return baseMapper.selectRecentRecordsByPage(offset, limit, withinHours, DeviceRetryConstants.MAX_RETRY_COUNT);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRetryCount(Long id, Integer retryCount) {
        try {
            int updateRows = baseMapper.updateRetryCount(id, retryCount);
            return updateRows > 0;
        } catch (Exception e) {
            log.error("更新重试次数异常: id={}, retryCount={}", id, retryCount, e);
            return false;
        }
    }

    @Override
    public LockDeviceGyRetryRecord getByDeviceInfo(String deviceId, String product, String os) {
        try {
            // 使用Lambda查询替代XML中的简单查询
            LambdaQueryWrapper<LockDeviceGyRetryRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(LockDeviceGyRetryRecord::getDeviceId, deviceId)
                    .eq(LockDeviceGyRetryRecord::getProduct, product)
                    .eq(LockDeviceGyRetryRecord::getOs, os)
                    .last("LIMIT 1");
            return getOne(queryWrapper);
        } catch (Exception e) {
            log.error("根据设备信息查询记录异常: deviceId={}, product={}, os={}", deviceId, product, os, e);
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteExpiredRecords(int hours) {
        try {
            List<LockDeviceGyRetryRecord> expiredRecords = baseMapper.selectExpiredRecords(hours);
            if (expiredRecords.isEmpty()) {
                return 0;
            }

            List<Long> ids = expiredRecords.stream().map(LockDeviceGyRetryRecord::getId).collect(Collectors.toList());
            boolean success = removeByIds(ids);

            int deletedCount = success ? expiredRecords.size() : 0;
            log.info("清理过期重试记录: 删除{}条记录", deletedCount);
            return deletedCount;

        } catch (Exception e) {
            log.error("删除过期记录异常: hours={}", hours, e);
            return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSave(List<LockDeviceGyRetryRecord> records) {
        try {
            if (records == null || records.isEmpty()) {
                return 0;
            }

            boolean success = saveBatch(records);
            return success ? records.size() : 0;

        } catch (Exception e) {
            log.error("批量保存记录异常: size={}", records.size(), e);
            return 0;
        }
    }
}
