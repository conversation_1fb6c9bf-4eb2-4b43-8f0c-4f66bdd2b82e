package com.shinet.core.safe.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.shinet.core.safe.core.constants.DeviceRetryConstants;
import com.shinet.core.safe.core.entity.LockDeviceAttributionRstBackup;
import com.shinet.core.safe.core.mapper.LockDeviceAttributionRstBackupMapper;
import com.shinet.core.safe.core.service.LockDeviceAttributionRstBackupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备归因备份服务实现
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@Service
public class LockDeviceAttributionRstBackupServiceImpl 
        extends ServiceImpl<LockDeviceAttributionRstBackupMapper, LockDeviceAttributionRstBackup> 
        implements LockDeviceAttributionRstBackupService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean createBackup(String sourceTable, String sourceId, String deviceId, 
                               String product, String os, String originalData, Long retryRecordId) {
        try {
            // 参数校验
            if (sourceTable == null || sourceId == null || deviceId == null || 
                product == null || os == null || originalData == null) {
                log.warn("备份记录参数不完整: sourceTable={}, sourceId={}, deviceId={}", 
                        sourceTable, sourceId, deviceId);
                return false;
            }


            // 创建备份记录
            LockDeviceAttributionRstBackup backup = LockDeviceAttributionRstBackup.createBackup(
                    sourceTable, sourceId, deviceId, product, os, originalData, retryRecordId);

            QueryWrapper<LockDeviceAttributionRstBackup> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda()
                    .eq(LockDeviceAttributionRstBackup::getOs, os)
                    .eq(LockDeviceAttributionRstBackup::getProduct, product)
                    .eq(LockDeviceAttributionRstBackup::getDeviceId, deviceId)
                    .orderByDesc(LockDeviceAttributionRstBackup::getUpdateTime)
                    .last("LIMIT 1")
            ;

            LockDeviceAttributionRstBackup one = this.getOne(queryWrapper);

            boolean success = false;
            if (one == null) {
                success = save(backup);
            } else {
                UpdateWrapper<LockDeviceAttributionRstBackup> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda()
                        .eq(LockDeviceAttributionRstBackup::getOs, os)
                        .eq(LockDeviceAttributionRstBackup::getProduct, product)
                        .eq(LockDeviceAttributionRstBackup::getDeviceId, deviceId)
                        .set(LockDeviceAttributionRstBackup::getOriginalData, originalData)
                        .set(LockDeviceAttributionRstBackup::getUpdateTime, new Date());
                success = this.update(updateWrapper);
            }

            
            if (success) {
                log.info("{}设备归因备份记录创建成功: sourceTable={}, sourceId={}, deviceId={}", 
                        DeviceRetryConstants.LogTag.DATA_BACKUP, sourceTable, sourceId, deviceId);
            } else {
                log.error("{}设备归因备份记录创建失败: sourceTable={}, sourceId={}", 
                         DeviceRetryConstants.LogTag.DATA_BACKUP, sourceTable, sourceId);
            }
            
            return success;
            
        } catch (Exception e) {
            log.error("{}创建备份记录异常: sourceTable={}, sourceId={}", 
                     DeviceRetryConstants.LogTag.DATA_BACKUP, sourceTable, sourceId, e);
            return false;
        }
    }

    @Override
    public LockDeviceAttributionRstBackup getBySource(String sourceTable, String sourceId) {
        try {
            LambdaQueryWrapper<LockDeviceAttributionRstBackup> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(LockDeviceAttributionRstBackup::getSourceTable, sourceTable)
                       .eq(LockDeviceAttributionRstBackup::getSourceId, sourceId)
                       .last("LIMIT 1");
            return getOne(queryWrapper);
        } catch (Exception e) {
            log.error("根据来源信息查询备份记录异常: sourceTable={}, sourceId={}", sourceTable, sourceId, e);
            return null;
        }
    }

    @Override
    public List<LockDeviceAttributionRstBackup> getByDeviceInfo(String deviceId, String product, String os) {
        try {
            return baseMapper.selectByDeviceInfo(deviceId, product, os);
        } catch (Exception e) {
            log.error("根据设备信息查询备份记录异常: deviceId={}, product={}, os={}", deviceId, product, os, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<LockDeviceAttributionRstBackup> getByRetryRecordId(Long retryRecordId) {
        try {
            LambdaQueryWrapper<LockDeviceAttributionRstBackup> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(LockDeviceAttributionRstBackup::getRetryRecordId, retryRecordId)
                       .orderByDesc(LockDeviceAttributionRstBackup::getCreateTime);
            return list(queryWrapper);
        } catch (Exception e) {
            log.error("根据重试记录ID查询备份记录异常: retryRecordId={}", retryRecordId, e);
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteExpiredBackups(int days) {
        try {
            List<LockDeviceAttributionRstBackup> expiredBackups = baseMapper.selectExpiredRecords(days);
            if (expiredBackups.isEmpty()) {
                return 0;
            }
            
            List<Long> ids = expiredBackups.stream().map(LockDeviceAttributionRstBackup::getId).collect(Collectors.toList());
            boolean success = removeByIds(ids);
            
            int deletedCount = success ? expiredBackups.size() : 0;
            log.info("{}清理过期备份记录: 删除{}条记录", 
                    DeviceRetryConstants.LogTag.DATA_BACKUP, deletedCount);
            return deletedCount;
            
        } catch (Exception e) {
            log.error("{}删除过期备份记录异常: days={}", 
                     DeviceRetryConstants.LogTag.DATA_BACKUP, days, e);
            return 0;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchSave(List<LockDeviceAttributionRstBackup> backups) {
        try {
            if (backups == null || backups.isEmpty()) {
                return 0;
            }
            
            boolean success = saveBatch(backups);
            return success ? backups.size() : 0;
            
        } catch (Exception e) {
            log.error("{}批量保存备份记录异常: size={}", 
                     DeviceRetryConstants.LogTag.DATA_BACKUP, backups.size(), e);
            return 0;
        }
    }
}
