package com.shinet.core.safe.core.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import lombok.extern.slf4j.Slf4j;

/**
 * JSON工具类
 * 用于设备归因重试机制中的数据序列化和反序列化
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
public class JsonUtils {

    /**
     * 对象转JSON字符串
     * 
     * @param obj 要转换的对象
     * @return JSON字符串，转换失败返回null
     */
    public static String toJsonString(Object obj) {
        if (obj == null) {
            return null;
        }
        
        try {
            return JSON.toJSONString(obj);
        } catch (JSONException e) {
            log.error("对象转JSON字符串失败: {}", obj.getClass().getSimpleName(), e);
            return null;
        }
    }

    /**
     * JSON字符串转对象
     * 
     * @param jsonString JSON字符串
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 转换后的对象，转换失败返回null
     */
    public static <T> T parseObject(String jsonString, Class<T> clazz) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }
        
        try {
            return JSON.parseObject(jsonString, clazz);
        } catch (JSONException e) {
            log.error("JSON字符串转对象失败: targetClass={}, jsonString={}", 
                     clazz.getSimpleName(), jsonString, e);
            return null;
        }
    }

    /**
     * 检查字符串是否为有效的JSON格式
     * 
     * @param jsonString 待检查的字符串
     * @return 是否为有效JSON
     */
    public static boolean isValidJson(String jsonString) {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return false;
        }
        
        try {
            JSON.parse(jsonString);
            return true;
        } catch (JSONException e) {
            return false;
        }
    }

    /**
     * 安全地转换对象为JSON字符串
     * 如果转换失败，返回默认值
     * 
     * @param obj 要转换的对象
     * @param defaultValue 默认值
     * @return JSON字符串或默认值
     */
    public static String toJsonStringSafe(Object obj, String defaultValue) {
        String result = toJsonString(obj);
        return result != null ? result : defaultValue;
    }

    /**
     * 安全地解析JSON字符串为对象
     * 如果解析失败，返回默认值
     * 
     * @param jsonString JSON字符串
     * @param clazz 目标类型
     * @param defaultValue 默认值
     * @param <T> 泛型类型
     * @return 解析后的对象或默认值
     */
    public static <T> T parseObjectSafe(String jsonString, Class<T> clazz, T defaultValue) {
        T result = parseObject(jsonString, clazz);
        return result != null ? result : defaultValue;
    }

    /**
     * 格式化JSON字符串（美化输出）
     * 
     * @param jsonString 原始JSON字符串
     * @return 格式化后的JSON字符串
     */
    public static String formatJson(String jsonString) {
        if (!isValidJson(jsonString)) {
            return jsonString;
        }
        
        try {
            Object obj = JSON.parse(jsonString);
            return JSON.toJSONString(obj, true);
        } catch (JSONException e) {
            log.warn("JSON格式化失败: {}", jsonString, e);
            return jsonString;
        }
    }

    /**
     * 压缩JSON字符串（移除空格和换行）
     * 
     * @param jsonString 原始JSON字符串
     * @return 压缩后的JSON字符串
     */
    public static String compactJson(String jsonString) {
        if (!isValidJson(jsonString)) {
            return jsonString;
        }
        
        try {
            Object obj = JSON.parse(jsonString);
            return JSON.toJSONString(obj);
        } catch (JSONException e) {
            log.warn("JSON压缩失败: {}", jsonString, e);
            return jsonString;
        }
    }
}
