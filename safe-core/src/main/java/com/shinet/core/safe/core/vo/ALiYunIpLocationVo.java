package com.shinet.core.safe.core.vo;

import lombok.Data;

@Data
public class ALiYunIpLocationVo {
//{"msg":"成功","success":true,"code":200,"data":{"orderNo":"202508011639368244467","province":"江苏省","city":"苏州市","nation":"中国","ip":"**************","isp":"移动"}}
    private Integer code;
    private String msg;
    private Boolean success;
    private ALiYunIpLocationData data;
}

@Data
class ALiYunIpLocationData {
    private String ip;
    private String orderNo;
    private String province;
    private String city;
    private String nation;
    private String isp;
}
