package com.shinet.core.safe.core.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AB实验数据VO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AbTestDataVO {
    
    /**
     * 分组ID
     */
    private Integer categoryId;
    
    private String deviceId;
    
    private String loginTime;

    private String testTagResult;
    
    /**
     * 创建默认数据（分组0）
     */
    public static AbTestDataVO createDefault() {
        return AbTestDataVO.builder()
                .categoryId(0)
                .build();
    }
}
