package com.shinet.core.safe.core.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * AB实验响应VO
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AbTestResponseVO {

    /**
     * 实验数据
     */
    private AbTestDataVO data;
    
    /**
     * 响应状态码
     */
    private Integer status;
    
    /**
     * 响应消息
     */
    private String message;

    /**
     * 创建默认响应（分组0）
     */
    public static AbTestResponseVO createDefault() {
        return AbTestResponseVO.builder()
                .status(200)
                .message("default")
                .data(AbTestDataVO.createDefault())
                .build();
    }
}
