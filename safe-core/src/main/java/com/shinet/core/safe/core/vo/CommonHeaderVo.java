package com.shinet.core.safe.core.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 通用请求头VO - 用于设备拉黑检测
 *
 * <AUTHOR>
 * @since 2025-01-24
 */
@Data
public class CommonHeaderVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 操作系统：android/ios
     */
    private String os;

    /**
     * IP地址
     */
    private String ip;

    /**
     * OAID（Android专用）
     */
    private String oaid;

    /**
     * CAID（iOS专用）
     */
    private String caid;

    /**
     * IDFA（iOS专用）
     */
    private String idfa;

    private String product;

    /**
     * 设备ID
     */
    private String deviceId;

    private String imei;

    private String androidId;

    /**
     * 获取操作系统整型值
     *
     * @return 0-Android, 1-iOS
     */
    public Integer getIntOs() {
        try {
            return "android".equalsIgnoreCase(os) ? 0 : 1;
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 检查是否为Android系统
     */
    public boolean isAndroid() {
        return "android".equalsIgnoreCase(os);
    }

    /**
     * 检查是否为iOS系统
     */
    public boolean isIos() {
        return "ios".equalsIgnoreCase(os);
    }

    /**
     * 从CommonHeaderDTO转换（兼容现有系统）
     */
    public static CommonHeaderVo fromCommonHeaderDTO(Object commonHeaderDTO) {
        if (commonHeaderDTO == null) {
            return null;
        }

        CommonHeaderVo vo = new CommonHeaderVo();
        try {
            // 使用反射获取字段值，避免直接依赖safe-biz模块
            Class<?> clazz = commonHeaderDTO.getClass();

            vo.setOs(getFieldValue(commonHeaderDTO, clazz, "os"));
            vo.setIp(getFieldValue(commonHeaderDTO, clazz, "ip"));
            vo.setOaid(getFieldValue(commonHeaderDTO, clazz, "oaid"));
            vo.setCaid(getFieldValue(commonHeaderDTO, clazz, "caid"));
            vo.setIdfa(getFieldValue(commonHeaderDTO, clazz, "idfa"));
            vo.setProduct(getFieldValue(commonHeaderDTO, clazz, "product"));
            vo.setDeviceId(getFieldValue(commonHeaderDTO, clazz, "deviceId"));
            vo.setImei(getFieldValue(commonHeaderDTO, clazz, "imei"));
            vo.setAndroidId(getFieldValue(commonHeaderDTO, clazz, "androidId"));

        } catch (Exception e) {
            // 转换失败时返回空对象，不影响主流程
        }

        return vo;
    }

    /**
     * 反射获取字段值
     */
    private static String getFieldValue(Object obj, Class<?> clazz, String fieldName) {
        try {
            java.lang.reflect.Field field = clazz.getDeclaredField(fieldName);
            field.setAccessible(true);
            Object value = field.get(obj);
            return value != null ? value.toString() : null;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 设置基础信息的便捷方法
     */
    public CommonHeaderVo setBasicInfo(String os, String ip, String product) {
        this.os = os;
        this.ip = ip;
        this.product = product;
        return this;
    }
}
