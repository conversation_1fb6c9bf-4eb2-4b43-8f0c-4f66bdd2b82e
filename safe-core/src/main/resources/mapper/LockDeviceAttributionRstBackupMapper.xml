<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shinet.core.safe.core.mapper.LockDeviceAttributionRstBackupMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.shinet.core.safe.core.entity.LockDeviceAttributionRstBackup">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="source_table" property="sourceTable" jdbcType="VARCHAR"/>
        <result column="source_id" property="sourceId" jdbcType="VARCHAR"/>
        <result column="device_id" property="deviceId" jdbcType="VARCHAR"/>
        <result column="product" property="product" jdbcType="VARCHAR"/>
        <result column="os" property="os" jdbcType="VARCHAR"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="original_data" property="originalData" jdbcType="LONGVARCHAR"/>
        <result column="delete_reason" property="deleteReason" jdbcType="VARCHAR"/>
        <result column="retry_record_id" property="retryRecordId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, source_table, source_id, device_id, product, os, user_id, 
        original_data, delete_reason, retry_record_id, create_time, update_time
    </sql>

    <!-- 根据来源表和记录ID查询备份记录 -->
    <select id="selectBySource" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM lock_device_attribution_rst_backup
        WHERE source_table = #{sourceTable} 
          AND source_id = #{sourceId}
        LIMIT 1
    </select>

    <!-- 根据设备信息查询备份记录 -->
    <select id="selectByDeviceInfo" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM lock_device_attribution_rst_backup
        WHERE device_id = #{deviceId} 
          AND product = #{product} 
          AND os = #{os}
        ORDER BY create_time DESC
    </select>

    <!-- 查询过期的备份记录 -->
    <select id="selectExpiredRecords" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM lock_device_attribution_rst_backup
        WHERE create_time &lt; DATE_SUB(NOW(), INTERVAL #{days} DAY)
        ORDER BY create_time ASC
    </select>

    <!-- 根据重试记录ID查询备份记录 -->
    <select id="selectByRetryRecordId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM lock_device_attribution_rst_backup
        WHERE retry_record_id = #{retryRecordId}
        ORDER BY create_time DESC
    </select>

    <!-- 统计备份记录数量 -->
    <select id="countBackupRecords" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM lock_device_attribution_rst_backup
        <where>
            <if test="sourceTable != null and sourceTable != ''">
                AND source_table = #{sourceTable}
            </if>
            <if test="startTime != null">
                AND create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND create_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <!-- 批量查询备份记录（分页） -->
    <select id="selectBackupRecordsByPage" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM lock_device_attribution_rst_backup
        <where>
            <if test="sourceTable != null and sourceTable != ''">
                AND source_table = #{sourceTable}
            </if>
            <if test="product != null and product != ''">
                AND product = #{product}
            </if>
            <if test="os != null and os != ''">
                AND os = #{os}
            </if>
        </where>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

</mapper>
