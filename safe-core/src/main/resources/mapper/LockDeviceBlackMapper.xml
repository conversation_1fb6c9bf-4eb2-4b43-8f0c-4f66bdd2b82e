<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shinet.core.safe.core.mapper.LockDeviceBlackMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.shinet.core.safe.core.entity.LockDeviceBlack">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="os" property="os" jdbcType="VARCHAR"/>
        <result column="target_type" property="targetType" jdbcType="TINYINT"/>
        <result column="target_id" property="targetId" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, os, target_type, target_id, remark, create_time, update_time
    </sql>

    <!-- 根据os和设备标识列表批量查询 -->
    <select id="selectByOsAndTargetIds" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM lock_device_black
        WHERE os = #{os}
        <if test="targetIds != null and targetIds.size() > 0">
            AND target_id IN
            <foreach collection="targetIds" item="targetId" open="(" separator="," close=")">
                #{targetId}
            </foreach>
        </if>
    </select>

    <!-- 批量插入设备拉黑记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO lock_device_black (os, target_type, target_id, remark, create_time, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.os}, #{item.targetType}, #{item.targetId}, #{item.remark}, NOW(), NOW())
        </foreach>
    </insert>

    <!-- 根据os和target_type查询所有设备标识 -->
    <select id="selectTargetIdsByOsAndType" resultType="java.lang.String">
        SELECT target_id
        FROM lock_device_black
        WHERE os = #{os}
        <if test="targetType != null">
            AND target_type = #{targetType}
        </if>
    </select>

    <!-- 根据os查询所有拉黑记录 -->
    <select id="selectAllByOs" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM lock_device_black
        WHERE os = #{os}
        ORDER BY create_time DESC
    </select>

    <!-- 检查设备是否已拉黑 -->
    <select id="selectByOsAndTypeAndId" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM lock_device_black
        WHERE os = #{os}
          AND target_type = #{targetType}
          AND target_id = #{targetId}
        LIMIT 1
    </select>

    <!-- 分页查询拉黑记录 -->
    <select id="selectByPage" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM lock_device_black
        <where>
            <if test="os != null and os != ''">
                AND os = #{os}
            </if>
            <if test="targetType != null">
                AND target_type = #{targetType}
            </if>
        </where>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计拉黑记录总数 -->
    <select id="countByCondition" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM lock_device_black
        <where>
            <if test="os != null and os != ''">
                AND os = #{os}
            </if>
            <if test="targetType != null">
                AND target_type = #{targetType}
            </if>
        </where>
    </select>

</mapper>
