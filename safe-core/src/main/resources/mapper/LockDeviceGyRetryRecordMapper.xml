<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shinet.core.safe.core.mapper.LockDeviceGyRetryRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.shinet.core.safe.core.entity.LockDeviceGyRetryRecord">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="product" property="product" jdbcType="VARCHAR"/>
        <result column="device_id" property="deviceId" jdbcType="VARCHAR"/>
        <result column="os" property="os" jdbcType="VARCHAR"/>
        <result column="is_ocpc" property="isOcpc" jdbcType="TINYINT"/>
        <result column="retry_count" property="retryCount" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, product, device_id, os, is_ocpc, retry_count, create_time, update_time
    </sql>

    <!-- 查询待处理的重试记录 -->
    <select id="selectPendingRecords" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM lock_device_gy_retry_record
        WHERE is_ocpc = 0
          AND create_time &gt;= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 更新重试次数 -->
    <update id="updateRetryCount">
        UPDATE lock_device_gy_retry_record
        SET retry_count = #{retryCount}, 
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <!-- 根据设备信息查询记录 -->
    <select id="selectByDeviceInfo" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM lock_device_gy_retry_record
        WHERE device_id = #{deviceId} 
          AND product = #{product} 
          AND os = #{os}
        LIMIT 1
    </select>

    <!-- 批量查询指定时间范围内的记录 -->
    <select id="selectExpiredRecords" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM lock_device_gy_retry_record
        WHERE create_time &lt; DATE_SUB(NOW(), INTERVAL #{hours} HOUR)
    </select>

    <!-- 批量查询待处理记录（用于定时任务） -->
    <select id="selectBatchPendingRecords" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM lock_device_gy_retry_record
        WHERE retry_count &lt; #{maxRetryCount}
          AND is_ocpc = 0
          AND create_time &gt;= DATE_SUB(NOW(), INTERVAL #{withinHours} HOUR)
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 统计待处理记录数量 -->
    <select id="countPendingRecords" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM lock_device_gy_retry_record
        WHERE retry_count &lt; #{maxRetryCount}
    </select>

    <!-- 根据产品和操作系统统计记录数量 -->
    <select id="countByProductAndOs" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM lock_device_gy_retry_record
        WHERE product = #{product}
        <if test="os != null and os != ''">
            AND os = #{os}
        </if>
    </select>

    <!-- 分页查询一小时内创建的is_ocpc=0且未达到最大重试次数的记录 -->
    <select id="selectRecentRecordsByPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM lock_device_gy_retry_record
        WHERE is_ocpc = 0
          AND retry_count &lt; #{maxRetryCount}
          AND update_time &gt;= DATE_SUB(NOW(), INTERVAL #{withinHours} HOUR)
        ORDER BY update_time DESC
        LIMIT #{offset}, #{limit}
    </select>

</mapper>
