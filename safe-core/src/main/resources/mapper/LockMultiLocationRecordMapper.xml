<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.shinet.core.safe.core.mapper.LockMultiLocationRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.shinet.core.safe.core.entity.LockMultiLocationRecord">
        <id column="id" property="id" />
        <result column="product" property="product" />
        <result column="device_id" property="deviceId" />
        <result column="os" property="os" />
        <result column="location_count" property="locationCount" />
        <result column="location_info" property="locationInfo" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, product, device_id, os, location_count, location_info, create_time, update_time
    </sql>

    <!-- 根据设备ID、产品和操作系统查询记录 -->
    <select id="selectByDeviceProductOs" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM lock_multi_location_record
        WHERE device_id = #{deviceId}
          AND product = #{product}
          AND os = #{os}
        LIMIT 1
    </select>

    <!-- 根据产品和操作系统查询记录列表 -->
    <select id="selectByProductAndOs" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM lock_multi_location_record
        WHERE product = #{product}
          AND os = #{os}
        ORDER BY create_time DESC
    </select>

    <!-- 根据位置数量范围查询记录 -->
    <select id="selectByLocationCountRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM lock_multi_location_record
        WHERE location_count BETWEEN #{minCount} AND #{maxCount}
        ORDER BY location_count DESC, create_time DESC
    </select>

    <!-- 统计指定产品和操作系统的记录数量 -->
    <select id="countByProductAndOs" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM lock_multi_location_record
        WHERE product = #{product}
          AND os = #{os}
    </select>

    <!-- 批量插入记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO lock_multi_location_record
        (product, device_id, os, location_count, location_info, create_time, update_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.product}, #{item.deviceId}, #{item.os}, #{item.locationCount}, 
             #{item.locationInfo}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

    <!-- 根据设备ID列表批量查询 -->
    <select id="selectByDeviceIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM lock_multi_location_record
        WHERE product = #{product}
          AND os = #{os}
          AND device_id IN
        <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
            #{deviceId}
        </foreach>
        ORDER BY create_time DESC
    </select>

</mapper>
