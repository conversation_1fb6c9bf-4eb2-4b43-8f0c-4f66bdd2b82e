<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.shinet.core</groupId>
		<artifactId>core-safe</artifactId>
		<version>${revision}</version>
	</parent>

	<artifactId>safe-processor</artifactId>
	<version>${revision}</version>

	<name>safe-processor</name>

	<description>safe data processor service</description>
	<properties>
		<java.version>1.8</java.version>
<!--		<revision>0.0.1-SNAPSHOT</revision>-->
	</properties>
	<dependencies>
		<!-- 基础核心依赖 -->
<!--		<dependency>-->
<!--			<groupId>com.shinet.core.base</groupId>-->
<!--			<artifactId>base-core</artifactId>-->
<!--			<version>0.1.5</version>-->
<!--		</dependency>-->
		<dependency>
			<groupId>com.shinet.core</groupId>
			<artifactId>safe-core</artifactId>
			<version>${revision}</version>
		</dependency>

		<!-- Spring Boot 基础依赖 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jdbc</artifactId>
		</dependency>

		<!-- MyBatis Plus 依赖 -->
<!--		<dependency>-->
<!--			<groupId>com.baomidou</groupId>-->
<!--			<artifactId>mybatis-plus-boot-starter</artifactId>-->
<!--		</dependency>-->

		<!-- Kafka 依赖 -->
		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>kafka-clients</artifactId>
		</dependency>

		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
		</dependency>
		<!-- 可选, 用于进程内存使用图表 -->
		<dependency>
			<groupId>io.github.mweirauch</groupId>
			<artifactId>micrometer-jvm-extras</artifactId>
			<version>0.2.0</version>
		</dependency>
		<dependency>
			<groupId>top.zrbcool</groupId>
			<artifactId>pepper-metrics-servlet</artifactId>
			<version>1.0.24</version>
		</dependency>
		<dependency>
			<groupId>top.zrbcool</groupId>
			<artifactId>pepper-metrics-ds-prometheus</artifactId>
			<version>1.0.24</version>
		</dependency>

		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>

		<!-- 测试依赖 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
			<scope>test</scope>
		</dependency>
<!--		<dependency>-->
<!--			<groupId>org.testcontainers</groupId>-->
<!--			<artifactId>junit-jupiter</artifactId>-->
<!--			<scope>test</scope>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>org.testcontainers</groupId>-->
<!--			<artifactId>kafka</artifactId>-->
<!--			<scope>test</scope>-->
<!--		</dependency>-->
	</dependencies>

	<build>
		<finalName>safe-processor</finalName>
		<resources>
			<resource>
				<directory>src/main/java</directory>
				<includes>
					<include>**/*.xml</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/resources</directory>
				<includes>
					<include>**/*.*</include>
				</includes>
				<filtering>true</filtering>
			</resource>
		</resources>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>org.eclipse.m2e</groupId>
					<artifactId>lifecycle-mapping</artifactId>
					<version>1.0.0</version>
					<configuration>
						<lifecycleMappingMetadata>
							<pluginExecutions>
								<pluginExecution>
									<pluginExecutionFilter>
										<groupId>org.codehaus.mojo</groupId>
										<artifactId>flatten-maven-plugin</artifactId>
										<versionRange>[1.0.0,)</versionRange>
										<goals>
											<goal>flatten</goal>
										</goals>
									</pluginExecutionFilter>
									<action>
										<ignore/>
									</action>
								</pluginExecution>
							</pluginExecutions>
						</lifecycleMappingMetadata>
						<mainClass>com.shinet.core.safe.processor.SafeProcessorApplication</mainClass>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<mainClass>com.shinet.core.safe.processor.SafeProcessorApplication</mainClass>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
			</plugin>
		</plugins>
	</build>
</project>
