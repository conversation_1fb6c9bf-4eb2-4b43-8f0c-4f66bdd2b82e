package com.shinet.core.safe.processor;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * Safe-Processor 数据处理服务启动类
 *
 * <AUTHOR>
 */
@Slf4j
@SpringBootApplication
@EnableKafka
@EnableTransactionManagement
@ComponentScan(basePackages = {
    "com.shinet.core.safe.processor",  // 当前模块包路径
    "com.shinet.core.safe.core"        // safe-core模块包路径，确保能扫描到LockDeviceAttributionRstBackupService等服务类
})
@MapperScan(basePackages = {"com.shinet.core.safe.core.mapper"})
@EnableApolloConfig(value = {
    "prometheus.management",
    "base.motan",
    "safe.datasource"
})
public class SafeProcessorApplication {

    public static void main(String[] args) {
        ConfigurableApplicationContext context = SpringApplication.run(SafeProcessorApplication.class, args);
        log.info("Safe-Processor 数据处理服务启动成功");
    }
}
