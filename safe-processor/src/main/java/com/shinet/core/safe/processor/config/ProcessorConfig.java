package com.shinet.core.safe.processor.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * Safe-Processor业务配置
 * 
 * 配置数据处理相关的业务参数：
 * 1. 数据备份配置
 * 2. 数据清理配置  
 * 3. 性能控制配置
 * 4. 异常处理配置
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "processor")
public class ProcessorConfig {

    /**
     * 数据备份配置
     */
    private BackupConfig backup = new BackupConfig();
    
    /**
     * 数据清理配置
     */
    private CleanConfig clean = new CleanConfig();
    
    /**
     * 性能控制配置
     */
    private PerformanceConfig performance = new PerformanceConfig();

    @Data
    public static class BackupConfig {
        /**
         * 备份开关
         */
        private boolean enabled = true;
        
        /**
         * 备份数据保留天数
         */
        private int retentionDays = 30;
        
        /**
         * 批量备份大小
         */
        private int batchSize = 100;
    }

    @Data
    public static class CleanConfig {
        /**
         * 清理开关
         */
        private boolean enabled = true;
        
        /**
         * 清理前是否强制备份
         */
        private boolean forceBackup = true;
        
        /**
         * 批量删除大小
         */
        private int batchSize = 100;
    }

    @Data
    public static class PerformanceConfig {
        /**
         * 处理超时时间（秒）
         */
        private int timeoutSeconds = 30;
        
        /**
         * 最大重试次数
         */
        private int maxRetries = 3;
        
        /**
         * 重试间隔（毫秒）
         */
        private long retryIntervalMs = 1000;
        
        /**
         * 是否启用性能监控
         */
        private boolean monitorEnabled = true;
    }
}
