package com.shinet.core.safe.processor.config;

/**
 * Safe-Processor常量定义
 * 
 * 包含：
 * 1. 日志标签常量
 * 2. 业务常量
 * 3. 数据库表名常量
 * 4. 状态码常量
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
public class ProcessorConstants {

    /**
     * 日志标签
     */
    public static class LogTag {
        /** Kafka消费日志标签 */
        public static final String KAFKA_CONSUMER = "[PROCESSOR-KAFKA]";
        
        /** 数据备份日志标签 */
        public static final String DATA_BACKUP = "[PROCESSOR-BACKUP]";
        
        /** 数据清理日志标签 */
        public static final String DATA_CLEAN = "[PROCESSOR-CLEAN]";
        
        /** 状态更新日志标签 */
        public static final String STATUS_UPDATE = "[PROCESSOR-STATUS]";
        
        /** 业务处理日志标签 */
        public static final String BUSINESS_PROCESS = "[PROCESSOR-BUSINESS]";
        
        /** 异常处理日志标签 */
        public static final String EXCEPTION_HANDLE = "[PROCESSOR-EXCEPTION]";
    }

    /**
     * 数据库表名
     */
    public static class TableName {
        /** Android设备锁定结果表 */
        public static final String ANDROID_LOCK_RST = "AndroidLockRst";
        
        /** iOS设备锁定结果表 */
        public static final String LOCK_IOS_RST = "LockIosRst";
        
        /** 设备归因备份表 */
        public static final String LOCK_DEVICE_ATTRIBUTION_RST_BACKUP = "LockDeviceAttributionRstBackup";
        
        /** 设备归因重试记录表 */
        public static final String LOCK_DEVICE_GY_RETRY_RECORD = "LockDeviceGyRetryRecord";
    }

    /**
     * 操作系统类型
     */
    public static class OsType {
        /** Android系统 */
        public static final String ANDROID = "android";
        
        /** iOS系统 */
        public static final String IOS = "ios";
    }

    /**
     * 处理状态
     */
    public static class ProcessStatus {
        /** 处理成功 */
        public static final String SUCCESS = "SUCCESS";
        
        /** 处理失败 */
        public static final String FAILED = "FAILED";
        
        /** 跳过处理 */
        public static final String SKIPPED = "SKIPPED";
    }

    /**
     * OCPC状态
     */
    public static class OcpcStatus {
        /** 非OCPC用户 */
        public static final int NON_OCPC = 0;
        
        /** OCPC用户 */
        public static final int OCPC = 1;
    }

    /**
     * 业务配置
     */
    public static class BusinessConfig {
        /** 默认批量处理大小 */
        public static final int DEFAULT_BATCH_SIZE = 100;
        
        /** 默认超时时间（秒） */
        public static final int DEFAULT_TIMEOUT_SECONDS = 30;
        
        /** 默认重试次数 */
        public static final int DEFAULT_MAX_RETRIES = 3;
        
        /** 默认重试间隔（毫秒） */
        public static final long DEFAULT_RETRY_INTERVAL_MS = 1000;
    }
}
