package com.shinet.core.safe.processor.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.transaction.annotation.TransactionManagementConfigurer;

import javax.sql.DataSource;

/**
 * 事务管理配置
 * 
 * 功能：
 * 1. 配置数据库事务管理器
 * 2. 确保业务流程的事务一致性
 * 3. 支持声明式事务管理
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@Configuration
@EnableTransactionManagement
public class TransactionConfig implements TransactionManagementConfigurer {

    /**
     * 配置事务管理器
     * 
     * @param dataSource 数据源
     * @return 事务管理器
     */
    @Bean
    public PlatformTransactionManager transactionManager(DataSource dataSource) {
        DataSourceTransactionManager transactionManager = new DataSourceTransactionManager(dataSource);
        
        // 设置事务超时时间（30秒）
        transactionManager.setDefaultTimeout(30);
        
        // 设置事务隔离级别为读已提交
        // transactionManager.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        
        log.info("数据库事务管理器配置完成: timeout=30s");
        
        return transactionManager;
    }

    @Override
    public PlatformTransactionManager annotationDrivenTransactionManager() {
        return transactionManager(null);
    }
}
