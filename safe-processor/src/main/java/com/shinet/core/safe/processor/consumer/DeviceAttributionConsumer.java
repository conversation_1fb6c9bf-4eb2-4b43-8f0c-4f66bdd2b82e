package com.shinet.core.safe.processor.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.shinet.core.safe.processor.service.DeviceDataProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;

/**
 * 设备归因Kafka消费者
 *
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@Component
public class DeviceAttributionConsumer {

    @Autowired
    private DeviceDataProcessService deviceDataProcessService;

    /**
     * 消费设备归因成功消息
     * 
     * @param message 消息内容（JSON格式）
     * @param partition 分区号
     * @param offset 偏移量
     * @param acknowledgment 手动确认
     */
    @KafkaListener(topics = "lock_retry_gy_data",
            groupId = "lock_retry_gy_group",
            containerFactory = "deviceAttributionListenerFactory")
    public void consumeDeviceAttribution(@Payload String message,
                                       @Header(KafkaHeaders.RECEIVED_PARTITION_ID) int partition,
                                       @Header(KafkaHeaders.OFFSET) long offset,
                                       Acknowledgment acknowledgment) {
        
        try {
            DeviceAttributionMessage attributionMessage = parseMessage(message);
            if (attributionMessage == null || !attributionMessage.isValid()) {
                log.warn("设备归因消息格式无效，跳过处理: message={}", message);
                acknowledgment.acknowledge();
                return;
            }
            
            // 调用业务服务处理完整流程
            deviceDataProcessService.processDeviceAttribution(attributionMessage);

            // 处理成功，提交offset
            acknowledgment.acknowledge();
            
        } catch (Exception e) {
            log.error("设备归因消费异常: partition={}, offset={}, message={}", 
                     partition, offset, message, e);
            // 暂时只打印日志，后续可增加重试
            acknowledgment.acknowledge();
        }
    }

    /**
     * 解析Kafka消息
     * 
     * @param message JSON格式的消息
     * @return 设备归因消息对象，解析失败返回null
     */
    private DeviceAttributionMessage parseMessage(String message) {
        if (message == null || message.trim().isEmpty()) {
            log.warn("消息内容为空");
            return null;
        }
        
        try {
            DeviceAttributionMessage attributionMessage = JSON.parseObject(message, DeviceAttributionMessage.class);
            
            if (attributionMessage == null) {
                log.warn("JSON解析结果为null: message={}", message);
                return null;
            }
            
            log.debug("消息解析成功: {}", attributionMessage);
            return attributionMessage;
            
        } catch (JSONException e) {
            log.error("JSON解析失败: message={}", message, e);
            return null;
        } catch (Exception e) {
            log.error("消息解析异常: message={}", message, e);
            return null;
        }
    }
}
