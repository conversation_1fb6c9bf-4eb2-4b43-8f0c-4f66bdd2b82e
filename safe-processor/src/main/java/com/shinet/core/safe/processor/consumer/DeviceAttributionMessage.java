package com.shinet.core.safe.processor.consumer;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * 设备归因消息实体
 * 
 * 消息格式：{product, os, deviceId}
 * 来源：归因服务发送的Kafka消息
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeviceAttributionMessage {

    /**
     * 产品标识
     * 示例：jj, ddgs2, yyddddss等
     */
    @NotBlank(message = "产品标识不能为空")
    private String product;

    /**
     * 操作系统
     * 取值：android, ios
     */
    @NotBlank(message = "操作系统不能为空")
    private String os;

    /**
     * 设备ID
     * Android: oaid
     * iOS: caid
     */
    @NotBlank(message = "设备ID不能为空")
    private String deviceId;

    /**
     * 创建设备归因消息
     * 
     * @param product 产品标识
     * @param os 操作系统
     * @param deviceId 设备ID
     * @return 设备归因消息实例
     */
    public static DeviceAttributionMessage create(String product, String os, String deviceId) {
        return new DeviceAttributionMessage(product, os, deviceId);
    }

    /**
     * 获取查询键（用于数据库查询）
     * 
     * @return 格式化的查询键
     */
    public String getQueryKey() {
        return String.format("product=%s, os=%s, deviceId=%s", product, os, deviceId);
    }

    /**
     * 验证消息完整性
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return StrUtil.isNotBlank(product) && StrUtil.isNotBlank(os) && StrUtil.isNotBlank(deviceId);
    }

    @Override
    public String toString() {
        return String.format("DeviceAttributionMessage{product='%s', os='%s', deviceId='%s'}", 
                           product, os, deviceId);
    }
}
