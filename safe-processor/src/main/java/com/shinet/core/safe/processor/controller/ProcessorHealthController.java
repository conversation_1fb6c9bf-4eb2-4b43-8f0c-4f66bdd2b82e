package com.shinet.core.safe.processor.controller;

import com.shinet.core.safe.processor.config.ProcessorConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 处理器健康检查控制器
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("/processor")
public class ProcessorHealthController {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 健康检查接口
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> health = new HashMap<>();
        
//        try {
//            // 检查服务状态
//            health.put("status", "UP");
//            health.put("service", "safe-processor");
//            health.put("timestamp", System.currentTimeMillis());
//
//            // 检查数据库连接
//            Map<String, Object> database = checkDatabase();
//            health.put("database", database);
//
//            log.debug("{}健康检查完成: status=UP", ProcessorConstants.LogTag.BUSINESS_PROCESS);
//
//        } catch (Exception e) {
//            health.put("status", "DOWN");
//            health.put("error", e.getMessage());
//
//            log.error("{}健康检查失败", ProcessorConstants.LogTag.BUSINESS_PROCESS, e);
//        }
        health.put("status", 200);
        return health;
    }

    /**
     * 服务信息接口
     * 
     * @return 服务信息
     */
    @GetMapping("/info")
    public Map<String, Object> info() {
        Map<String, Object> info = new HashMap<>();
        
        info.put("name", "Safe-Processor");
        info.put("description", "设备归因数据处理服务");
        info.put("version", "1.0.0");
        info.put("features", new String[]{
            "Kafka消息消费",
            "数据备份服务", 
            "数据清理服务",
            "状态更新服务",
            "事务管理"
        });
        
        return info;
    }

    /**
     * 检查数据库连接
     * 
     * @return 数据库状态
     */
    private Map<String, Object> checkDatabase() {
        Map<String, Object> database = new HashMap<>();
        
        try {
            // 执行简单查询测试连接
            Integer result = jdbcTemplate.queryForObject("SELECT 1", Integer.class);
            
            if (result != null && result == 1) {
                database.put("status", "UP");
                database.put("connection", "OK");
            } else {
                database.put("status", "DOWN");
                database.put("connection", "FAILED");
            }
            
        } catch (Exception e) {
            database.put("status", "DOWN");
            database.put("connection", "ERROR");
            database.put("error", e.getMessage());
            
            log.warn("{}数据库连接检查失败", ProcessorConstants.LogTag.BUSINESS_PROCESS, e);
        }
        
        return database;
    }
}
