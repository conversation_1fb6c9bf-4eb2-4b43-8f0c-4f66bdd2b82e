package com.shinet.core.safe.processor.exception;

import com.shinet.core.safe.processor.config.ProcessorConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理器
 *
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     * 
     * @param e 处理器异常
     * @return 异常响应
     */
    @ExceptionHandler(ProcessorException.class)
    public ResponseEntity<Map<String, Object>> handleProcessorException(ProcessorException e) {
        log.error("{}业务异常: errorCode={}, message={}", 
                 ProcessorConstants.LogTag.EXCEPTION_HANDLE, e.getErrorCode(), e.getErrorMessage(), e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorCode", e.getErrorCode());
        response.put("errorMessage", e.getErrorMessage());
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理数据备份异常
     * 
     * @param e 备份异常
     * @return 异常响应
     */
    @ExceptionHandler(ProcessorException.BackupException.class)
    public ResponseEntity<Map<String, Object>> handleBackupException(ProcessorException.BackupException e) {
        log.error("{}数据备份异常: {}", ProcessorConstants.LogTag.DATA_BACKUP, e.getErrorMessage(), e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorCode", e.getErrorCode());
        response.put("errorMessage", "数据备份失败: " + e.getErrorMessage());
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理数据清理异常
     * 
     * @param e 清理异常
     * @return 异常响应
     */
    @ExceptionHandler(ProcessorException.CleanException.class)
    public ResponseEntity<Map<String, Object>> handleCleanException(ProcessorException.CleanException e) {
        log.error("{}数据清理异常: {}", ProcessorConstants.LogTag.DATA_CLEAN, e.getErrorMessage(), e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorCode", e.getErrorCode());
        response.put("errorMessage", "数据清理失败: " + e.getErrorMessage());
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理消息解析异常
     * 
     * @param e 消息解析异常
     * @return 异常响应
     */
    @ExceptionHandler(ProcessorException.MessageParseException.class)
    public ResponseEntity<Map<String, Object>> handleMessageParseException(ProcessorException.MessageParseException e) {
        log.error("{}消息解析异常: {}", ProcessorConstants.LogTag.KAFKA_CONSUMER, e.getErrorMessage(), e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorCode", e.getErrorCode());
        response.put("errorMessage", "消息解析失败: " + e.getErrorMessage());
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
    }

    /**
     * 处理数据库异常
     * 
     * @param e 数据库异常
     * @return 异常响应
     */
    @ExceptionHandler(ProcessorException.DatabaseException.class)
    public ResponseEntity<Map<String, Object>> handleDatabaseException(ProcessorException.DatabaseException e) {
        log.error("{}数据库异常: {}", ProcessorConstants.LogTag.EXCEPTION_HANDLE, e.getErrorMessage(), e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorCode", e.getErrorCode());
        response.put("errorMessage", "数据库操作失败");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理通用异常
     * 
     * @param e 通用异常
     * @return 异常响应
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleGenericException(Exception e) {
        log.error("{}系统异常: {}", ProcessorConstants.LogTag.EXCEPTION_HANDLE, e.getMessage(), e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorCode", "SYSTEM_ERROR");
        response.put("errorMessage", "系统内部错误");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }

    /**
     * 处理运行时异常
     * 
     * @param e 运行时异常
     * @return 异常响应
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<Map<String, Object>> handleRuntimeException(RuntimeException e) {
        log.error("{}运行时异常: {}", ProcessorConstants.LogTag.EXCEPTION_HANDLE, e.getMessage(), e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorCode", "RUNTIME_ERROR");
        response.put("errorMessage", "运行时错误");
        response.put("timestamp", System.currentTimeMillis());
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}
