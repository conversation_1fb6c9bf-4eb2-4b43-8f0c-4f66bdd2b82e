package com.shinet.core.safe.processor.exception;

/**
 * 处理器业务异常
 * 
 * 用于封装业务处理过程中的异常情况
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
public class ProcessorException extends RuntimeException {

    private final String errorCode;
    private final String errorMessage;

    public ProcessorException(String errorCode, String errorMessage) {
        super(errorMessage);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public ProcessorException(String errorCode, String errorMessage, Throwable cause) {
        super(errorMessage, cause);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * 数据备份异常
     */
    public static class BackupException extends ProcessorException {
        public BackupException(String message) {
            super("BACKUP_ERROR", message);
        }

        public BackupException(String message, Throwable cause) {
            super("BACKUP_ERROR", message, cause);
        }
    }

    /**
     * 数据清理异常
     */
    public static class CleanException extends ProcessorException {
        public CleanException(String message) {
            super("CLEAN_ERROR", message);
        }

        public CleanException(String message, Throwable cause) {
            super("CLEAN_ERROR", message, cause);
        }
    }

    /**
     * 状态更新异常
     */
    public static class StatusUpdateException extends ProcessorException {
        public StatusUpdateException(String message) {
            super("STATUS_UPDATE_ERROR", message);
        }

        public StatusUpdateException(String message, Throwable cause) {
            super("STATUS_UPDATE_ERROR", message, cause);
        }
    }

    /**
     * 消息解析异常
     */
    public static class MessageParseException extends ProcessorException {
        public MessageParseException(String message) {
            super("MESSAGE_PARSE_ERROR", message);
        }

        public MessageParseException(String message, Throwable cause) {
            super("MESSAGE_PARSE_ERROR", message, cause);
        }
    }

    /**
     * 数据库操作异常
     */
    public static class DatabaseException extends ProcessorException {
        public DatabaseException(String message) {
            super("DATABASE_ERROR", message);
        }

        public DatabaseException(String message, Throwable cause) {
            super("DATABASE_ERROR", message, cause);
        }
    }
}
