package com.shinet.core.safe.processor.service;

import com.alibaba.fastjson.JSON;
import com.shinet.core.safe.core.service.LockDeviceAttributionRstBackupService;
import com.shinet.core.safe.processor.config.ProcessorConstants;
import com.shinet.core.safe.processor.consumer.DeviceAttributionMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

/**
 * 设备数据备份服务
 *
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@Service
public class DeviceDataBackupService {

    @Autowired
    private LockDeviceAttributionRstBackupService backupService;
    
    @Autowired
    private DeviceDataQueryService deviceDataQueryService;

    /**
     * 备份设备数据
     * 
     * @param message 设备归因消息
     * @return 备份结果
     */
    @Transactional(rollbackFor = Exception.class)
    public BackupResult backupDeviceData(DeviceAttributionMessage message) {
        try {
            // 根据操作系统类型查询对应表的最新记录
            Map<String, Object> latestRecord = queryLatestRecord(message);
            
            if (latestRecord == null || latestRecord.isEmpty()) {
                log.info("{}未找到设备记录，跳过备份: {}", 
                        ProcessorConstants.LogTag.DATA_BACKUP, message.getQueryKey());
                return BackupResult.success("未找到记录，跳过备份");
            }
            
            // 转换为JSON格式
            String originalData = JSON.toJSONString(latestRecord);
            String sourceTable = getSourceTableName(message.getOs());
            String sourceId = getSourceId(latestRecord, message.getOs());
            
            // 创建备份记录
            boolean success = backupService.createBackup(
                sourceTable, 
                sourceId, 
                message.getDeviceId(), 
                message.getProduct(), 
                message.getOs(), 
                originalData, 
                null // retryRecordId暂时为null，后续可以关联
            );


            
            if (!success) {
                log.error("{}设备数据备份失败: {}",
                        ProcessorConstants.LogTag.DATA_BACKUP, message.getQueryKey());
                return BackupResult.failure("备份失败");
            }
            
        } catch (Exception e) {
            log.error("{}设备数据备份异常: {}", 
                     ProcessorConstants.LogTag.DATA_BACKUP, message.getQueryKey(), e);
            return BackupResult.failure("备份异常: " + e.getMessage());
        }

        return BackupResult.success("备份成功");
    }

    /**
     * 查询最新记录
     */
    private Map<String, Object> queryLatestRecord(DeviceAttributionMessage message) {
        if (ProcessorConstants.OsType.ANDROID.equals(message.getOs())) {
            return deviceDataQueryService.queryLatestAndroidRecord(
                message.getProduct(), message.getDeviceId());
        } else if (ProcessorConstants.OsType.IOS.equals(message.getOs())) {
            return deviceDataQueryService.queryLatestIosRecord(
                message.getProduct(), message.getDeviceId());
        } else {
            log.warn("{}不支持的操作系统类型: os={}", 
                    ProcessorConstants.LogTag.DATA_BACKUP, message.getOs());
            return null;
        }
    }

    /**
     * 获取源表名
     */
    private String getSourceTableName(String os) {
        if (ProcessorConstants.OsType.ANDROID.equals(os)) {
            return ProcessorConstants.TableName.ANDROID_LOCK_RST;
        } else if (ProcessorConstants.OsType.IOS.equals(os)) {
            return ProcessorConstants.TableName.LOCK_IOS_RST;
        } else {
            throw new IllegalArgumentException("不支持的操作系统类型: " + os);
        }
    }

    /**
     * 获取源记录ID
     */
    private String getSourceId(Map<String, Object> record, String os) {
        if (ProcessorConstants.OsType.ANDROID.equals(os)) {
            Object id = record.get("id");
            return id != null ? id.toString() : null;
        } else if (ProcessorConstants.OsType.IOS.equals(os)) {
            Object id = record.get("id");
            return id != null ? id.toString() : null;
        } else {
            throw new IllegalArgumentException("不支持的操作系统类型: " + os);
        }
    }

    /**
     * 备份结果类
     */
    public static class BackupResult {
        private boolean success;
        private String message;
        private String sourceTable;
        private String sourceId;
        private String originalData;

        private BackupResult(boolean success, String message, String sourceTable, String sourceId, String originalData) {
            this.success = success;
            this.message = message;
            this.sourceTable = sourceTable;
            this.sourceId = sourceId;
            this.originalData = originalData;
        }

        public static BackupResult success(String message) {
            return new BackupResult(true, message, null, null, null);
        }

        public static BackupResult success(String message, String sourceTable, String sourceId, String originalData) {
            return new BackupResult(true, message, sourceTable, sourceId, originalData);
        }

        public static BackupResult failure(String message) {
            return new BackupResult(false, message, null, null, null);
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public String getSourceTable() { return sourceTable; }
        public String getSourceId() { return sourceId; }
        public String getOriginalData() { return originalData; }
    }
}
