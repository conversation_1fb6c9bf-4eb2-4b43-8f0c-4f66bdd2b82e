package com.shinet.core.safe.processor.service;

import com.shinet.core.safe.processor.config.ProcessorConstants;
import com.shinet.core.safe.processor.consumer.DeviceAttributionMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 设备数据清理服务
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@Service
public class DeviceDataCleanService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 清理设备数据
     *
     * @param message 设备归因消息
     * @return 清理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public CleanResult cleanDeviceData(DeviceAttributionMessage message) {
        int deletedCount = 0;

        if (ProcessorConstants.OsType.ANDROID.equals(message.getOs())) {
            deletedCount = deleteAndroidRecords(message.getProduct(), message.getDeviceId());
        } else if (ProcessorConstants.OsType.IOS.equals(message.getOs())) {
            deletedCount = deleteIosRecords(message.getProduct(), message.getDeviceId());
        }

        if (deletedCount <= 0) {
            log.info("{}未找到需要清理的设备记录: {}",
                    ProcessorConstants.LogTag.DATA_CLEAN, message.getQueryKey());
            return CleanResult.success("未找到记录，跳过清理", 0);
        }
        return CleanResult.success("成功", deletedCount);
    }

    /**
     * 删除Android设备记录
     *
     * @param product  产品标识
     * @param deviceId 设备ID (oaid)
     * @return 删除记录数
     */
    private int deleteAndroidRecords(String product, String deviceId) {
        log.debug("{}删除Android设备记录: product={}, deviceId={}",
                ProcessorConstants.LogTag.DATA_CLEAN, product, deviceId);

        try {
            String sql = "DELETE FROM android_lock_rst WHERE product = ? AND oaid = ?";
            int deletedCount = jdbcTemplate.update(sql, product, deviceId);

            log.debug("{}Android设备记录删除完成: 删除{}条记录, product={}, deviceId={}",
                    ProcessorConstants.LogTag.DATA_CLEAN, deletedCount, product, deviceId);

            return deletedCount;

        } catch (Exception e) {
            log.error("{}删除Android设备记录异常: product={}, deviceId={}",
                    ProcessorConstants.LogTag.DATA_CLEAN, product, deviceId, e);
            throw e;
        }
    }

    /**
     * 删除iOS设备记录
     *
     * @param product  产品标识
     * @param deviceId 设备ID (caid)
     * @return 删除记录数
     */
    private int deleteIosRecords(String product, String deviceId) {
        log.debug("{}删除iOS设备记录: product={}, deviceId={}",
                ProcessorConstants.LogTag.DATA_CLEAN, product, deviceId);

        try {
            String sql = "DELETE FROM lock_ios_rst WHERE product = ? AND caid = ?";
            int deletedCount = jdbcTemplate.update(sql, product, deviceId);

            log.debug("{}iOS设备记录删除完成: 删除{}条记录, product={}, deviceId={}",
                    ProcessorConstants.LogTag.DATA_CLEAN, deletedCount, product, deviceId);

            return deletedCount;

        } catch (Exception e) {
            log.error("{}删除iOS设备记录异常: product={}, deviceId={}",
                    ProcessorConstants.LogTag.DATA_CLEAN, product, deviceId, e);
            throw e;
        }
    }

    /**
     * 批量清理设备数据（预留接口）
     *
     * @param messages 设备归因消息列表
     * @return 清理结果
     */
    @Transactional(rollbackFor = Exception.class)
    public CleanResult batchCleanDeviceData(java.util.List<DeviceAttributionMessage> messages) {
        log.info("{}开始批量清理设备数据: 数量={}", ProcessorConstants.LogTag.DATA_CLEAN, messages.size());

        int totalDeleted = 0;
        int successCount = 0;

        for (DeviceAttributionMessage message : messages) {
            try {
                CleanResult result = cleanDeviceData(message);
                if (result.isSuccess()) {
                    totalDeleted += result.getDeletedCount();
                    successCount++;
                }
            } catch (Exception e) {
                log.error("{}批量清理单条记录失败: {}",
                        ProcessorConstants.LogTag.DATA_CLEAN, message.getQueryKey(), e);
            }
        }

        log.info("{}批量清理完成: 成功{}条, 总删除{}条记录",
                ProcessorConstants.LogTag.DATA_CLEAN, successCount, totalDeleted);

        return CleanResult.success("批量清理完成", totalDeleted);
    }

    /**
     * 清理结果类
     */
    public static class CleanResult {
        private boolean success;
        private String message;
        private int deletedCount;

        private CleanResult(boolean success, String message, int deletedCount) {
            this.success = success;
            this.message = message;
            this.deletedCount = deletedCount;
        }

        public static CleanResult success(String message, int deletedCount) {
            return new CleanResult(true, message, deletedCount);
        }

        public static CleanResult failure(String message) {
            return new CleanResult(false, message, 0);
        }

        // Getters
        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }

        public int getDeletedCount() {
            return deletedCount;
        }
    }
}
