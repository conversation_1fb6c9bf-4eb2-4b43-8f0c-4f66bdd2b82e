package com.shinet.core.safe.processor.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.shinet.core.safe.core.entity.LockDeviceGyRetryRecord;
import com.shinet.core.safe.core.service.DeviceGyRetryRecordService;
import com.shinet.core.safe.processor.config.ProcessorConstants;
import com.shinet.core.safe.processor.consumer.DeviceAttributionMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 设备数据处理主服务
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@Service
public class DeviceDataProcessService {

    @Autowired
    private DeviceDataBackupService backupService;

    @Autowired
    private DeviceDataCleanService cleanService;

    @Autowired
    private DeviceGyRetryRecordService retryRecordService;

    /**
     * 处理设备归因数据
     *
     * @param message 设备归因消息
     * @return 处理是否成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean processDeviceAttribution(DeviceAttributionMessage message) {
        log.info("{}开始处理设备归因: {}", ProcessorConstants.LogTag.BUSINESS_PROCESS, message.getQueryKey());

        // 步骤1：备份设备数据
        DeviceDataBackupService.BackupResult backupResult = backupService.backupDeviceData(message);
        if (!backupResult.isSuccess()) {
            log.error("{}设备数据备份失败: {}, 原因: {}",
                    ProcessorConstants.LogTag.BUSINESS_PROCESS, message.getQueryKey(), backupResult.getMessage());
        }

        // 步骤2：清理原表数据
        DeviceDataCleanService.CleanResult cleanResult = cleanService.cleanDeviceData(message);

        log.info("{}设备数据{}: 删除{}条记录, {}",
                ProcessorConstants.LogTag.BUSINESS_PROCESS, cleanResult.getMessage(), cleanResult.getDeletedCount(), message.getQueryKey());

        // 步骤3：更新重试记录状态
        boolean statusUpdateResult = updateRetryRecordStatus(message);
        if (!statusUpdateResult) {
            log.error("{}重试记录状态更新失败: {}",
                    ProcessorConstants.LogTag.BUSINESS_PROCESS, message.getQueryKey());
            throw new RuntimeException("重试记录状态更新失败: " + message.getQueryKey());
        }

        return true;
    }

    /**
     * 更新重试记录状态
     *
     * @param message 设备归因消息
     * @return 更新是否成功
     */
    private boolean updateRetryRecordStatus(DeviceAttributionMessage message) {
        log.debug("{}更新重试记录状态: {}", ProcessorConstants.LogTag.STATUS_UPDATE, message.getQueryKey());

        // 查询对应的重试记录
        LockDeviceGyRetryRecord retryRecord = retryRecordService.getByDeviceInfo(
                message.getDeviceId(), message.getProduct(), message.getOs());

        if (retryRecord == null) {
            log.warn("{}未找到对应的重试记录: {}",
                    ProcessorConstants.LogTag.STATUS_UPDATE, message.getQueryKey());
            return true;
        }

        // 更新is_ocpc状态为1
        LambdaUpdateWrapper<LockDeviceGyRetryRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(LockDeviceGyRetryRecord::getId, retryRecord.getId())
                .set(LockDeviceGyRetryRecord::getIsOcpc, ProcessorConstants.OcpcStatus.OCPC)
                .set(LockDeviceGyRetryRecord::getUpdateTime, new Date());

        return retryRecordService.update(updateWrapper);

    }

    /**
     * 批量处理设备归因数据（预留接口）
     *
     * @param messages 设备归因消息列表
     * @return 处理结果统计
     */
    @Transactional(rollbackFor = Exception.class)
    public ProcessResult batchProcessDeviceAttribution(java.util.List<DeviceAttributionMessage> messages) {
        log.info("{}开始批量处理设备归因: 数量={}", ProcessorConstants.LogTag.BUSINESS_PROCESS, messages.size());

        int successCount = 0;
        int failureCount = 0;

        for (DeviceAttributionMessage message : messages) {
            try {
                boolean success = processDeviceAttribution(message);
                if (success) {
                    successCount++;
                } else {
                    failureCount++;
                }
            } catch (Exception e) {
                log.error("{}批量处理单条记录失败: {}",
                        ProcessorConstants.LogTag.BUSINESS_PROCESS, message.getQueryKey(), e);
                failureCount++;
            }
        }

        log.info("{}批量处理完成: 成功{}条, 失败{}条",
                ProcessorConstants.LogTag.BUSINESS_PROCESS, successCount, failureCount);

        return new ProcessResult(successCount, failureCount);
    }

    /**
     * 处理结果统计类
     */
    public static class ProcessResult {
        private int successCount;
        private int failureCount;

        public ProcessResult(int successCount, int failureCount) {
            this.successCount = successCount;
            this.failureCount = failureCount;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public int getFailureCount() {
            return failureCount;
        }

        public int getTotalCount() {
            return successCount + failureCount;
        }

        public double getSuccessRate() {
            return getTotalCount() > 0 ? (double) successCount / getTotalCount() : 0.0;
        }
    }
}
