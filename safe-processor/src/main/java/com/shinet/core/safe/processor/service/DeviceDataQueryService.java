package com.shinet.core.safe.processor.service;

import com.shinet.core.safe.processor.config.ProcessorConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 设备数据查询服务
 *
 * 
 * 注意：由于safe-processor只依赖safe-core，不依赖safe-biz，
 * 所以暂使用JdbcTemplate直接查询数据库，后续将mapper迁入core模块
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
@Service
public class DeviceDataQueryService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 查询Android设备最新记录
     * 
     * @param product 产品标识
     * @param deviceId 设备ID (oaid)
     * @return 最新记录，不存在返回null
     */
    public Map<String, Object> queryLatestAndroidRecord(String product, String deviceId) {
        log.debug("{}查询Android设备最新记录: product={}, deviceId={}", 
                 ProcessorConstants.LogTag.DATA_BACKUP, product, deviceId);
        
        try {
            String sql = "SELECT * FROM android_lock_rst WHERE product = ? AND oaid = ? " +
                        "ORDER BY update_time DESC LIMIT 1";
            
            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, product, deviceId);
            
            if (results.isEmpty()) {
                log.debug("{}未找到Android设备记录: product={}, deviceId={}", 
                         ProcessorConstants.LogTag.DATA_BACKUP, product, deviceId);
                return null;
            }
            
            Map<String, Object> record = results.get(0);
            log.debug("{}查询到Android设备记录: id={}, product={}, deviceId={}", 
                     ProcessorConstants.LogTag.DATA_BACKUP, record.get("id"), product, deviceId);
            
            return record;
            
        } catch (Exception e) {
            log.error("{}查询Android设备记录异常: product={}, deviceId={}", 
                     ProcessorConstants.LogTag.DATA_BACKUP, product, deviceId, e);
            return null;
        }
    }

    /**
     * 查询iOS设备最新记录
     * 
     * @param product 产品标识
     * @param deviceId 设备ID (caid)
     * @return 最新记录，不存在返回null
     */
    public Map<String, Object> queryLatestIosRecord(String product, String deviceId) {
        log.debug("{}查询iOS设备最新记录: product={}, deviceId={}", 
                 ProcessorConstants.LogTag.DATA_BACKUP, product, deviceId);
        
        try {
            String sql = "SELECT * FROM lock_ios_rst WHERE product = ? AND caid = ? " +
                        "ORDER BY update_time DESC LIMIT 1";
            
            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, product, deviceId);
            
            if (results.isEmpty()) {
                log.debug("{}未找到iOS设备记录: product={}, deviceId={}", 
                         ProcessorConstants.LogTag.DATA_BACKUP, product, deviceId);
                return null;
            }
            
            Map<String, Object> record = results.get(0);
            log.debug("{}查询到iOS设备记录: id={}, product={}, deviceId={}", 
                     ProcessorConstants.LogTag.DATA_BACKUP, record.get("id"), product, deviceId);
            
            return record;
            
        } catch (Exception e) {
            log.error("{}查询iOS设备记录异常: product={}, deviceId={}", 
                     ProcessorConstants.LogTag.DATA_BACKUP, product, deviceId, e);
            return null;
        }
    }

    /**
     * 统计Android设备记录数量
     * 
     * @param product 产品标识
     * @param deviceId 设备ID
     * @return 记录数量
     */
    public int countAndroidRecords(String product, String deviceId) {
        try {
            String sql = "SELECT COUNT(*) FROM AndroidLockRst WHERE product = ? AND oaid = ?";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, product, deviceId);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("{}统计Android设备记录异常: product={}, deviceId={}", 
                     ProcessorConstants.LogTag.DATA_BACKUP, product, deviceId, e);
            return 0;
        }
    }

    /**
     * 统计iOS设备记录数量
     * 
     * @param product 产品标识
     * @param deviceId 设备ID
     * @return 记录数量
     */
    public int countIosRecords(String product, String deviceId) {
        try {
            String sql = "SELECT COUNT(*) FROM LockIosRst WHERE product = ? AND caid = ?";
            Integer count = jdbcTemplate.queryForObject(sql, Integer.class, product, deviceId);
            return count != null ? count : 0;
        } catch (Exception e) {
            log.error("{}统计iOS设备记录异常: product={}, deviceId={}", 
                     ProcessorConstants.LogTag.DATA_BACKUP, product, deviceId, e);
            return 0;
        }
    }

    /**
     * 检查设备记录是否存在
     * 
     * @param product 产品标识
     * @param deviceId 设备ID
     * @param os 操作系统
     * @return 是否存在记录
     */
    public boolean existsDeviceRecord(String product, String deviceId, String os) {
        if (ProcessorConstants.OsType.ANDROID.equals(os)) {
            return countAndroidRecords(product, deviceId) > 0;
        } else if (ProcessorConstants.OsType.IOS.equals(os)) {
            return countIosRecords(product, deviceId) > 0;
        } else {
            log.warn("{}不支持的操作系统类型: os={}", 
                    ProcessorConstants.LogTag.DATA_BACKUP, os);
            return false;
        }
    }
}
