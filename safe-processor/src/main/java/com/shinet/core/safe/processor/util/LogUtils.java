package com.shinet.core.safe.processor.util;

import com.shinet.core.safe.processor.config.ProcessorConstants;
import com.shinet.core.safe.processor.consumer.DeviceAttributionMessage;
import lombok.extern.slf4j.Slf4j;

/**
 * 日志工具类
 * 
 * 功能：
 * 1. 统一日志格式
 * 2. 标准化日志输出
 * 3. 敏感信息脱敏
 * 4. 性能日志记录
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Slf4j
public class LogUtils {

    /**
     * 记录Kafka消费日志
     * 
     * @param message 设备归因消息
     * @param action 操作类型
     * @param result 操作结果
     */
    public static void logKafkaConsume(DeviceAttributionMessage message, String action, String result) {
        log.info("{}Kafka消费 | 操作: {} | 结果: {} | 消息: {}", 
                ProcessorConstants.LogTag.KAFKA_CONSUMER, action, result, 
                message != null ? message.getQueryKey() : "null");
    }

    /**
     * 记录数据备份日志
     * 
     * @param message 设备归因消息
     * @param action 操作类型
     * @param result 操作结果
     * @param details 详细信息
     */
    public static void logDataBackup(DeviceAttributionMessage message, String action, String result, String details) {
        log.info("{}数据备份 | 操作: {} | 结果: {} | 详情: {} | 消息: {}", 
                ProcessorConstants.LogTag.DATA_BACKUP, action, result, details,
                message != null ? message.getQueryKey() : "null");
    }

    /**
     * 记录数据清理日志
     * 
     * @param message 设备归因消息
     * @param action 操作类型
     * @param result 操作结果
     * @param deletedCount 删除记录数
     */
    public static void logDataClean(DeviceAttributionMessage message, String action, String result, int deletedCount) {
        log.info("{}数据清理 | 操作: {} | 结果: {} | 删除数量: {} | 消息: {}", 
                ProcessorConstants.LogTag.DATA_CLEAN, action, result, deletedCount,
                message != null ? message.getQueryKey() : "null");
    }

    /**
     * 记录状态更新日志
     * 
     * @param message 设备归因消息
     * @param action 操作类型
     * @param result 操作结果
     * @param recordId 记录ID
     */
    public static void logStatusUpdate(DeviceAttributionMessage message, String action, String result, Long recordId) {
        log.info("{}状态更新 | 操作: {} | 结果: {} | 记录ID: {} | 消息: {}", 
                ProcessorConstants.LogTag.STATUS_UPDATE, action, result, recordId,
                message != null ? message.getQueryKey() : "null");
    }

    /**
     * 记录业务处理日志
     * 
     * @param message 设备归因消息
     * @param action 操作类型
     * @param result 操作结果
     * @param duration 处理耗时（毫秒）
     */
    public static void logBusinessProcess(DeviceAttributionMessage message, String action, String result, long duration) {
        log.info("{}业务处理 | 操作: {} | 结果: {} | 耗时: {}ms | 消息: {}", 
                ProcessorConstants.LogTag.BUSINESS_PROCESS, action, result, duration,
                message != null ? message.getQueryKey() : "null");
    }

    /**
     * 记录异常日志
     * 
     * @param message 设备归因消息
     * @param action 操作类型
     * @param exception 异常信息
     */
    public static void logException(DeviceAttributionMessage message, String action, Exception exception) {
        log.error("{}异常处理 | 操作: {} | 异常: {} | 消息: {}", 
                ProcessorConstants.LogTag.EXCEPTION_HANDLE, action, exception.getMessage(),
                message != null ? message.getQueryKey() : "null", exception);
    }

    /**
     * 记录性能日志
     * 
     * @param operation 操作名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param recordCount 处理记录数
     */
    public static void logPerformance(String operation, long startTime, long endTime, int recordCount) {
        long duration = endTime - startTime;
        double avgTime = recordCount > 0 ? (double) duration / recordCount : 0;
        
        log.info("{}性能统计 | 操作: {} | 总耗时: {}ms | 记录数: {} | 平均耗时: {:.2f}ms", 
                ProcessorConstants.LogTag.BUSINESS_PROCESS, operation, duration, recordCount, avgTime);
    }

    /**
     * 脱敏设备ID
     * 
     * @param deviceId 原始设备ID
     * @return 脱敏后的设备ID
     */
    public static String maskDeviceId(String deviceId) {
        if (deviceId == null || deviceId.length() <= 6) {
            return deviceId;
        }
        
        int prefixLength = 3;
        int suffixLength = 3;
        int maskLength = deviceId.length() - prefixLength - suffixLength;
        
        StringBuilder masked = new StringBuilder();
        masked.append(deviceId.substring(0, prefixLength));
        for (int i = 0; i < maskLength; i++) {
            masked.append("*");
        }
        masked.append(deviceId.substring(deviceId.length() - suffixLength));
        
        return masked.toString();
    }

    /**
     * 格式化处理结果
     * 
     * @param success 是否成功
     * @return 格式化的结果字符串
     */
    public static String formatResult(boolean success) {
        return success ? "SUCCESS" : "FAILED";
    }

    /**
     * 格式化处理结果（带详情）
     * 
     * @param success 是否成功
     * @param details 详细信息
     * @return 格式化的结果字符串
     */
    public static String formatResult(boolean success, String details) {
        String result = formatResult(success);
        return details != null && !details.isEmpty() ? result + "(" + details + ")" : result;
    }

    /**
     * 记录启动日志
     * 
     * @param component 组件名称
     * @param config 配置信息
     */
    public static void logStartup(String component, String config) {
        log.info("{}组件启动 | 组件: {} | 配置: {}", 
                ProcessorConstants.LogTag.BUSINESS_PROCESS, component, config);
    }

    /**
     * 记录关闭日志
     * 
     * @param component 组件名称
     * @param reason 关闭原因
     */
    public static void logShutdown(String component, String reason) {
        log.info("{}组件关闭 | 组件: {} | 原因: {}", 
                ProcessorConstants.LogTag.BUSINESS_PROCESS, component, reason);
    }
}
