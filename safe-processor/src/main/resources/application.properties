server.port=8300
spring.application.name=safe-processor

mybatis-plus.typeAliasesPackage=com.shinet.core.safe.entity
mybatis-plus.mapper-locations=classpath*:mapper/*.xml
mybatis-plus.configuration.log-impl = org.apache.ibatis.logging.stdout.StdOutImpl


app.logging.path=logs/safe-processor

# Apollo
apollo.bootstrap.enabled=true
apollo.bootstrap.eagerLoad.enabled=true
apollo.meta=http://172.16.42.140:8288,http://172.16.42.141:8288
app.id=core-safe

# Kafka
spring.kafka.bootstrap-servers=alikafka-pre-cn-tl32lya4k00m-1-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-tl32lya4k00m-2-vpc.alikafka.aliyuncs.com:9092,alikafka-pre-cn-tl32lya4k00m-3-vpc.alikafka.aliyuncs.com:9092

# Redis
spring.redis.cluster.nodes=user-even-redis001.shinet-inc.com:9720,user-even-redis002.shinet-inc.com:9720,user-even-redis003.shinet-inc.com:9720
spring.redis.lettuce.pool.max-active = 100
spring.redis.lettuce.pool.max-wait = -1
spring.redis.lettuce.pool.max-idle = 8
spring.redis.lettuce.pool.min-idle = 0
spring.redis.timeout = 2000

spring.redis2.cluster.nodes=safe-data-redis001.shinet-inc.com:9720,safe-data-redis002.shinet-inc.com:9720,safe-data-redis003.shinet-inc.com:9720
spring.redis2.lettuce.pool.max-active = 100
spring.redis2.lettuce.pool.max-wait = -1
spring.redis2.lettuce.pool.max-idle = 8
spring.redis2.lettuce.pool.min-idle = 0
spring.redis2.timeout = 2000

management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
