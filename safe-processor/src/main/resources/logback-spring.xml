<?xml version="1.0" encoding="UTF-8"?>
<configuration  scan="true" scanPeriod="10 seconds">
    <include resource="com/shinet/core/base/logback-spring.xml" />
    <root level="info">
        <appender-ref ref="INFO_FILE" />
        <appender-ref ref="stdout" />
        <appender-ref ref="aliyun" />
        <appender-ref ref="ERROR_FILE" />
    </root>
    <logger name="com.coohua" level="INFO">
    </logger>
    <logger name="org.springframework" level="INFO">
    </logger>

</configuration>
