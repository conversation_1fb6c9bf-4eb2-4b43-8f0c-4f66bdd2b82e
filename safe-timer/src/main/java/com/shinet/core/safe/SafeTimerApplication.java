package com.shinet.core.safe;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@SpringBootApplication
@EnableAspectJAutoProxy
@MapperScan(basePackages = {"com.shinet.core.safe.msql.mapper", "com.shinet.core.safe.core.mapper"})
@EnableApolloConfig(value = {
		"prometheus.management","base.motan","safe.datasource","bp.retrieve.rpc.refer","ad.user.ad.api.referer","bp.user.rpc.referer","bp.account.rpc.referer"
})
@Slf4j
public class SafeTimerApplication {

	public static void main(String[] args) {
		try {
			ConfigurableApplicationContext configurableApplicationContext = SpringApplication.run(SafeTimerApplication.class, args);

//			ToSignOutUserTimer toSignOutUserTimer = configurableApplicationContext.getBean(ToSignOutUserTimer.class);
//			toSignOutUserTimer.starSignoutuser(null);
		}catch (Exception e){
			e.printStackTrace();
			log.error("",e);
		}
	}

}
