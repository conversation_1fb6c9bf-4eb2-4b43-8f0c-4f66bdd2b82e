package com.shinet.core.safe.dto;

import lombok.Data;

import java.util.List;

/**
 * 设备归因API请求DTO
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Data
public class DeviceAttributionRequest {

    /**
     * 设备信息列表
     */
    private List<DeviceInfo> gyRetryDeviceList;

    /**
     * 设备信息
     */
    @Data
    public static class DeviceInfo {
        
        /**
         * 产品标识
         */
        private String product;
        
        /**
         * 操作系统：android/ios
         */
        private String os;
        
        /**
         * 设备ID：Android为oaid，iOS为caid
         */
        private String deviceId;
        
        public DeviceInfo(String product, String os, String deviceId) {
            this.product = product;
            this.os = os;
            this.deviceId = deviceId;
        }
    }
}
