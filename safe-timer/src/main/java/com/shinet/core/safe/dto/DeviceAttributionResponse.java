package com.shinet.core.safe.dto;

import lombok.Data;

/**
 * 设备归因API响应DTO
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Data
public class DeviceAttributionResponse {

    /**
     * 状态码：200表示成功
     */
    private Integer status;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return status != null && status == 200;
    }
}
