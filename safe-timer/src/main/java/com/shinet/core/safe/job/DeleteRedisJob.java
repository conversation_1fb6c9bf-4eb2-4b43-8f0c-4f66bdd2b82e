package com.shinet.core.safe.job;

import com.shinet.core.safe.core.constants.DeviceRetryConstants;
import com.shinet.core.safe.util.RedisUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class DeleteRedisJob {

//    @Autowired
//    private RedisUtils redisUtils;
//
//    @XxlJob("deleteRedisKeyJob")
//    public ReturnT<?> executeDeviceAttributionRetry(String param) {
//
//        try {
//            if (param == null || param.trim().isEmpty()) return ReturnT.SUCCESS;
//            long deletedCount = redisUtils.deleteKeysByPrefix(param);
//            XxlJobLogger.log("删除" + deletedCount + "条");
//            return ReturnT.SUCCESS;
//        } catch (Exception e) {
//            XxlJobLogger.log("{}deleteRedisKeyJob任务执行异常", DeviceRetryConstants.LogTag.TIMER_TASK, e);
//            return ReturnT.FAIL;
//        }
//    }

    public void init(){
        log.info("DeleteRedisJob INIT...");
    }

    public void destroy(){
        log.info("DeleteRedisJob DESTROY...");
    }
}
