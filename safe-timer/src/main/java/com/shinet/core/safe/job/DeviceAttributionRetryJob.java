package com.shinet.core.safe.job;

import com.shinet.core.safe.core.constants.DeviceRetryConstants;
import com.shinet.core.safe.service.DeviceAttributionRetryService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 设备归因重试定时任务Job
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@Component
@Slf4j
public class DeviceAttributionRetryJob {

    @Autowired
    private DeviceAttributionRetryService deviceAttributionRetryService;

    /**
     * 设备归因重试定时任务
     * 每小时执行一次，处理待重试的设备记录
     */
    @XxlJob("deviceAttributionRetry")
    public ReturnT<?> executeDeviceAttributionRetry(String param) {
        log.info("{}设备归因重试定时任务开始执行", DeviceRetryConstants.LogTag.TIMER_TASK);

        try {
            // 调用业务服务处理
            boolean success = deviceAttributionRetryService.processDeviceAttributionRetry();

            if (success) {
                XxlJobLogger.log("{}设备归因重试定时任务执行成功", DeviceRetryConstants.LogTag.TIMER_TASK);
                return ReturnT.SUCCESS;
            } else {
                XxlJobLogger.log("{}设备归因重试定时任务执行失败", DeviceRetryConstants.LogTag.TIMER_TASK);
                return ReturnT.FAIL;
            }

        } catch (Exception e) {
            XxlJobLogger.log("{}设备归因重试定时任务执行异常", DeviceRetryConstants.LogTag.TIMER_TASK, e);
            return ReturnT.FAIL;
        }
    }

    public void init(){
        log.info("DeviceAttributionRetryJob INIT...");
    }

    public void destroy(){
        log.info("DeviceAttributionRetryJob DESTROY...");
    }

}
