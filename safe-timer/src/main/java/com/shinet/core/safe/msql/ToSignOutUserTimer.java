package com.shinet.core.safe.msql;

import com.google.common.collect.Lists;
import com.shinet.core.safe.msql.service.SignOutUserService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class ToSignOutUserTimer {
    @Autowired
    SignOutUserService signOutUserService;
    @XxlJob("safe-signoutuser")
    public ReturnT<?> starSignoutuser(String param){
//        List<String> tagList = new ArrayList<>();
//        if(StringUtils.isNotBlank(param)){
//            tagList = Lists.newArrayList(param.split(","));
//        }
//        signOutUserService.startCleanUser(tagList);
        return ReturnT.SUCCESS;
    }
}
