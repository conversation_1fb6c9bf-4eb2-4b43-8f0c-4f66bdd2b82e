package com.shinet.core.safe.service;

import com.alibaba.fastjson.JSON;
import com.shinet.core.safe.core.constants.DeviceRetryConstants;
import com.shinet.core.safe.core.entity.LockDeviceGyRetryRecord;
import com.shinet.core.safe.dto.DeviceAttributionRequest;
import com.shinet.core.safe.dto.DeviceAttributionResponse;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 设备归因API调用服务
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Service
@Slf4j
public class DeviceAttributionApiService {

    @Value("${device.attribution.api.url:xxx}")
    private String apiUrl;

    private final RestTemplate restTemplate;

    public DeviceAttributionApiService() {
        this.restTemplate = new RestTemplate();
    }

    /**
     * 批量调用设备归因API
     * 
     * @param records 设备记录列表
     * @return 是否调用成功
     */
    public boolean batchCallAttribution(List<LockDeviceGyRetryRecord> records) {
        if (records == null || records.isEmpty()) {
            XxlJobLogger.log("{}设备记录列表为空，跳过API调用", DeviceRetryConstants.LogTag.API_CALL);
            return true;
        }

        try {
            // 构建请求参数
            DeviceAttributionRequest request = buildRequest(records);
            
            // 调用API
            DeviceAttributionResponse response = callApi(request);
            
            if (response != null && response.isSuccess()) {
                XxlJobLogger.log("{}设备归因API调用成功: 设备数量={}",
                        DeviceRetryConstants.LogTag.API_CALL, records.size());
                return true;
            } else {
                XxlJobLogger.log("{}设备归因API调用失败: response={}",
                         DeviceRetryConstants.LogTag.API_CALL, JSON.toJSONString(response));
                return false;
            }
            
        } catch (Exception e) {
            log.error("{}设备归因API调用异常: 设备数量={}", 
                     DeviceRetryConstants.LogTag.API_CALL, records.size(), e);
            return false;
        }
    }

    /**
     * 构建API请求参数
     */
    private DeviceAttributionRequest buildRequest(List<LockDeviceGyRetryRecord> records) {
        List<DeviceAttributionRequest.DeviceInfo> devices = records.stream()
                .map(record -> new DeviceAttributionRequest.DeviceInfo(
                        record.getProduct(), 
                        record.getOs(), 
                        record.getDeviceId()))
                .collect(Collectors.toList());
        
        DeviceAttributionRequest request = new DeviceAttributionRequest();
        request.setGyRetryDeviceList(devices);
        return request;
    }

    /**
     * 调用设备归因API
     */
    private DeviceAttributionResponse callApi(DeviceAttributionRequest request) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<DeviceAttributionRequest> entity = new HttpEntity<>(request, headers);
            
            ResponseEntity<DeviceAttributionResponse> response = restTemplate.exchange(
                    apiUrl, 
                    HttpMethod.POST, 
                    entity, 
                    DeviceAttributionResponse.class);
            
            return response.getBody();
            
        } catch (Exception e) {
            XxlJobLogger.log("{}HTTP请求异常: url={}", DeviceRetryConstants.LogTag.API_CALL, apiUrl, e);
            throw e;
        }
    }
}
