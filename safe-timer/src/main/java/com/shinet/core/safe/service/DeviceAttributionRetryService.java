package com.shinet.core.safe.service;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.shinet.core.safe.core.constants.DeviceRetryConstants;
import com.shinet.core.safe.core.entity.LockDeviceGyRetryRecord;
import com.shinet.core.safe.core.service.DeviceGyRetryRecordService;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 设备归因重试业务服务
 * 
 * <AUTHOR>
 * @since 2025-01-17
 */
@Service
@Slf4j
public class DeviceAttributionRetryService {

    @Autowired
    private DeviceGyRetryRecordService deviceGyRetryRecordService;
    
    @Autowired
    private DeviceAttributionApiService deviceAttributionApiService;

    /**
     * 处理设备归因重试
     * 每次Job执行时，分页查询一小时内创建的所有is_ocpc=0记录进行处理
     *
     * @return 是否处理成功
     */
    public boolean processDeviceAttributionRetry() {
        try {
            int totalProcessed = 0;
            int totalSuccess = 0;
            int offset = 0;


            // 分页处理一小时内的所有记录
            while (true) {
                // 分页查询一小时内创建的is_ocpc=0且未达到最大重试次数的记录
                List<LockDeviceGyRetryRecord> currentBatch = deviceGyRetryRecordService
                        .getRecentRecordsByPage(offset, DeviceRetryConstants.BATCH_SIZE, DeviceRetryConstants.TASK_QUERY_HOURS);

                if (currentBatch.isEmpty()) {
                    XxlJobLogger.log("{}分页查询完成，没有更多记录，任务结束", DeviceRetryConstants.LogTag.TIMER_TASK);
                    break;
                }

                XxlJobLogger.log("{}查询到第{}页记录: {}条",
                        DeviceRetryConstants.LogTag.TIMER_TASK, (offset / DeviceRetryConstants.BATCH_SIZE) + 1, currentBatch.size());

                // 处理当前批次
                int batchSuccess = processBatch(currentBatch);

                totalProcessed += currentBatch.size();
                totalSuccess += batchSuccess;

                offset += DeviceRetryConstants.BATCH_SIZE;

                if (currentBatch.size() < DeviceRetryConstants.BATCH_SIZE) {
                    break;
                }
            }

            XxlJobLogger.log("{}设备归因重试任务执行完成: 总处理={}条, 成功={}条",
                    DeviceRetryConstants.LogTag.TIMER_TASK, totalProcessed, totalSuccess);

            return true;

        } catch (Exception e) {
            XxlJobLogger.log("{}设备归因重试处理异常", DeviceRetryConstants.LogTag.TIMER_TASK, e);
            return false;
        }
    }

    /**
     * 处理一批记录
     */
    private int processBatch(List<LockDeviceGyRetryRecord> records) {
        int successCount = 0;
        
        // 按API批量大小分组处理
        for (int i = 0; i < records.size(); i += DeviceRetryConstants.API_BATCH_SIZE) {
            int endIndex = Math.min(i + DeviceRetryConstants.API_BATCH_SIZE, records.size());
            List<LockDeviceGyRetryRecord> apiBatch = records.subList(i, endIndex);
            
            boolean apiSuccess = processApiBatch(apiBatch);
            if (apiSuccess) {
                successCount += apiBatch.size();
            }
        }
        
        return successCount;
    }

    /**
     * 处理API批次
     */
    private boolean processApiBatch(List<LockDeviceGyRetryRecord> apiBatch) {
        try {
            // 调用设备归因API
            boolean apiSuccess = deviceAttributionApiService.batchCallAttribution(apiBatch);
            
            // 更新记录状态
            updateRecordStatus(apiBatch, apiSuccess);
            
            return apiSuccess;
            
        } catch (Exception e) {
            XxlJobLogger.log("{}API批次处理异常: 批次大小={}",
                     DeviceRetryConstants.LogTag.TIMER_TASK, apiBatch.size(), e);
            
            updateRecordStatus(apiBatch, false);
            return false;
        }
    }

    /**
     * 更新记录状态
     */
    private void updateRecordStatus(List<LockDeviceGyRetryRecord> records, boolean success) {
        for (LockDeviceGyRetryRecord record : records) {
            record.setRetryCount(record.getRetryCount() + 1);
            record.setUpdateTime(new Date());
        }

        deviceGyRetryRecordService.updateBatchById(records);
    }
}
