server.port=8299
spring.application.name = safe-timer

xxl.job.admin.addresses = http://*************:8080/xxl-job-admin
xxl.job.executor.appname = safe-timer
xxl.job.executor.logpath = /data/logs/xxl-job/safe-timer
xxl.job.executor.logretentiondays = 3
xxl.job.executor.port = -1

# Apollo
apollo.bootstrap.enabled=true
apollo.bootstrap.eagerLoad.enabled=true
apollo.meta=http://*************:8288,http://*************:8288
app.id=core-safe

mybatis-plus.typeAliasesPackage = com.shinet.core.safe.mapper
mybatis-plus.configuration.log-impl = org.apache.ibatis.logging.stdout.StdOutImpl


app.logging.path = logs/safe-timer

# Device Attribution API Configuration
device.attribution.api.url = http://ocpc-api.shinet-inc.com/dispense/user/event/retryLockGuiy

spring.redis.cluster.nodes=user-even-redis001.shinet-inc.com:9720,user-even-redis002.shinet-inc.com:9720,user-even-redis003.shinet-inc.com:9720
spring.redis.lettuce.pool.max-active = 100
spring.redis.lettuce.pool.max-wait = -1
spring.redis.lettuce.pool.max-idle = 8
spring.redis.lettuce.pool.min-idle = 0
spring.redis.timeout = 2000

spring.redis2.cluster.nodes=safe-data-redis001.shinet-inc.com:9720,safe-data-redis002.shinet-inc.com:9720,safe-data-redis003.shinet-inc.com:9720
spring.redis2.lettuce.pool.max-active = 100
spring.redis2.lettuce.pool.max-wait = -1
spring.redis2.lettuce.pool.max-idle = 8
spring.redis2.lettuce.pool.min-idle = 0
spring.redis2.timeout = 2000